/**
 * Build script for Electron application
 * Handles frontend build, backend packaging, and Electron distribution
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const projectRoot = path.join(__dirname, '..');
const frontendDir = path.join(projectRoot, 'frontend');
const electronDir = path.join(projectRoot, 'electron');
const backendDir = path.join(projectRoot, 'backend');

console.log('🚀 Building TikTok Automation Desktop Application...\n');

/**
 * Execute command with error handling
 */
function execCommand(command, cwd = projectRoot) {
    console.log(`📦 Executing: ${command}`);
    try {
        execSync(command, { 
            cwd, 
            stdio: 'inherit',
            env: { ...process.env, NODE_ENV: 'production' }
        });
        console.log('✅ Command completed successfully\n');
    } catch (error) {
        console.error(`❌ Command failed: ${error.message}`);
        process.exit(1);
    }
}

/**
 * Check if directory exists
 */
function checkDirectory(dir, name) {
    if (!fs.existsSync(dir)) {
        console.error(`❌ ${name} directory not found: ${dir}`);
        process.exit(1);
    }
    console.log(`✅ ${name} directory found: ${dir}`);
}

/**
 * Main build process
 */
async function build() {
    console.log('🔍 Checking project structure...');
    
    // Check required directories
    checkDirectory(frontendDir, 'Frontend');
    checkDirectory(electronDir, 'Electron');
    checkDirectory(backendDir, 'Backend');
    
    console.log('\n📦 Installing dependencies...');
    
    // Install frontend dependencies
    console.log('Installing frontend dependencies...');
    execCommand('npm install', frontendDir);
    
    // Install electron dependencies
    console.log('Installing electron dependencies...');
    execCommand('npm install', electronDir);
    
    console.log('\n🏗️  Building frontend...');
    
    // Build React frontend
    execCommand('npm run build', frontendDir);
    
    // Verify frontend build
    const frontendBuildDir = path.join(frontendDir, 'build');
    if (!fs.existsSync(frontendBuildDir)) {
        console.error('❌ Frontend build failed - build directory not found');
        process.exit(1);
    }
    console.log('✅ Frontend build completed');
    
    console.log('\n🐍 Preparing backend...');
    
    // Install backend dependencies
    execCommand('pip install -r requirements.txt', backendDir);
    
    // Create backend distribution info
    const backendInfo = {
        version: '1.0.0',
        built_at: new Date().toISOString(),
        python_version: process.version,
        platform: process.platform
    };
    
    fs.writeFileSync(
        path.join(backendDir, 'build_info.json'),
        JSON.stringify(backendInfo, null, 2)
    );
    
    console.log('✅ Backend prepared');
    
    console.log('\n⚡ Building Electron application...');
    
    // Get build target from command line args
    const args = process.argv.slice(2);
    const platform = args.find(arg => ['--win', '--mac', '--linux'].includes(arg));
    
    let buildCommand = 'npm run build';
    
    if (platform === '--win') {
        buildCommand = 'npm run build-win';
        console.log('🪟 Building for Windows...');
    } else if (platform === '--mac') {
        buildCommand = 'npm run build-mac';
        console.log('🍎 Building for macOS...');
    } else if (platform === '--linux') {
        buildCommand = 'npm run build-linux';
        console.log('🐧 Building for Linux...');
    } else {
        console.log('🌍 Building for current platform...');
    }
    
    // Build Electron app
    execCommand(buildCommand, electronDir);
    
    console.log('\n🎉 Build completed successfully!');
    
    // Show output information
    const distDir = path.join(electronDir, 'dist');
    if (fs.existsSync(distDir)) {
        console.log(`📁 Distribution files created in: ${distDir}`);
        
        const files = fs.readdirSync(distDir);
        console.log('📋 Generated files:');
        files.forEach(file => {
            const filePath = path.join(distDir, file);
            const stats = fs.statSync(filePath);
            const size = (stats.size / 1024 / 1024).toFixed(2);
            console.log(`   - ${file} (${size} MB)`);
        });
    }
    
    console.log('\n✨ TikTok Automation Desktop Application build complete!');
}

/**
 * Clean build artifacts
 */
function clean() {
    console.log('🧹 Cleaning build artifacts...');
    
    const dirsToClean = [
        path.join(frontendDir, 'build'),
        path.join(frontendDir, 'node_modules'),
        path.join(electronDir, 'dist'),
        path.join(electronDir, 'node_modules'),
        path.join(backendDir, '__pycache__'),
        path.join(backendDir, '.pytest_cache'),
        path.join(backendDir, 'build_info.json')
    ];
    
    dirsToClean.forEach(dir => {
        if (fs.existsSync(dir)) {
            console.log(`🗑️  Removing: ${dir}`);
            fs.rmSync(dir, { recursive: true, force: true });
        }
    });
    
    console.log('✅ Clean completed');
}

/**
 * Development build
 */
function devBuild() {
    console.log('🔧 Building for development...');
    
    // Install dependencies only
    console.log('Installing frontend dependencies...');
    execCommand('npm install', frontendDir);
    
    console.log('Installing electron dependencies...');
    execCommand('npm install', electronDir);
    
    console.log('Installing backend dependencies...');
    execCommand('pip install -r requirements.txt', backendDir);
    
    console.log('✅ Development setup complete');
    console.log('💡 Run "npm start" in frontend/ and "npm run dev" in electron/ to start development');
}

// Handle command line arguments
const command = process.argv[2];

switch (command) {
    case 'clean':
        clean();
        break;
    case 'dev':
        devBuild();
        break;
    case 'help':
        console.log(`
TikTok Automation Build Script

Usage:
  node build-electron.js [command] [options]

Commands:
  (default)  Build production application
  clean      Clean all build artifacts
  dev        Setup for development
  help       Show this help

Options:
  --win      Build for Windows
  --mac      Build for macOS  
  --linux    Build for Linux

Examples:
  node build-electron.js           # Build for current platform
  node build-electron.js --win     # Build for Windows
  node build-electron.js clean     # Clean build artifacts
  node build-electron.js dev       # Setup for development
        `);
        break;
    default:
        build();
        break;
}

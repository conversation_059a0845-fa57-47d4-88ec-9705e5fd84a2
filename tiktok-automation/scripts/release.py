#!/usr/bin/env python3
"""
Release management script for TikTok Automation
"""

import os
import sys
import subprocess
import json
import re
from pathlib import Path
from typing import Dict, List, Optional
import argparse
from datetime import datetime
import requests

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent


class ReleaseManager:
    """Manages release process"""
    
    def __init__(self, version: str = None, release_type: str = "patch"):
        self.project_root = PROJECT_ROOT
        self.version = version
        self.release_type = release_type
        self.current_version = self._get_current_version()
        self.new_version = version or self._calculate_new_version()
        
        print(f"🚀 Release Manager initialized")
        print(f"   Current Version: {self.current_version}")
        print(f"   New Version: {self.new_version}")
        print(f"   Release Type: {self.release_type}")
    
    def _get_current_version(self) -> str:
        """Get current version from package.json"""
        try:
            package_json = self.project_root / "package.json"
            with open(package_json) as f:
                data = json.load(f)
                return data.get("version", "0.0.0")
        except Exception:
            return "0.0.0"
    
    def _calculate_new_version(self) -> str:
        """Calculate new version based on release type"""
        current = self.current_version.split('.')
        major, minor, patch = int(current[0]), int(current[1]), int(current[2])
        
        if self.release_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif self.release_type == "minor":
            minor += 1
            patch = 0
        else:  # patch
            patch += 1
        
        return f"{major}.{minor}.{patch}"
    
    def update_version_files(self) -> bool:
        """Update version in all relevant files"""
        print(f"📝 Updating version to {self.new_version}...")
        
        try:
            # Update package.json
            package_json = self.project_root / "package.json"
            with open(package_json) as f:
                data = json.load(f)
            
            data["version"] = self.new_version
            
            with open(package_json, 'w') as f:
                json.dump(data, f, indent=2)
            
            print(f"   ✅ Updated {package_json}")
            
            # Update frontend package.json
            frontend_package = self.project_root / "frontend" / "package.json"
            if frontend_package.exists():
                with open(frontend_package) as f:
                    data = json.load(f)
                
                data["version"] = self.new_version
                
                with open(frontend_package, 'w') as f:
                    json.dump(data, f, indent=2)
                
                print(f"   ✅ Updated {frontend_package}")
            
            # Update backend version
            backend_version = self.project_root / "backend" / "core" / "version.py"
            version_content = f'__version__ = "{self.new_version}"\n'
            
            with open(backend_version, 'w') as f:
                f.write(version_content)
            
            print(f"   ✅ Updated {backend_version}")
            
            # Update README.md version badges
            readme_file = self.project_root / "README.md"
            if readme_file.exists():
                with open(readme_file) as f:
                    content = f.read()
                
                # Update version badge
                content = re.sub(
                    r'(https://img\.shields\.io/badge/version-)[^-]+(-.+\.svg)',
                    f'\\g<1>{self.new_version}\\g<2>',
                    content
                )
                
                with open(readme_file, 'w') as f:
                    f.write(content)
                
                print(f"   ✅ Updated {readme_file}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error updating version files: {e}")
            return False
    
    def generate_changelog(self) -> bool:
        """Generate changelog for the release"""
        print("📋 Generating changelog...")
        
        try:
            # Get git commits since last tag
            result = subprocess.run([
                "git", "log", "--oneline", "--pretty=format:%s", f"v{self.current_version}..HEAD"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode != 0:
                # If no previous tag, get all commits
                result = subprocess.run([
                    "git", "log", "--oneline", "--pretty=format:%s"
                ], capture_output=True, text=True, cwd=self.project_root)
            
            commits = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            # Categorize commits
            features = []
            fixes = []
            improvements = []
            others = []
            
            for commit in commits:
                commit = commit.strip()
                if not commit:
                    continue
                
                if any(keyword in commit.lower() for keyword in ['feat:', 'feature:', 'add:']):
                    features.append(commit)
                elif any(keyword in commit.lower() for keyword in ['fix:', 'bug:', 'patch:']):
                    fixes.append(commit)
                elif any(keyword in commit.lower() for keyword in ['improve:', 'enhance:', 'update:']):
                    improvements.append(commit)
                else:
                    others.append(commit)
            
            # Generate changelog content
            changelog_content = f"""# Changelog

## [v{self.new_version}] - {datetime.now().strftime('%Y-%m-%d')}

"""
            
            if features:
                changelog_content += "### ✨ New Features\n"
                for feature in features:
                    changelog_content += f"- {feature}\n"
                changelog_content += "\n"
            
            if improvements:
                changelog_content += "### 🚀 Improvements\n"
                for improvement in improvements:
                    changelog_content += f"- {improvement}\n"
                changelog_content += "\n"
            
            if fixes:
                changelog_content += "### 🐛 Bug Fixes\n"
                for fix in fixes:
                    changelog_content += f"- {fix}\n"
                changelog_content += "\n"
            
            if others:
                changelog_content += "### 📝 Other Changes\n"
                for other in others:
                    changelog_content += f"- {other}\n"
                changelog_content += "\n"
            
            # Read existing changelog if it exists
            changelog_file = self.project_root / "CHANGELOG.md"
            existing_content = ""
            
            if changelog_file.exists():
                with open(changelog_file) as f:
                    existing_content = f.read()
                
                # Remove the first line if it's just "# Changelog"
                lines = existing_content.split('\n')
                if lines and lines[0].strip() == "# Changelog":
                    existing_content = '\n'.join(lines[1:])
            
            # Combine new and existing content
            full_content = changelog_content + existing_content
            
            with open(changelog_file, 'w') as f:
                f.write(full_content)
            
            print(f"   ✅ Generated changelog: {changelog_file}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error generating changelog: {e}")
            return False
    
    def create_git_tag(self) -> bool:
        """Create git tag for the release"""
        print(f"🏷️  Creating git tag v{self.new_version}...")
        
        try:
            # Add all changes
            subprocess.run(["git", "add", "."], check=True, cwd=self.project_root)
            
            # Commit changes
            subprocess.run([
                "git", "commit", "-m", f"chore: release v{self.new_version}"
            ], check=True, cwd=self.project_root)
            
            # Create tag
            subprocess.run([
                "git", "tag", "-a", f"v{self.new_version}", 
                "-m", f"Release v{self.new_version}"
            ], check=True, cwd=self.project_root)
            
            print(f"   ✅ Created tag v{self.new_version}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error creating git tag: {e}")
            return False
    
    def push_release(self) -> bool:
        """Push release to remote repository"""
        print("📤 Pushing release to remote...")
        
        try:
            # Push commits
            subprocess.run(["git", "push"], check=True, cwd=self.project_root)
            
            # Push tags
            subprocess.run(["git", "push", "--tags"], check=True, cwd=self.project_root)
            
            print("   ✅ Pushed release to remote")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error pushing release: {e}")
            return False
    
    def create_github_release(self, github_token: str) -> bool:
        """Create GitHub release"""
        print("🐙 Creating GitHub release...")
        
        try:
            # Read changelog for release notes
            changelog_file = self.project_root / "CHANGELOG.md"
            release_notes = ""
            
            if changelog_file.exists():
                with open(changelog_file) as f:
                    content = f.read()
                
                # Extract notes for current version
                lines = content.split('\n')
                in_current_version = False
                
                for line in lines:
                    if f"## [v{self.new_version}]" in line:
                        in_current_version = True
                        continue
                    elif line.startswith("## [v") and in_current_version:
                        break
                    elif in_current_version:
                        release_notes += line + '\n'
            
            # Create release via GitHub API
            repo_info = self._get_repo_info()
            if not repo_info:
                print("   ❌ Could not determine repository information")
                return False
            
            owner, repo = repo_info
            
            release_data = {
                "tag_name": f"v{self.new_version}",
                "target_commitish": "main",
                "name": f"v{self.new_version}",
                "body": release_notes.strip(),
                "draft": False,
                "prerelease": "beta" in self.new_version or "alpha" in self.new_version
            }
            
            headers = {
                "Authorization": f"token {github_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            response = requests.post(
                f"https://api.github.com/repos/{owner}/{repo}/releases",
                json=release_data,
                headers=headers
            )
            
            if response.status_code == 201:
                release_url = response.json()["html_url"]
                print(f"   ✅ Created GitHub release: {release_url}")
                return True
            else:
                print(f"   ❌ GitHub API error: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error creating GitHub release: {e}")
            return False
    
    def _get_repo_info(self) -> Optional[tuple]:
        """Get repository owner and name from git remote"""
        try:
            result = subprocess.run([
                "git", "remote", "get-url", "origin"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode != 0:
                return None
            
            url = result.stdout.strip()
            
            # Parse GitHub URL
            if "github.com" in url:
                if url.startswith("git@"):
                    # SSH format: **************:owner/repo.git
                    parts = url.split(":")[-1].replace(".git", "").split("/")
                elif url.startswith("https://"):
                    # HTTPS format: https://github.com/owner/repo.git
                    parts = url.replace("https://github.com/", "").replace(".git", "").split("/")
                else:
                    return None
                
                if len(parts) >= 2:
                    return parts[0], parts[1]
            
            return None
            
        except Exception:
            return None
    
    def run_tests(self) -> bool:
        """Run tests before release"""
        print("🧪 Running tests...")
        
        try:
            result = subprocess.run([
                "python", "scripts/run-tests.py", "--component", "all"
            ], cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ All tests passed")
                return True
            else:
                print("   ❌ Tests failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error running tests: {e}")
            return False
    
    def build_release(self) -> bool:
        """Build release packages"""
        print("🏗️  Building release packages...")
        
        try:
            result = subprocess.run([
                "python", "scripts/build.py", "--platform", "all"
            ], cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ Release packages built successfully")
                return True
            else:
                print("   ❌ Build failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error building release: {e}")
            return False
    
    def create_release(self, skip_tests: bool = False, skip_build: bool = False, 
                      github_token: str = None) -> bool:
        """Create complete release"""
        print(f"🚀 Creating release v{self.new_version}...")
        
        success = True
        
        # Run tests
        if not skip_tests and not self.run_tests():
            success = False
        
        # Update version files
        if success and not self.update_version_files():
            success = False
        
        # Generate changelog
        if success and not self.generate_changelog():
            success = False
        
        # Build release
        if success and not skip_build and not self.build_release():
            success = False
        
        # Create git tag
        if success and not self.create_git_tag():
            success = False
        
        # Push to remote
        if success and not self.push_release():
            success = False
        
        # Create GitHub release
        if success and github_token and not self.create_github_release(github_token):
            success = False
        
        if success:
            print(f"\n🎉 Release v{self.new_version} created successfully!")
            print(f"🏷️  Tag: v{self.new_version}")
            print(f"📦 Build artifacts available in dist/")
        else:
            print(f"\n💥 Release creation failed!")
        
        return success


def main():
    """Main release function"""
    parser = argparse.ArgumentParser(description="TikTok Automation Release Manager")
    parser.add_argument(
        "--version", "-v",
        help="Specific version to release (e.g., 1.2.3)"
    )
    parser.add_argument(
        "--type", "-t",
        choices=["major", "minor", "patch"],
        default="patch",
        help="Release type for version bump"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="Skip running tests"
    )
    parser.add_argument(
        "--skip-build",
        action="store_true",
        help="Skip building release packages"
    )
    parser.add_argument(
        "--github-token",
        help="GitHub token for creating release"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    
    args = parser.parse_args()
    
    # Get GitHub token from environment if not provided
    github_token = args.github_token or os.getenv("GITHUB_TOKEN")
    
    # Initialize release manager
    release_manager = ReleaseManager(args.version, args.type)
    
    if args.dry_run:
        print("🔍 Dry run mode - no changes will be made")
        print(f"Would create release: v{release_manager.new_version}")
        return
    
    # Create release
    success = release_manager.create_release(
        skip_tests=args.skip_tests,
        skip_build=args.skip_build,
        github_token=github_token
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

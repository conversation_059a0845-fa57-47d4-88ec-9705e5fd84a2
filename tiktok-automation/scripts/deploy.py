#!/usr/bin/env python3
"""
Deployment script for TikTok Automation platform
"""

import os
import sys
import subprocess
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any, List

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent


class DeploymentManager:
    """Manages deployment of TikTok Automation platform"""
    
    def __init__(self, environment: str = "production"):
        self.environment = environment
        self.project_root = PROJECT_ROOT
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        config_file = self.project_root / "deploy" / f"{self.environment}.json"
        
        if config_file.exists():
            with open(config_file) as f:
                return json.load(f)
        
        # Default configuration
        return {
            "docker": {
                "compose_file": "docker-compose.prod.yml",
                "build_args": {},
                "environment_file": ".env.prod"
            },
            "backup": {
                "enabled": True,
                "retention_days": 30
            },
            "health_check": {
                "timeout": 300,
                "interval": 10
            }
        }
    
    def deploy_docker(self, force_rebuild: bool = False) -> bool:
        """Deploy using Docker Compose"""
        print(f"🐳 Deploying with Docker ({self.environment})...")
        
        try:
            # Change to project directory
            os.chdir(self.project_root)
            
            # Check if Docker is available
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            subprocess.run(["docker-compose", "--version"], check=True, capture_output=True)
            
            compose_file = self.config["docker"]["compose_file"]
            env_file = self.config["docker"]["environment_file"]
            
            # Verify environment file exists
            if not Path(env_file).exists():
                print(f"❌ Environment file {env_file} not found!")
                return False
            
            # Pull latest images
            print("📦 Pulling latest images...")
            subprocess.run([
                "docker-compose", "-f", compose_file,
                "pull"
            ], check=True)
            
            # Build images if needed
            if force_rebuild:
                print("🏗️  Building images...")
                subprocess.run([
                    "docker-compose", "-f", compose_file,
                    "build", "--no-cache"
                ], check=True)
            
            # Create backup before deployment
            if self.config["backup"]["enabled"]:
                self._create_backup()
            
            # Deploy services
            print("🚀 Starting services...")
            subprocess.run([
                "docker-compose", "-f", compose_file,
                "up", "-d", "--remove-orphans"
            ], check=True)
            
            # Wait for services to be healthy
            if self._wait_for_health():
                print("✅ Deployment successful!")
                return True
            else:
                print("❌ Health check failed!")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Docker deployment failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Deployment error: {e}")
            return False
    
    def deploy_standalone(self) -> bool:
        """Deploy as standalone application"""
        print(f"📦 Deploying standalone ({self.environment})...")
        
        try:
            # Build backend
            print("🐍 Building backend...")
            backend_dir = self.project_root / "backend"
            os.chdir(backend_dir)
            
            # Install dependencies
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
            
            # Run database migrations
            subprocess.run([
                sys.executable, "-c",
                "from core.database import init_database; import asyncio; asyncio.run(init_database())"
            ], check=True)
            
            # Build frontend
            print("⚛️  Building frontend...")
            frontend_dir = self.project_root / "frontend"
            os.chdir(frontend_dir)
            
            subprocess.run(["npm", "install"], check=True)
            subprocess.run(["npm", "run", "build"], check=True)
            
            # Build Electron app
            print("⚡ Building Electron app...")
            electron_dir = self.project_root / "electron"
            os.chdir(electron_dir)
            
            subprocess.run(["npm", "install"], check=True)
            subprocess.run(["npm", "run", "build"], check=True)
            
            print("✅ Standalone deployment successful!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Standalone deployment failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Deployment error: {e}")
            return False
    
    def deploy_cloud(self, provider: str = "aws") -> bool:
        """Deploy to cloud provider"""
        print(f"☁️  Deploying to {provider.upper()}...")
        
        if provider == "aws":
            return self._deploy_aws()
        elif provider == "gcp":
            return self._deploy_gcp()
        elif provider == "azure":
            return self._deploy_azure()
        else:
            print(f"❌ Unsupported cloud provider: {provider}")
            return False
    
    def _deploy_aws(self) -> bool:
        """Deploy to AWS using ECS/Fargate"""
        try:
            # Check AWS CLI
            subprocess.run(["aws", "--version"], check=True, capture_output=True)
            
            # Build and push Docker images
            print("🏗️  Building and pushing Docker images...")
            
            # Get AWS account ID
            result = subprocess.run([
                "aws", "sts", "get-caller-identity", "--query", "Account", "--output", "text"
            ], capture_output=True, text=True, check=True)
            account_id = result.stdout.strip()
            
            region = "us-east-1"  # Configure as needed
            registry = f"{account_id}.dkr.ecr.{region}.amazonaws.com"
            
            # Login to ECR
            subprocess.run([
                "aws", "ecr", "get-login-password", "--region", region
            ], stdout=subprocess.PIPE, check=True)
            
            # Build and push images
            images = ["backend", "frontend"]
            for image in images:
                tag = f"{registry}/tiktok-automation-{image}:latest"
                
                # Build image
                subprocess.run([
                    "docker", "build",
                    "-f", f"docker/Dockerfile.{image}",
                    "-t", tag, "."
                ], check=True)
                
                # Push image
                subprocess.run(["docker", "push", tag], check=True)
            
            # Deploy using CloudFormation or CDK
            print("🚀 Deploying infrastructure...")
            # Implementation depends on your IaC tool
            
            print("✅ AWS deployment successful!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ AWS deployment failed: {e}")
            return False
    
    def _deploy_gcp(self) -> bool:
        """Deploy to Google Cloud Platform"""
        # Implementation for GCP deployment
        print("🚧 GCP deployment not implemented yet")
        return False
    
    def _deploy_azure(self) -> bool:
        """Deploy to Microsoft Azure"""
        # Implementation for Azure deployment
        print("🚧 Azure deployment not implemented yet")
        return False
    
    def _create_backup(self) -> bool:
        """Create backup before deployment"""
        print("💾 Creating backup...")
        
        try:
            backup_dir = self.project_root / "backups" / f"backup_{int(time.time())}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup database
            if self._backup_database(backup_dir):
                print("✅ Database backup created")
            
            # Backup configuration
            if self._backup_config(backup_dir):
                print("✅ Configuration backup created")
            
            # Cleanup old backups
            self._cleanup_old_backups()
            
            return True
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            return False
    
    def _backup_database(self, backup_dir: Path) -> bool:
        """Backup database"""
        try:
            if self.environment == "docker":
                # Docker database backup
                subprocess.run([
                    "docker", "exec", "tiktok-automation-db",
                    "pg_dump", "-U", "user", "tiktok_automation"
                ], stdout=open(backup_dir / "database.sql", "w"), check=True)
            else:
                # Standalone database backup
                db_file = self.project_root / "backend" / "tiktok_automation.db"
                if db_file.exists():
                    import shutil
                    shutil.copy2(db_file, backup_dir / "database.db")
            
            return True
        except Exception as e:
            print(f"Database backup failed: {e}")
            return False
    
    def _backup_config(self, backup_dir: Path) -> bool:
        """Backup configuration files"""
        try:
            config_files = [
                ".env",
                ".env.prod",
                "docker-compose.yml",
                "docker-compose.prod.yml"
            ]
            
            for config_file in config_files:
                source = self.project_root / config_file
                if source.exists():
                    import shutil
                    shutil.copy2(source, backup_dir / config_file)
            
            return True
        except Exception as e:
            print(f"Config backup failed: {e}")
            return False
    
    def _cleanup_old_backups(self) -> None:
        """Remove old backups"""
        try:
            backup_root = self.project_root / "backups"
            if not backup_root.exists():
                return
            
            retention_days = self.config["backup"]["retention_days"]
            cutoff_time = time.time() - (retention_days * 24 * 60 * 60)
            
            for backup_dir in backup_root.iterdir():
                if backup_dir.is_dir() and backup_dir.name.startswith("backup_"):
                    try:
                        timestamp = int(backup_dir.name.split("_")[1])
                        if timestamp < cutoff_time:
                            import shutil
                            shutil.rmtree(backup_dir)
                            print(f"🗑️  Removed old backup: {backup_dir.name}")
                    except (ValueError, IndexError):
                        continue
                        
        except Exception as e:
            print(f"Backup cleanup failed: {e}")
    
    def _wait_for_health(self) -> bool:
        """Wait for services to be healthy"""
        print("🔍 Checking service health...")
        
        timeout = self.config["health_check"]["timeout"]
        interval = self.config["health_check"]["interval"]
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check backend health
                import requests
                response = requests.get("http://localhost:8000/health", timeout=5)
                
                if response.status_code == 200:
                    health_data = response.json()
                    if health_data.get("status") == "healthy":
                        print("✅ Services are healthy!")
                        return True
                
            except Exception:
                pass
            
            print(f"⏳ Waiting for services... ({int(time.time() - start_time)}s)")
            time.sleep(interval)
        
        return False
    
    def rollback(self, backup_name: str = None) -> bool:
        """Rollback to previous deployment"""
        print("🔄 Rolling back deployment...")
        
        try:
            if backup_name:
                backup_dir = self.project_root / "backups" / backup_name
            else:
                # Find latest backup
                backup_root = self.project_root / "backups"
                backups = [d for d in backup_root.iterdir() if d.is_dir()]
                if not backups:
                    print("❌ No backups found!")
                    return False
                backup_dir = max(backups, key=lambda x: x.stat().st_mtime)
            
            if not backup_dir.exists():
                print(f"❌ Backup not found: {backup_dir}")
                return False
            
            print(f"📦 Restoring from backup: {backup_dir.name}")
            
            # Stop current services
            if self.environment == "docker":
                subprocess.run([
                    "docker-compose", "-f", self.config["docker"]["compose_file"],
                    "down"
                ], check=True)
            
            # Restore database
            self._restore_database(backup_dir)
            
            # Restore configuration
            self._restore_config(backup_dir)
            
            # Restart services
            if self.environment == "docker":
                subprocess.run([
                    "docker-compose", "-f", self.config["docker"]["compose_file"],
                    "up", "-d"
                ], check=True)
            
            print("✅ Rollback successful!")
            return True
            
        except Exception as e:
            print(f"❌ Rollback failed: {e}")
            return False
    
    def _restore_database(self, backup_dir: Path) -> bool:
        """Restore database from backup"""
        try:
            if self.environment == "docker":
                db_backup = backup_dir / "database.sql"
                if db_backup.exists():
                    subprocess.run([
                        "docker", "exec", "-i", "tiktok-automation-db",
                        "psql", "-U", "user", "tiktok_automation"
                    ], stdin=open(db_backup), check=True)
            else:
                db_backup = backup_dir / "database.db"
                if db_backup.exists():
                    import shutil
                    shutil.copy2(db_backup, self.project_root / "backend" / "tiktok_automation.db")
            
            return True
        except Exception as e:
            print(f"Database restore failed: {e}")
            return False
    
    def _restore_config(self, backup_dir: Path) -> bool:
        """Restore configuration from backup"""
        try:
            for config_file in backup_dir.iterdir():
                if config_file.is_file() and config_file.name.startswith(".env"):
                    import shutil
                    shutil.copy2(config_file, self.project_root / config_file.name)
            
            return True
        except Exception as e:
            print(f"Config restore failed: {e}")
            return False


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description="TikTok Automation Deployment Manager")
    parser.add_argument(
        "--environment", "-e",
        choices=["development", "staging", "production"],
        default="production",
        help="Deployment environment"
    )
    parser.add_argument(
        "--method", "-m",
        choices=["docker", "standalone", "cloud"],
        default="docker",
        help="Deployment method"
    )
    parser.add_argument(
        "--cloud-provider",
        choices=["aws", "gcp", "azure"],
        default="aws",
        help="Cloud provider for cloud deployment"
    )
    parser.add_argument(
        "--force-rebuild",
        action="store_true",
        help="Force rebuild of Docker images"
    )
    parser.add_argument(
        "--rollback",
        metavar="BACKUP_NAME",
        help="Rollback to specified backup"
    )
    parser.add_argument(
        "--backup-only",
        action="store_true",
        help="Create backup only, don't deploy"
    )
    
    args = parser.parse_args()
    
    # Initialize deployment manager
    deployer = DeploymentManager(args.environment)
    
    print(f"🚀 TikTok Automation Deployment Manager")
    print(f"Environment: {args.environment}")
    print(f"Method: {args.method}")
    print("-" * 50)
    
    success = False
    
    try:
        if args.rollback:
            success = deployer.rollback(args.rollback)
        elif args.backup_only:
            success = deployer._create_backup()
        elif args.method == "docker":
            success = deployer.deploy_docker(args.force_rebuild)
        elif args.method == "standalone":
            success = deployer.deploy_standalone()
        elif args.method == "cloud":
            success = deployer.deploy_cloud(args.cloud_provider)
        
        if success:
            print("\n🎉 Deployment completed successfully!")
            sys.exit(0)
        else:
            print("\n💥 Deployment failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Deployment cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

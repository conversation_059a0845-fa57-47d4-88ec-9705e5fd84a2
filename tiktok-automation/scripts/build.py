#!/usr/bin/env python3
"""
Build script for TikTok Automation platform
"""

import os
import sys
import subprocess
import shutil
import json
import platform
from pathlib import Path
from typing import Dict, List, Optional
import argparse
from datetime import datetime

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent


class BuildManager:
    """Manages the build process for all components"""
    
    def __init__(self, target_platform: str = None, build_type: str = "production"):
        self.project_root = PROJECT_ROOT
        self.target_platform = target_platform or platform.system().lower()
        self.build_type = build_type
        self.build_dir = self.project_root / "dist"
        self.temp_dir = self.project_root / "temp"
        
        # Build configuration
        self.config = {
            "version": self._get_version(),
            "build_date": datetime.now().isoformat(),
            "platform": self.target_platform,
            "build_type": self.build_type
        }
        
        print(f"🏗️  Build Manager initialized")
        print(f"   Platform: {self.target_platform}")
        print(f"   Build Type: {self.build_type}")
        print(f"   Version: {self.config['version']}")
    
    def _get_version(self) -> str:
        """Get version from package.json"""
        try:
            package_json = self.project_root / "package.json"
            if package_json.exists():
                with open(package_json) as f:
                    data = json.load(f)
                    return data.get("version", "1.0.0")
        except Exception:
            pass
        return "1.0.0"
    
    def clean_build_dirs(self):
        """Clean build directories"""
        print("🧹 Cleaning build directories...")
        
        dirs_to_clean = [
            self.build_dir,
            self.temp_dir,
            self.project_root / "backend" / "dist",
            self.project_root / "frontend" / "build",
            self.project_root / "frontend" / "dist"
        ]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"   Cleaned: {dir_path}")
        
        # Create fresh build directory
        self.build_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def build_backend(self) -> bool:
        """Build Python backend"""
        print("🐍 Building Python backend...")
        
        try:
            backend_dir = self.project_root / "backend"
            os.chdir(backend_dir)
            
            # Install dependencies
            print("   Installing dependencies...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
            
            # Create backend distribution
            backend_dist = self.temp_dir / "backend"
            backend_dist.mkdir(parents=True, exist_ok=True)
            
            # Copy backend files
            files_to_copy = [
                "main.py",
                "core/",
                "api/",
                "models/",
                "services/",
                "automation/",
                "camoufox_integration/",
                "requirements.txt"
            ]
            
            for item in files_to_copy:
                src = backend_dir / item
                if src.exists():
                    if src.is_dir():
                        shutil.copytree(src, backend_dist / item, dirs_exist_ok=True)
                    else:
                        shutil.copy2(src, backend_dist / item)
            
            # Create startup script
            self._create_backend_startup_script(backend_dist)
            
            print("   ✅ Backend build completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Backend build failed: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Backend build error: {e}")
            return False
        finally:
            os.chdir(self.project_root)
    
    def build_frontend(self) -> bool:
        """Build React frontend"""
        print("⚛️  Building React frontend...")
        
        try:
            frontend_dir = self.project_root / "frontend"
            os.chdir(frontend_dir)
            
            # Install dependencies
            print("   Installing dependencies...")
            subprocess.run(["npm", "install"], check=True)
            
            # Build frontend
            print("   Building frontend...")
            env = os.environ.copy()
            env["GENERATE_SOURCEMAP"] = "false"
            env["REACT_APP_VERSION"] = self.config["version"]
            env["REACT_APP_BUILD_DATE"] = self.config["build_date"]
            
            subprocess.run(["npm", "run", "build"], check=True, env=env)
            
            # Copy build to temp directory
            frontend_build = frontend_dir / "build"
            frontend_dist = self.temp_dir / "frontend"
            
            if frontend_build.exists():
                shutil.copytree(frontend_build, frontend_dist, dirs_exist_ok=True)
            
            print("   ✅ Frontend build completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Frontend build failed: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Frontend build error: {e}")
            return False
        finally:
            os.chdir(self.project_root)
    
    def build_electron(self) -> bool:
        """Build Electron application"""
        print("⚡ Building Electron application...")
        
        try:
            # Install dependencies
            print("   Installing dependencies...")
            subprocess.run(["npm", "install"], check=True, cwd=self.project_root)
            
            # Copy built components to electron directory
            self._prepare_electron_assets()
            
            # Build Electron app
            print("   Building Electron app...")
            
            build_command = ["npm", "run", f"build:{self.target_platform}"]
            if self.target_platform == "all":
                build_command = ["npm", "run", "build:all"]
            
            subprocess.run(build_command, check=True, cwd=self.project_root)
            
            print("   ✅ Electron build completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Electron build failed: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Electron build error: {e}")
            return False
    
    def _prepare_electron_assets(self):
        """Prepare assets for Electron build"""
        print("   Preparing Electron assets...")
        
        # Copy backend to electron resources
        backend_src = self.temp_dir / "backend"
        backend_dest = self.project_root / "src" / "resources" / "backend"
        
        if backend_dest.exists():
            shutil.rmtree(backend_dest)
        
        if backend_src.exists():
            shutil.copytree(backend_src, backend_dest)
        
        # Copy frontend to electron resources
        frontend_src = self.temp_dir / "frontend"
        frontend_dest = self.project_root / "src" / "resources" / "frontend"
        
        if frontend_dest.exists():
            shutil.rmtree(frontend_dest)
        
        if frontend_src.exists():
            shutil.copytree(frontend_src, frontend_dest)
        
        # Create build info file
        build_info = {
            **self.config,
            "components": {
                "backend": backend_src.exists(),
                "frontend": frontend_src.exists()
            }
        }
        
        build_info_file = self.project_root / "src" / "resources" / "build-info.json"
        build_info_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(build_info_file, 'w') as f:
            json.dump(build_info, f, indent=2)
    
    def _create_backend_startup_script(self, backend_dist: Path):
        """Create startup script for backend"""
        if self.target_platform == "windows":
            script_content = """@echo off
cd /d "%~dp0"
python main.py
pause
"""
            script_file = backend_dist / "start_backend.bat"
        else:
            script_content = """#!/bin/bash
cd "$(dirname "$0")"
python3 main.py
"""
            script_file = backend_dist / "start_backend.sh"
        
        with open(script_file, 'w') as f:
            f.write(script_content)
        
        if not self.target_platform == "windows":
            os.chmod(script_file, 0o755)
    
    def create_installer(self) -> bool:
        """Create installer packages"""
        print("📦 Creating installer packages...")
        
        try:
            # The electron-builder should have created the distributables
            electron_dist = self.project_root / "dist"
            
            if not electron_dist.exists():
                print("   ❌ No Electron dist directory found")
                return False
            
            # Copy to final build directory
            final_dist = self.build_dir / f"v{self.config['version']}"
            final_dist.mkdir(parents=True, exist_ok=True)
            
            # Copy all distributable files
            for item in electron_dist.iterdir():
                if item.is_file() and item.suffix in ['.exe', '.dmg', '.AppImage', '.deb', '.rpm']:
                    dest = final_dist / item.name
                    shutil.copy2(item, dest)
                    print(f"   📦 Created: {dest.name}")
            
            # Create checksums
            self._create_checksums(final_dist)
            
            # Create release notes
            self._create_release_notes(final_dist)
            
            print("   ✅ Installer packages created")
            return True
            
        except Exception as e:
            print(f"   ❌ Installer creation failed: {e}")
            return False
    
    def _create_checksums(self, dist_dir: Path):
        """Create checksum files for distributables"""
        import hashlib
        
        checksums = {}
        
        for file_path in dist_dir.iterdir():
            if file_path.is_file() and file_path.suffix in ['.exe', '.dmg', '.AppImage', '.deb', '.rpm']:
                # Calculate SHA256
                sha256_hash = hashlib.sha256()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        sha256_hash.update(chunk)
                
                checksums[file_path.name] = {
                    "sha256": sha256_hash.hexdigest(),
                    "size": file_path.stat().st_size
                }
        
        # Write checksums file
        checksums_file = dist_dir / "checksums.json"
        with open(checksums_file, 'w') as f:
            json.dump(checksums, f, indent=2)
        
        # Write SHA256SUMS file (traditional format)
        sha256sums_file = dist_dir / "SHA256SUMS"
        with open(sha256sums_file, 'w') as f:
            for filename, data in checksums.items():
                f.write(f"{data['sha256']}  {filename}\n")
    
    def _create_release_notes(self, dist_dir: Path):
        """Create release notes file"""
        release_notes = f"""# TikTok Automation v{self.config['version']}

## Release Information
- **Version**: {self.config['version']}
- **Build Date**: {self.config['build_date']}
- **Platform**: {self.config['platform']}
- **Build Type**: {self.config['build_type']}

## Installation Instructions

### Windows
1. Download the `.exe` file
2. Run the installer as administrator
3. Follow the installation wizard

### macOS
1. Download the `.dmg` file
2. Open the DMG and drag the app to Applications
3. Right-click and select "Open" on first launch

### Linux
1. Download the appropriate package (`.AppImage`, `.deb`, or `.rpm`)
2. For AppImage: Make executable and run
3. For DEB: `sudo dpkg -i package.deb`
4. For RPM: `sudo rpm -i package.rpm`

## System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **OS**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

## Verification
Verify file integrity using the provided checksums:
```bash
sha256sum -c SHA256SUMS
```

## Support
- Documentation: https://github.com/your-username/tiktok-automation/docs
- Issues: https://github.com/your-username/tiktok-automation/issues
- Discord: https://discord.gg/your-server
"""
        
        release_notes_file = dist_dir / "RELEASE_NOTES.md"
        with open(release_notes_file, 'w') as f:
            f.write(release_notes)
    
    def build_all(self) -> bool:
        """Build all components"""
        print("🚀 Starting complete build process...")
        
        success = True
        
        # Clean build directories
        self.clean_build_dirs()
        
        # Build backend
        if not self.build_backend():
            success = False
        
        # Build frontend
        if not self.build_frontend():
            success = False
        
        # Build Electron app
        if success and not self.build_electron():
            success = False
        
        # Create installer packages
        if success and not self.create_installer():
            success = False
        
        if success:
            print("\n🎉 Build completed successfully!")
            print(f"📦 Distributables available in: {self.build_dir}")
        else:
            print("\n💥 Build failed!")
        
        return success
    
    def get_build_info(self) -> Dict:
        """Get build information"""
        return self.config


def main():
    """Main build function"""
    parser = argparse.ArgumentParser(description="TikTok Automation Build Manager")
    parser.add_argument(
        "--platform", "-p",
        choices=["windows", "macos", "linux", "all"],
        default=None,
        help="Target platform for build"
    )
    parser.add_argument(
        "--type", "-t",
        choices=["development", "production"],
        default="production",
        help="Build type"
    )
    parser.add_argument(
        "--component", "-c",
        choices=["backend", "frontend", "electron", "all"],
        default="all",
        help="Component to build"
    )
    parser.add_argument(
        "--clean",
        action="store_true",
        help="Clean build directories only"
    )
    
    args = parser.parse_args()
    
    # Initialize build manager
    builder = BuildManager(args.platform, args.type)
    
    if args.clean:
        builder.clean_build_dirs()
        print("✅ Build directories cleaned")
        return
    
    success = False
    
    if args.component == "backend":
        success = builder.build_backend()
    elif args.component == "frontend":
        success = builder.build_frontend()
    elif args.component == "electron":
        success = builder.build_electron()
    else:
        success = builder.build_all()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

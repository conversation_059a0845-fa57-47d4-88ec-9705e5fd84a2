#!/usr/bin/env python3
"""
Test runner script for TikTok Automation project
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import json
import time
from typing import List, Dict, Any

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"


class TestRunner:
    """Test runner for the entire project"""
    
    def __init__(self):
        self.results = {
            "backend": {"passed": 0, "failed": 0, "skipped": 0, "errors": []},
            "frontend": {"passed": 0, "failed": 0, "skipped": 0, "errors": []},
            "integration": {"passed": 0, "failed": 0, "skipped": 0, "errors": []},
            "e2e": {"passed": 0, "failed": 0, "skipped": 0, "errors": []}
        }
        self.start_time = time.time()
    
    def run_backend_tests(self, test_type: str = "all", verbose: bool = False) -> bool:
        """Run backend Python tests"""
        print("🐍 Running Backend Tests...")
        
        os.chdir(BACKEND_DIR)
        
        # Prepare pytest command
        cmd = ["python", "-m", "pytest"]
        
        if verbose:
            cmd.append("-v")
        
        # Add coverage reporting
        cmd.extend([
            "--cov=.",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
        
        # Add test selection based on type
        if test_type == "unit":
            cmd.append("tests/test_services/")
            cmd.append("tests/test_models/")
        elif test_type == "api":
            cmd.append("tests/test_api/")
        elif test_type == "integration":
            cmd.append("tests/test_integration/")
        elif test_type == "automation":
            cmd.append("tests/test_automation/")
        else:
            cmd.append("tests/")
        
        # Add output format
        cmd.extend([
            "--junit-xml=test-results.xml",
            "--html=test-report.html",
            "--self-contained-html"
        ])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # Parse results
            self._parse_pytest_output(result.stdout, "backend")
            
            if result.returncode == 0:
                print("✅ Backend tests passed!")
                return True
            else:
                print("❌ Backend tests failed!")
                print(result.stdout)
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Error running backend tests: {e}")
            self.results["backend"]["errors"].append(str(e))
            return False
    
    def run_frontend_tests(self, test_type: str = "all", verbose: bool = False) -> bool:
        """Run frontend JavaScript/React tests"""
        print("⚛️  Running Frontend Tests...")
        
        os.chdir(FRONTEND_DIR)
        
        # Check if node_modules exists
        if not (FRONTEND_DIR / "node_modules").exists():
            print("📦 Installing frontend dependencies...")
            subprocess.run(["npm", "install"], check=True)
        
        # Prepare test command
        cmd = ["npm", "test"]
        
        if test_type == "unit":
            cmd.append("-- --testPathPattern=components")
        elif test_type == "integration":
            cmd.append("-- --testPathPattern=integration")
        elif test_type == "e2e":
            cmd.append("-- --testPathPattern=e2e")
        
        # Add coverage and output options
        env = os.environ.copy()
        env["CI"] = "true"  # Prevents watch mode
        env["GENERATE_SOURCEMAP"] = "false"
        
        if verbose:
            env["VERBOSE"] = "true"
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env
            )
            
            # Parse results
            self._parse_jest_output(result.stdout, "frontend")
            
            if result.returncode == 0:
                print("✅ Frontend tests passed!")
                return True
            else:
                print("❌ Frontend tests failed!")
                print(result.stdout)
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Error running frontend tests: {e}")
            self.results["frontend"]["errors"].append(str(e))
            return False
    
    def run_integration_tests(self, verbose: bool = False) -> bool:
        """Run integration tests"""
        print("🔗 Running Integration Tests...")
        
        os.chdir(BACKEND_DIR)
        
        cmd = [
            "python", "-m", "pytest",
            "tests/test_integration/",
            "-v" if verbose else "-q",
            "--junit-xml=integration-results.xml"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            self._parse_pytest_output(result.stdout, "integration")
            
            if result.returncode == 0:
                print("✅ Integration tests passed!")
                return True
            else:
                print("❌ Integration tests failed!")
                print(result.stdout)
                return False
                
        except Exception as e:
            print(f"❌ Error running integration tests: {e}")
            self.results["integration"]["errors"].append(str(e))
            return False
    
    def run_e2e_tests(self, verbose: bool = False) -> bool:
        """Run end-to-end tests"""
        print("🎭 Running E2E Tests...")
        
        # Start backend server for E2E tests
        print("🚀 Starting backend server...")
        backend_process = None
        
        try:
            os.chdir(BACKEND_DIR)
            backend_process = subprocess.Popen(
                ["python", "main.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            time.sleep(5)
            
            # Run E2E tests
            os.chdir(FRONTEND_DIR)
            
            cmd = ["npm", "run", "test:e2e"]
            if verbose:
                cmd.append("--verbose")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ E2E tests passed!")
                return True
            else:
                print("❌ E2E tests failed!")
                print(result.stdout)
                return False
                
        except Exception as e:
            print(f"❌ Error running E2E tests: {e}")
            self.results["e2e"]["errors"].append(str(e))
            return False
        finally:
            # Stop backend server
            if backend_process:
                backend_process.terminate()
                backend_process.wait()
    
    def run_linting(self) -> bool:
        """Run code linting and formatting checks"""
        print("🔍 Running Code Quality Checks...")
        
        success = True
        
        # Backend linting
        print("  📝 Checking Python code...")
        os.chdir(BACKEND_DIR)
        
        # Run flake8
        try:
            result = subprocess.run(
                ["python", "-m", "flake8", ".", "--max-line-length=100"],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print("❌ Python linting failed:")
                print(result.stdout)
                success = False
            else:
                print("✅ Python linting passed!")
        except Exception as e:
            print(f"❌ Error running Python linting: {e}")
            success = False
        
        # Run black formatting check
        try:
            result = subprocess.run(
                ["python", "-m", "black", "--check", "."],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print("❌ Python formatting check failed:")
                print(result.stdout)
                success = False
            else:
                print("✅ Python formatting check passed!")
        except Exception as e:
            print(f"❌ Error running Python formatting check: {e}")
            success = False
        
        # Frontend linting
        print("  📝 Checking JavaScript/React code...")
        os.chdir(FRONTEND_DIR)
        
        try:
            result = subprocess.run(
                ["npm", "run", "lint"],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print("❌ Frontend linting failed:")
                print(result.stdout)
                success = False
            else:
                print("✅ Frontend linting passed!")
        except Exception as e:
            print(f"❌ Error running frontend linting: {e}")
            success = False
        
        return success
    
    def run_security_checks(self) -> bool:
        """Run security vulnerability checks"""
        print("🔒 Running Security Checks...")
        
        success = True
        
        # Backend security check
        print("  🐍 Checking Python dependencies...")
        os.chdir(BACKEND_DIR)
        
        try:
            result = subprocess.run(
                ["python", "-m", "safety", "check"],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print("❌ Python security check failed:")
                print(result.stdout)
                success = False
            else:
                print("✅ Python security check passed!")
        except Exception as e:
            print(f"❌ Error running Python security check: {e}")
            success = False
        
        # Frontend security check
        print("  📦 Checking Node.js dependencies...")
        os.chdir(FRONTEND_DIR)
        
        try:
            result = subprocess.run(
                ["npm", "audit", "--audit-level=moderate"],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print("❌ Frontend security check failed:")
                print(result.stdout)
                success = False
            else:
                print("✅ Frontend security check passed!")
        except Exception as e:
            print(f"❌ Error running frontend security check: {e}")
            success = False
        
        return success
    
    def generate_report(self) -> None:
        """Generate test report"""
        duration = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("📊 TEST REPORT")
        print("="*60)
        
        total_passed = sum(r["passed"] for r in self.results.values())
        total_failed = sum(r["failed"] for r in self.results.values())
        total_skipped = sum(r["skipped"] for r in self.results.values())
        total_tests = total_passed + total_failed + total_skipped
        
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {total_passed}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️  Skipped: {total_skipped}")
        
        if total_tests > 0:
            success_rate = (total_passed / total_tests) * 100
            print(f"📊 Success Rate: {success_rate:.1f}%")
        
        print("\nDetailed Results:")
        for category, results in self.results.items():
            if results["passed"] + results["failed"] + results["skipped"] > 0:
                print(f"  {category.title()}:")
                print(f"    ✅ Passed: {results['passed']}")
                print(f"    ❌ Failed: {results['failed']}")
                print(f"    ⏭️  Skipped: {results['skipped']}")
                
                if results["errors"]:
                    print(f"    🚨 Errors: {len(results['errors'])}")
        
        # Save report to file
        report_data = {
            "timestamp": time.time(),
            "duration": duration,
            "results": self.results,
            "summary": {
                "total": total_tests,
                "passed": total_passed,
                "failed": total_failed,
                "skipped": total_skipped,
                "success_rate": success_rate if total_tests > 0 else 0
            }
        }
        
        with open(PROJECT_ROOT / "test-report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Full report saved to: {PROJECT_ROOT / 'test-report.json'}")
    
    def _parse_pytest_output(self, output: str, category: str) -> None:
        """Parse pytest output to extract test results"""
        lines = output.split('\n')
        for line in lines:
            if "passed" in line and "failed" in line:
                # Parse line like: "5 passed, 2 failed, 1 skipped"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed" and i > 0:
                        self.results[category]["passed"] = int(parts[i-1])
                    elif part == "failed" and i > 0:
                        self.results[category]["failed"] = int(parts[i-1])
                    elif part == "skipped" and i > 0:
                        self.results[category]["skipped"] = int(parts[i-1])
    
    def _parse_jest_output(self, output: str, category: str) -> None:
        """Parse Jest output to extract test results"""
        lines = output.split('\n')
        for line in lines:
            if "Tests:" in line:
                # Parse line like: "Tests: 5 passed, 2 failed, 1 skipped, 8 total"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed," and i > 0:
                        self.results[category]["passed"] = int(parts[i-1])
                    elif part == "failed," and i > 0:
                        self.results[category]["failed"] = int(parts[i-1])
                    elif part == "skipped," and i > 0:
                        self.results[category]["skipped"] = int(parts[i-1])


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="TikTok Automation Test Runner")
    parser.add_argument(
        "--type",
        choices=["all", "unit", "integration", "e2e", "api", "automation"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--component",
        choices=["all", "backend", "frontend"],
        default="all",
        help="Component to test"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--lint",
        action="store_true",
        help="Run linting checks"
    )
    parser.add_argument(
        "--security",
        action="store_true",
        help="Run security checks"
    )
    parser.add_argument(
        "--no-report",
        action="store_true",
        help="Skip generating test report"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    success = True
    
    print("🚀 Starting TikTok Automation Test Suite")
    print(f"📋 Test Type: {args.type}")
    print(f"🎯 Component: {args.component}")
    print("-" * 60)
    
    # Run linting if requested
    if args.lint:
        if not runner.run_linting():
            success = False
    
    # Run security checks if requested
    if args.security:
        if not runner.run_security_checks():
            success = False
    
    # Run tests based on component and type
    if args.component in ["all", "backend"]:
        if args.type in ["all", "unit", "api", "automation"]:
            if not runner.run_backend_tests(args.type, args.verbose):
                success = False
        
        if args.type in ["all", "integration"]:
            if not runner.run_integration_tests(args.verbose):
                success = False
    
    if args.component in ["all", "frontend"]:
        if args.type in ["all", "unit"]:
            if not runner.run_frontend_tests(args.type, args.verbose):
                success = False
    
    if args.type in ["all", "e2e"]:
        if not runner.run_e2e_tests(args.verbose):
            success = False
    
    # Generate report
    if not args.no_report:
        runner.generate_report()
    
    # Exit with appropriate code
    if success:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()

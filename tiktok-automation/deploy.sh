#!/bin/bash

# TikTok Automation Deployment Script
# This script handles deployment for development, staging, and production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="tiktok-automation"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "All requirements satisfied"
}

create_env_file() {
    log_info "Creating environment file..."
    
    if [ ! -f "$ENV_FILE" ]; then
        cat > "$ENV_FILE" << EOF
# TikTok Automation Environment Configuration

# Security
SECRET_KEY=$(openssl rand -hex 32)
REDIS_PASSWORD=$(openssl rand -hex 16)
GRAFANA_PASSWORD=admin

# Application
DEBUG=False
LOG_LEVEL=INFO

# Database
DATABASE_URL=sqlite:///./data/tiktok_automation.db

# CORS Origins (adjust for your domain)
CORS_ORIGINS=["http://localhost:3000","https://yourdomain.com"]

# Monitoring
ENABLE_MONITORING=false

# Proxy Settings (optional)
DEFAULT_PROXY_TIMEOUT=30
MAX_PROXY_RETRIES=3

# Automation Limits
MAX_FOLLOWS_PER_DAY=100
MAX_UNFOLLOWS_PER_DAY=50
MIN_DELAY_BETWEEN_ACTIONS=30
MAX_DELAY_BETWEEN_ACTIONS=120

# Production Settings
WORKERS=4
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=50
TIMEOUT=120
KEEPALIVE=5
EOF
        log_success "Environment file created: $ENV_FILE"
        log_warning "Please review and update the environment variables in $ENV_FILE"
    else
        log_info "Environment file already exists: $ENV_FILE"
    fi
}

create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p data
    mkdir -p logs
    mkdir -p profiles
    mkdir -p nginx/ssl
    mkdir -p monitoring
    
    # Set proper permissions
    chmod 755 data logs profiles
    
    log_success "Directories created"
}

build_images() {
    log_info "Building Docker images..."
    
    # Build backend
    log_info "Building backend image..."
    docker build -t ${PROJECT_NAME}-backend ./backend
    
    # Build frontend
    log_info "Building frontend image..."
    docker build -t ${PROJECT_NAME}-frontend ./frontend
    
    log_success "Docker images built successfully"
}

deploy_development() {
    log_info "Deploying for development environment..."
    
    # Stop existing containers
    docker-compose down --remove-orphans
    
    # Start services
    docker-compose up -d backend frontend redis
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 10
    
    # Check health
    check_health
    
    log_success "Development deployment completed"
    log_info "Frontend: http://localhost:3000"
    log_info "Backend API: http://localhost:8000"
    log_info "API Docs: http://localhost:8000/docs"
}

deploy_production() {
    log_info "Deploying for production environment..."
    
    # Ensure production environment
    export COMPOSE_PROFILES=production
    
    # Stop existing containers
    docker-compose down --remove-orphans
    
    # Start all services including nginx
    docker-compose --profile production up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 15
    
    # Check health
    check_health
    
    log_success "Production deployment completed"
    log_info "Application: http://localhost"
    log_info "API: http://localhost/api"
}

deploy_monitoring() {
    log_info "Deploying with monitoring..."
    
    # Create monitoring configuration
    create_monitoring_config
    
    # Start with monitoring profile
    export COMPOSE_PROFILES=monitoring
    docker-compose --profile monitoring up -d
    
    log_success "Monitoring deployment completed"
    log_info "Prometheus: http://localhost:9090"
    log_info "Grafana: http://localhost:3001 (admin/admin)"
}

create_monitoring_config() {
    log_info "Creating monitoring configuration..."
    
    # Prometheus config
    mkdir -p monitoring
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'tiktok-automation-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
EOF

    # Grafana datasource
    mkdir -p monitoring/grafana/datasources
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    log_success "Monitoring configuration created"
}

check_health() {
    log_info "Checking service health..."
    
    # Check backend
    for i in {1..30}; do
        if curl -f http://localhost:8000/api/v1/system/status &> /dev/null; then
            log_success "Backend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Backend health check failed"
            return 1
        fi
        sleep 2
    done
    
    # Check frontend
    for i in {1..30}; do
        if curl -f http://localhost:3000 &> /dev/null; then
            log_success "Frontend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Frontend health check failed"
            return 1
        fi
        sleep 2
    done
}

show_logs() {
    log_info "Showing application logs..."
    docker-compose logs -f --tail=100
}

stop_services() {
    log_info "Stopping all services..."
    docker-compose down --remove-orphans
    log_success "All services stopped"
}

cleanup() {
    log_info "Cleaning up Docker resources..."
    
    # Stop containers
    docker-compose down --remove-orphans
    
    # Remove images
    docker rmi ${PROJECT_NAME}-backend ${PROJECT_NAME}-frontend 2>/dev/null || true
    
    # Remove unused volumes (be careful!)
    read -p "Remove Docker volumes? This will delete all data! (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v
        log_warning "Docker volumes removed"
    fi
    
    # Clean up unused Docker resources
    docker system prune -f
    
    log_success "Cleanup completed"
}

backup_data() {
    log_info "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if [ -f "data/tiktok_automation.db" ]; then
        cp data/tiktok_automation.db "$BACKUP_DIR/"
        log_success "Database backed up"
    fi
    
    # Backup profiles
    if [ -d "profiles" ]; then
        cp -r profiles "$BACKUP_DIR/"
        log_success "Profiles backed up"
    fi
    
    # Backup configuration
    if [ -f "$ENV_FILE" ]; then
        cp "$ENV_FILE" "$BACKUP_DIR/"
        log_success "Configuration backed up"
    fi
    
    # Create archive
    tar -czf "${BACKUP_DIR}.tar.gz" -C backups "$(basename "$BACKUP_DIR")"
    rm -rf "$BACKUP_DIR"
    
    log_success "Backup created: ${BACKUP_DIR}.tar.gz"
}

restore_data() {
    if [ -z "$1" ]; then
        log_error "Please specify backup file: ./deploy.sh restore backup_file.tar.gz"
        exit 1
    fi
    
    BACKUP_FILE="$1"
    if [ ! -f "$BACKUP_FILE" ]; then
        log_error "Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    log_info "Restoring from backup: $BACKUP_FILE"
    
    # Stop services
    docker-compose down
    
    # Extract backup
    TEMP_DIR=$(mktemp -d)
    tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"
    
    # Restore files
    BACKUP_CONTENT=$(ls "$TEMP_DIR")
    cp -r "$TEMP_DIR/$BACKUP_CONTENT"/* .
    
    # Cleanup
    rm -rf "$TEMP_DIR"
    
    log_success "Data restored from backup"
    log_info "You can now start the services with: ./deploy.sh dev"
}

show_status() {
    log_info "Service Status:"
    docker-compose ps
    
    echo
    log_info "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

show_help() {
    echo "TikTok Automation Deployment Script"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  dev         Deploy for development (default)"
    echo "  prod        Deploy for production with nginx"
    echo "  monitoring  Deploy with monitoring (Prometheus + Grafana)"
    echo "  build       Build Docker images only"
    echo "  logs        Show application logs"
    echo "  status      Show service status and resource usage"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  backup      Create backup of data and configuration"
    echo "  restore     Restore from backup file"
    echo "  cleanup     Clean up Docker resources"
    echo "  help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0 dev                    # Deploy for development"
    echo "  $0 prod                   # Deploy for production"
    echo "  $0 backup                 # Create backup"
    echo "  $0 restore backup.tar.gz  # Restore from backup"
}

# Main script logic
main() {
    case "${1:-dev}" in
        "dev"|"development")
            check_requirements
            create_env_file
            create_directories
            build_images
            deploy_development
            ;;
        "prod"|"production")
            check_requirements
            create_env_file
            create_directories
            build_images
            deploy_production
            ;;
        "monitoring")
            check_requirements
            create_env_file
            create_directories
            build_images
            deploy_monitoring
            ;;
        "build")
            check_requirements
            build_images
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            deploy_development
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

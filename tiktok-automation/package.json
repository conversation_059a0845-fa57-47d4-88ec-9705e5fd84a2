{"name": "tiktok-automation-desktop", "version": "1.0.0", "description": "TikTok Automation Desktop App with Antidetect Browser", "main": "electron/main.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run start:backend\" \"npm run start:electron\"", "start:backend": "cd backend && python -m uvicorn main:app --reload --port 8000", "start:electron": "wait-on http://localhost:8000 && cd electron && npm start", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\" \"npm run dev:electron\"", "start:frontend": "cd frontend && npm start", "dev:electron": "wait-on http://localhost:8000 http://localhost:3000 && cd electron && npm run dev", "build": "python scripts/build.py", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && python -m PyInstaller --onefile main.py", "build:win": "python scripts/build.py --platform windows", "build:mac": "python scripts/build.py --platform macos", "build:linux": "python scripts/build.py --platform linux", "build:all": "python scripts/build.py --platform all", "dist": "cd electron && npm run build", "dist:win": "cd electron && npm run build-win", "dist:mac": "cd electron && npm run build-mac", "dist:linux": "cd electron && npm run build-linux", "clean": "python scripts/build.py --clean", "setup": "npm run install-all", "test": "jest", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && python -m pytest", "test:integration": "python scripts/run-tests.py --type integration", "test:e2e": "python scripts/run-tests.py --type e2e", "lint": "eslint src/", "security": "python scripts/run-tests.py --security", "deploy": "python scripts/deploy.py", "deploy:docker": "python scripts/deploy.py --method docker", "deploy:cloud": "python scripts/deploy.py --method cloud", "release": "python scripts/release.py", "release:major": "python scripts/release.py --type major", "release:minor": "python scripts/release.py --type minor", "release:patch": "python scripts/release.py --type patch", "format": "prettier --write src/"}, "keywords": ["tiktok", "automation", "antidetect", "browser", "desktop", "electron"], "author": "TikTok Automation Team", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "jest": "^29.7.0"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.7", "axios": "^1.6.2", "ws": "^8.16.0"}, "build": {"appId": "com.tiktokautomation.desktop", "productName": "TikTok Automation", "directories": {"output": "dist"}, "files": ["electron/**/*", "frontend/build/**/*", "backend/**/*", "!backend/__pycache__/**/*", "!backend/.pytest_cache/**/*", "!backend/venv/**/*"], "extraResources": [{"from": "backend/", "to": "backend/", "filter": ["**/*", "!__pycache__/**/*", "!.pytest_cache/**/*", "!venv/**/*", "!.env*"]}], "win": {"target": "nsis", "icon": "electron/assets/icon.ico"}, "mac": {"target": "dmg", "icon": "electron/assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "electron/assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}
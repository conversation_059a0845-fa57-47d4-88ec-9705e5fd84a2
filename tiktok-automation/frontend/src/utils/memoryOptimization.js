/**
 * Memory optimization utilities for frontend
 */

/**
 * Debounce function to limit function calls
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Throttle function to limit function calls
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return (...args) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Memory-efficient cache with LRU eviction
 */
export class LRUCache {
  constructor(maxSize = 100) {
    this.maxSize = maxSize;
    this.cache = new Map();
  }

  get(key) {
    if (this.cache.has(key)) {
      // Move to end (most recently used)
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used (first item)
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

/**
 * Weak reference manager for cleanup
 */
export class WeakReferenceManager {
  constructor() {
    this.refs = new WeakMap();
    this.cleanupCallbacks = new Map();
  }

  register(obj, cleanup) {
    const id = Math.random().toString(36).substr(2, 9);
    this.refs.set(obj, id);
    if (cleanup) {
      this.cleanupCallbacks.set(id, cleanup);
    }
    return id;
  }

  cleanup(obj) {
    const id = this.refs.get(obj);
    if (id && this.cleanupCallbacks.has(id)) {
      const cleanup = this.cleanupCallbacks.get(id);
      cleanup();
      this.cleanupCallbacks.delete(id);
    }
  }

  cleanupAll() {
    for (const cleanup of this.cleanupCallbacks.values()) {
      try {
        cleanup();
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    }
    this.cleanupCallbacks.clear();
  }
}

/**
 * Memory usage monitor
 */
export class MemoryMonitor {
  constructor() {
    this.measurements = [];
    this.maxMeasurements = 100;
    this.isSupported = 'memory' in performance;
  }

  measure() {
    if (!this.isSupported) return null;

    const memory = performance.memory;
    const measurement = {
      timestamp: Date.now(),
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    };

    this.measurements.push(measurement);
    
    // Keep only recent measurements
    if (this.measurements.length > this.maxMeasurements) {
      this.measurements.shift();
    }

    return measurement;
  }

  getStats() {
    if (!this.isSupported || this.measurements.length === 0) {
      return null;
    }

    const latest = this.measurements[this.measurements.length - 1];
    const usagePercent = (latest.usedJSHeapSize / latest.jsHeapSizeLimit) * 100;

    return {
      current: latest,
      usagePercent,
      trend: this.calculateTrend(),
      isSupported: this.isSupported
    };
  }

  calculateTrend() {
    if (this.measurements.length < 2) return 0;

    const recent = this.measurements.slice(-10);
    const first = recent[0].usedJSHeapSize;
    const last = recent[recent.length - 1].usedJSHeapSize;
    
    return ((last - first) / first) * 100;
  }

  shouldCleanup(threshold = 80) {
    const stats = this.getStats();
    return stats && stats.usagePercent > threshold;
  }
}

/**
 * Image optimization utilities
 */
export const imageOptimization = {
  /**
   * Create optimized image URL with size parameters
   */
  getOptimizedUrl(url, width, height, quality = 80) {
    if (!url) return '';
    
    // Add query parameters for image optimization
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}w=${width}&h=${height}&q=${quality}&f=webp`;
  },

  /**
   * Lazy load image with intersection observer
   */
  lazyLoad(img, src, options = {}) {
    const {
      threshold = 0.1,
      rootMargin = '50px',
      placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGM0Y0RjYiLz48L3N2Zz4='
    } = options;

    img.src = placeholder;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const image = entry.target;
            image.src = src;
            observer.unobserve(image);
          }
        });
      },
      { threshold, rootMargin }
    );

    observer.observe(img);
    return observer;
  },

  /**
   * Preload critical images
   */
  preload(urls) {
    return Promise.all(
      urls.map(url => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(url);
          img.onerror = reject;
          img.src = url;
        });
      })
    );
  }
};

/**
 * Component cleanup utilities
 */
export const componentCleanup = {
  /**
   * Create cleanup function for event listeners
   */
  createEventCleanup() {
    const listeners = [];

    const addListener = (element, event, handler, options) => {
      element.addEventListener(event, handler, options);
      listeners.push(() => element.removeEventListener(event, handler, options));
    };

    const cleanup = () => {
      listeners.forEach(remove => remove());
      listeners.length = 0;
    };

    return { addListener, cleanup };
  },

  /**
   * Create cleanup function for timeouts and intervals
   */
  createTimerCleanup() {
    const timers = [];

    const setTimeout = (callback, delay) => {
      const id = window.setTimeout(callback, delay);
      timers.push(() => window.clearTimeout(id));
      return id;
    };

    const setInterval = (callback, delay) => {
      const id = window.setInterval(callback, delay);
      timers.push(() => window.clearInterval(id));
      return id;
    };

    const cleanup = () => {
      timers.forEach(clear => clear());
      timers.length = 0;
    };

    return { setTimeout, setInterval, cleanup };
  },

  /**
   * Create cleanup function for abort controllers
   */
  createAbortCleanup() {
    const controllers = [];

    const createController = () => {
      const controller = new AbortController();
      controllers.push(controller);
      return controller;
    };

    const cleanup = () => {
      controllers.forEach(controller => controller.abort());
      controllers.length = 0;
    };

    return { createController, cleanup };
  }
};

/**
 * Data structure optimization
 */
export const dataOptimization = {
  /**
   * Chunk large arrays for processing
   */
  chunkArray(array, chunkSize = 100) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  },

  /**
   * Process large datasets in batches
   */
  async processBatches(data, processor, batchSize = 100, delay = 0) {
    const chunks = this.chunkArray(data, batchSize);
    const results = [];

    for (const chunk of chunks) {
      const batchResults = await processor(chunk);
      results.push(...batchResults);
      
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return results;
  },

  /**
   * Flatten nested objects for better performance
   */
  flattenObject(obj, prefix = '', result = {}) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          this.flattenObject(obj[key], newKey, result);
        } else {
          result[newKey] = obj[key];
        }
      }
    }
    return result;
  },

  /**
   * Remove circular references from objects
   */
  removeCircularReferences(obj, seen = new WeakSet()) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (seen.has(obj)) {
      return '[Circular Reference]';
    }

    seen.add(obj);

    if (Array.isArray(obj)) {
      return obj.map(item => this.removeCircularReferences(item, seen));
    }

    const result = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result[key] = this.removeCircularReferences(obj[key], seen);
      }
    }

    return result;
  }
};

/**
 * Performance measurement utilities
 */
export const performanceMeasurement = {
  /**
   * Measure function execution time
   */
  measureTime(name, fn) {
    return async (...args) => {
      const start = performance.now();
      const result = await fn(...args);
      const end = performance.now();
      
      console.log(`${name} took ${(end - start).toFixed(2)}ms`);
      return result;
    };
  },

  /**
   * Create performance observer
   */
  createObserver(callback) {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return null;
    }

    const observer = new PerformanceObserver(callback);
    
    try {
      observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
    } catch (error) {
      console.warn('PerformanceObserver observe failed:', error);
    }

    return observer;
  },

  /**
   * Mark performance milestones
   */
  mark(name) {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  },

  /**
   * Measure between marks
   */
  measure(name, startMark, endMark) {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
      } catch (error) {
        console.warn('Performance measure failed:', error);
      }
    }
  }
};

// Global instances
export const globalCache = new LRUCache(200);
export const globalWeakRefs = new WeakReferenceManager();
export const globalMemoryMonitor = new MemoryMonitor();

// Auto cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    globalWeakRefs.cleanupAll();
    globalCache.clear();
  });
}

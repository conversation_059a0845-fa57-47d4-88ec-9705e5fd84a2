/**
 * TikTok Automation Dashboard - Main management interface
 */

import React, { useState, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  FiPlus,
  FiPlay,
  FiStopCircle,
  FiUsers,
  FiActivity,
  FiTarget,
  FiTrendingUp
} from 'react-icons/fi';

// Components
import StatsCard from '../components/Dashboard/StatsCard';
import ProfileTable from '../components/Dashboard/ProfileTable';
import ProfileForm from '../components/Dashboard/ProfileForm';
import EditProfileForm from '../components/Dashboard/EditProfileForm';
import DeleteConfirmDialog from '../components/Dashboard/DeleteConfirmDialog';
import LogMonitor from '../components/Dashboard/LogMonitor';
import FollowSettings from '../components/Dashboard/FollowSettings';
import TasksOverview from '../components/Dashboard/TasksOverview';
import RecentActivity from '../components/Dashboard/RecentActivity';

// Services
import profileService from '../services/profileService';

const Dashboard = () => {
  const { setPageTitle, setPageSubtitle } = useOutletContext();

  // State management
  const [profiles, setProfiles] = useState([]);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedProfiles, setSelectedProfiles] = useState([]);
  const [editingProfile, setEditingProfile] = useState(null);
  const [deletingProfile, setDeletingProfile] = useState(null);

  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);

  // Set page title and subtitle
  useEffect(() => {
    setPageTitle('Dashboard');
    setPageSubtitle('Welcome to TikTok Automation');
  }, [setPageTitle, setPageSubtitle]);

  // Load data from backend
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load profiles from backend
        await profileService.loadProfiles();
        setProfiles(profileService.getProfiles());

        // Mock logs for now - TODO: Load from backend
        const mockLogs = [
          { id: 1, time: '09:45:30', level: 'INFO', message: 'System started successfully' },
          { id: 2, time: '09:45:31', level: 'INFO', message: 'Backend connection established' },
          { id: 3, time: '09:45:32', level: 'INFO', message: 'Profile service initialized' },
          { id: 4, time: '09:45:33', level: 'INFO', message: 'Ready for automation tasks' }
        ];
        setLogs(mockLogs);

      } catch (error) {
        console.error('Failed to load data:', error);
        // Add error log
        setLogs(prev => [...prev, {
          id: Date.now(),
          time: new Date().toLocaleTimeString(),
          level: 'ERROR',
          message: `Failed to load profiles: ${error.message}`
        }]);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Set up profile service listeners
    const handleProfileEvent = (event, data) => {
      const timestamp = new Date().toLocaleTimeString();
      let logMessage = '';

      switch (event) {
        case 'profiles_loaded':
          setProfiles(data);
          logMessage = `Loaded ${data.length} profiles from backend`;
          break;
        case 'profile_created':
          setProfiles(profileService.getProfiles());
          logMessage = `Profile "${data.username}" created successfully`;
          break;
        case 'profile_updated':
          setProfiles(profileService.getProfiles());
          logMessage = `Profile "${data.username}" updated`;
          break;
        case 'profile_deleted':
          setProfiles(profileService.getProfiles());
          logMessage = `Profile deleted (ID: ${data})`;
          break;
        case 'login_started':
          logMessage = `Login started for profile ID: ${data}`;
          break;
        case 'login_completed':
          logMessage = `Login completed for profile ID: ${data}`;
          break;
        case 'automation_started':
          logMessage = `Automation started for profile ID: ${data}`;
          break;
        case 'automation_paused':
          logMessage = `Automation paused for profile ID: ${data}`;
          break;
        case 'automation_stopped':
          logMessage = `Automation stopped for profile ID: ${data}`;
          break;
        default:
          return;
      }

      if (logMessage) {
        setLogs(prev => [...prev, {
          id: Date.now(),
          time: timestamp,
          level: 'INFO',
          message: logMessage
        }]);
      }
    };

    profileService.addListener(handleProfileEvent);

    // Cleanup
    return () => {
      profileService.removeListener(handleProfileEvent);
    };
  }, []);

  // Handle profile actions
  const handleProfileAction = async (profileId, action) => {
    try {
      let result;

      switch (action) {
        case 'login':
          result = await profileService.startLogin(profileId);
          break;
        case 'complete':
          result = await profileService.completeLogin(profileId);
          break;
        case 'start':
          result = await profileService.startFollowTask(profileId);
          break;
        case 'pause':
          result = await profileService.pauseFollowTask(profileId);
          break;
        case 'stop':
          result = await profileService.stopFollowTask(profileId);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      console.log(`Action ${action} for profile ${profileId}:`, result);
    } catch (error) {
      console.error(`Failed to execute action ${action} for profile ${profileId}:`, error);

      // Add error log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Failed to ${action} profile ${profileId}: ${error.message}`
      }]);
    }
  };

  const handleProfileSelect = (profileId, isSelected) => {
    setSelectedProfiles(prev => {
      if (isSelected) {
        return [...prev, profileId];
      } else {
        return prev.filter(id => id !== profileId);
      }
    });
  };

  const handleSelectAll = (isSelected) => {
    if (isSelected) {
      setSelectedProfiles(profiles.map(p => p.id));
    } else {
      setSelectedProfiles([]);
    }
  };

  const handleBulkAction = async (action) => {
    if (selectedProfiles.length === 0) {
      return;
    }

    try {
      console.log(`Bulk action ${action} for profiles:`, selectedProfiles);
      const results = await profileService.bulkAction(selectedProfiles, action);

      // Log results
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'INFO',
        message: `Bulk ${action}: ${successCount} successful, ${failCount} failed`
      }]);

    } catch (error) {
      console.error(`Failed to execute bulk action ${action}:`, error);
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Bulk ${action} failed: ${error.message}`
      }]);
    }
  };

  const handleCreateProfile = async (profileData) => {
    try {
      console.log('Creating profile:', profileData);

      // Create profile using service
      await profileService.createProfile(profileData);
      setShowProfileForm(false);

    } catch (error) {
      console.error('Failed to create profile:', error);

      // Add error log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Failed to create profile "${profileData.profileName}": ${error.message}`
      }]);

      // Show error to user (you might want to add a toast notification here)
      alert(`Failed to create profile: ${error.message}`);
    }
  };

  const handleEditProfile = (profile) => {
    setEditingProfile(profile);
    setShowEditForm(true);
  };

  const handleUpdateProfile = async (profileId, profileData) => {
    try {
      console.log('Updating profile:', profileId, profileData);

      // Update profile using service
      await profileService.updateProfile(profileId, profileData);
      setShowEditForm(false);
      setEditingProfile(null);

    } catch (error) {
      console.error('Failed to update profile:', error);

      // Add error log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Failed to update profile: ${error.message}`
      }]);

      // Show error to user
      alert(`Failed to update profile: ${error.message}`);
    }
  };

  const handleDeleteProfile = (profile) => {
    setDeletingProfile(profile);
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async (profileId) => {
    try {
      console.log('Deleting profile:', profileId);

      // Delete profile using service
      await profileService.deleteProfile(profileId);
      setShowDeleteDialog(false);
      setDeletingProfile(null);

      // Add success log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'INFO',
        message: `Profile deleted successfully`
      }]);

    } catch (error) {
      console.error('Failed to delete profile:', error);

      // Add error log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Failed to delete profile: ${error.message}`
      }]);

      // Show error to user
      alert(`Failed to delete profile: ${error.message}`);
    }
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  const handleExportLogs = () => {
    console.log('Exporting logs...');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Calculate stats
  const totalProfiles = profiles.length;
  const activeProfiles = profiles.filter(p => p.status === 'running').length;
  const totalFollows = profiles.reduce((sum, p) => sum + (p.followsToday || 0), 0);
  const successRate = totalProfiles > 0 ? Math.round((activeProfiles / totalProfiles) * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Profiles"
          value={totalProfiles}
          icon={FiUsers}
          color="blue"
          trend="+12%"
        />
        <StatsCard
          title="Active Tasks"
          value={activeProfiles}
          icon={FiActivity}
          color="green"
          trend="+5%"
        />
        <StatsCard
          title="Follows Today"
          value={totalFollows}
          icon={FiTarget}
          color="purple"
          trend="+23%"
        />
        <StatsCard
          title="Success Rate"
          value={`${successRate}%`}
          icon={FiTrendingUp}
          color="orange"
          trend="+8%"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Profile Management */}
        <div className="lg:col-span-2 space-y-6">
          {/* Profile Controls */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">
                  Profile Management ({totalProfiles})
                </h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowProfileForm(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                  >
                    <FiPlus className="w-4 h-4" />
                    <span>New Profile</span>
                  </button>
                  <button
                    onClick={() => handleBulkAction('start')}
                    disabled={selectedProfiles.length === 0}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiPlay className="w-4 h-4" />
                    <span>Start All</span>
                  </button>
                  <button
                    onClick={() => handleBulkAction('stop')}
                    disabled={selectedProfiles.length === 0}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiStopCircle className="w-4 h-4" />
                    <span>Stop All</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Profile Table */}
            <ProfileTable
              profiles={profiles}
              selectedProfiles={selectedProfiles}
              onProfileSelect={handleProfileSelect}
              onProfileAction={handleProfileAction}
              onSelectAll={handleSelectAll}
              onEditProfile={handleEditProfile}
              onDeleteProfile={handleDeleteProfile}
            />
          </div>

          {/* Tasks Overview */}
          <TasksOverview />
        </div>

        {/* Right Column - Sidebar Content */}
        <div className="space-y-6">
          {/* Follow Settings */}
          <FollowSettings />

          {/* Recent Activity */}
          <RecentActivity activities={logs.slice(-5)} />

          {/* Log Monitor */}
          <LogMonitor
            logs={logs}
            onClearLogs={handleClearLogs}
            onExportLogs={handleExportLogs}
          />
        </div>
      </div>

      {/* Profile Form Modal */}
      <ProfileForm
        isOpen={showProfileForm}
        onClose={() => setShowProfileForm(false)}
        onSubmit={handleCreateProfile}
      />

      {/* Edit Profile Form Modal */}
      <EditProfileForm
        isOpen={showEditForm}
        onClose={() => {
          setShowEditForm(false);
          setEditingProfile(null);
        }}
        onSubmit={handleUpdateProfile}
        profile={editingProfile}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false);
          setDeletingProfile(null);
        }}
        onConfirm={handleConfirmDelete}
        profile={deletingProfile}
      />
    </div>
  );
};

export default Dashboard;

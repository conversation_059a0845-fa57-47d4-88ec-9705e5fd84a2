/**
 * Main Layout Component
 */

import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import Sidebar from './Sidebar';
import Header from './Header';
import { useWebSocket } from '../../hooks/useWebSocket';

const Layout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [pageTitle, setPageTitle] = useState('Dashboard');
  const [pageSubtitle, setPageSubtitle] = useState('Welcome to TikTok Automation');

  // WebSocket connection for real-time updates
  const { isConnected, lastMessage } = useWebSocket();

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setSidebarCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Check initial size

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update tray status
  useEffect(() => {
    if (window.electronAPI) {
      window.electronAPI.updateTrayStatus({
        backend: isConnected ? 'Connected' : 'Disconnected',
        runningTasks: 0 // This would come from your state management
      });
    }
  }, [isConnected]);

  // Handle menu actions from Electron
  useEffect(() => {
    if (window.electronAPI) {
      window.electronAPI.onMenuAction((action, data) => {
        switch (action) {
          case 'new-profile':
            // Navigate to new profile page
            window.location.href = '/profiles/new';
            break;
          case 'settings':
            window.location.href = '/settings';
            break;
          case 'view-logs':
            // Open logs modal or navigate to logs page
            break;
          default:
            console.log('Unhandled menu action:', action);
        }
      });
    }

    return () => {
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners('menu-action');
      }
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar 
        isCollapsed={sidebarCollapsed} 
        onToggle={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header 
          title={pageTitle}
          subtitle={pageSubtitle}
        />

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="p-6"
          >
            <Outlet context={{ setPageTitle, setPageSubtitle }} />
          </motion.div>
        </main>

        {/* Status Bar */}
        <div className="bg-gray-800 text-white px-6 py-2 text-sm flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span>Backend: {isConnected ? 'Connected' : 'Disconnected'}</span>
            </span>
            
            {lastMessage && (
              <span className="text-gray-300">
                Last update: {new Date(lastMessage.timestamp * 1000).toLocaleTimeString()}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <span>TikTok Automation v1.0.0</span>
            {window.electronAPI && (
              <span className="text-gray-400">Desktop App</span>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for mobile sidebar */}
      {!sidebarCollapsed && window.innerWidth < 1024 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
};

export default Layout;

/**
 * Real-time monitoring dashboard component
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Cpu,
  HardDrive,
  Zap,
  Eye,
  EyeOff,
  Pause,
  Play,
  Settings
} from 'lucide-react';

const RealTimeMonitor = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [metrics, setMetrics] = useState({});
  const [alerts, setAlerts] = useState([]);
  const [logs, setLogs] = useState([]);
  const [systemStatus, setSystemStatus] = useState({});
  const [metricsHistory, setMetricsHistory] = useState([]);
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [selectedMetrics, setSelectedMetrics] = useState(['cpu_usage_percent', 'memory_usage_percent']);
  const [autoScroll, setAutoScroll] = useState(true);
  
  const wsRef = useRef(null);
  const logsEndRef = useRef(null);
  const maxHistoryPoints = 50;

  useEffect(() => {
    connectWebSocket();
    fetchInitialData();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, autoScroll]);

  const connectWebSocket = () => {
    const wsUrl = `ws://localhost:8000/ws`;
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = () => {
      setIsConnected(true);
      console.log('WebSocket connected');
      
      // Subscribe to monitoring updates
      wsRef.current.send(JSON.stringify({
        type: 'subscribe',
        subscription: 'monitoring'
      }));
    };

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleWebSocketMessage(data);
    };

    wsRef.current.onclose = () => {
      setIsConnected(false);
      console.log('WebSocket disconnected');
      
      // Attempt to reconnect after 3 seconds
      setTimeout(connectWebSocket, 3000);
    };

    wsRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  };

  const handleWebSocketMessage = (data) => {
    switch (data.type) {
      case 'metrics':
        updateMetrics(data.data);
        break;
      case 'alert':
        addAlert(data.data);
        break;
      case 'log':
        addLog(data.data);
        break;
      case 'system_update':
        updateSystemStatus(data.data);
        break;
      default:
        console.log('Unknown message type:', data.type);
    }
  };

  const updateMetrics = (metricsData) => {
    const newMetrics = {};
    metricsData.metrics.forEach(metric => {
      newMetrics[metric.name] = metric;
    });
    
    setMetrics(prev => ({ ...prev, ...newMetrics }));
    
    // Update metrics history for charts
    setMetricsHistory(prev => {
      const newHistory = [...prev];
      const timestamp = new Date(metricsData.timestamp);
      
      // Create new data point
      const dataPoint = {
        timestamp: timestamp.toLocaleTimeString(),
        time: timestamp.getTime(),
        ...Object.fromEntries(
          metricsData.metrics.map(metric => [metric.name, metric.value])
        )
      };
      
      newHistory.push(dataPoint);
      
      // Keep only last N points
      if (newHistory.length > maxHistoryPoints) {
        newHistory.shift();
      }
      
      return newHistory;
    });
  };

  const addAlert = (alertData) => {
    setAlerts(prev => [alertData, ...prev].slice(0, 20)); // Keep last 20 alerts
  };

  const addLog = (logData) => {
    setLogs(prev => [logData, ...prev].slice(0, 100)); // Keep last 100 logs
  };

  const updateSystemStatus = (statusData) => {
    setSystemStatus(prev => ({ ...prev, ...statusData }));
  };

  const fetchInitialData = async () => {
    try {
      // Fetch system status
      const statusResponse = await fetch('/api/v1/monitoring/system/status');
      const statusData = await statusResponse.json();
      setSystemStatus(statusData);
      
      // Fetch recent alerts
      const alertsResponse = await fetch('/api/v1/monitoring/alerts?limit=10');
      const alertsData = await alertsResponse.json();
      setAlerts(alertsData.alerts);
      
      // Fetch recent logs
      const logsResponse = await fetch('/api/v1/monitoring/logs?limit=50');
      const logsData = await logsResponse.json();
      setLogs(logsData.logs);
      
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  };

  const toggleMonitoring = async () => {
    try {
      const endpoint = isMonitoring ? 'stop' : 'start';
      await fetch(`/api/v1/monitoring/monitoring/${endpoint}`, {
        method: 'POST'
      });
      setIsMonitoring(!isMonitoring);
    } catch (error) {
      console.error('Error toggling monitoring:', error);
    }
  };

  const resolveAlert = async (alertId) => {
    try {
      await fetch(`/api/v1/monitoring/alerts/${alertId}/resolve`, {
        method: 'POST'
      });
      
      setAlerts(prev => 
        prev.map(alert => 
          alert.id === alertId ? { ...alert, resolved: true } : alert
        )
      );
    } catch (error) {
      console.error('Error resolving alert:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getAlertIcon = (level) => {
    switch (level) {
      case 'critical': return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info': return <CheckCircle className="w-5 h-5 text-blue-500" />;
      default: return <CheckCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      case 'info': return 'text-blue-600';
      case 'debug': return 'text-gray-600';
      default: return 'text-gray-800';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">Real-time Monitoring</h1>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMonitoring}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${
              isMonitoring 
                ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                : 'bg-green-100 text-green-700 hover:bg-green-200'
            }`}
          >
            {isMonitoring ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            <span>{isMonitoring ? 'Pause' : 'Start'} Monitoring</span>
          </button>
          
          <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </button>
        </div>
      </div>

      {/* System Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Status</p>
              <p className={`text-2xl font-bold ${getStatusColor(systemStatus.status)}`}>
                {systemStatus.status || 'Unknown'}
              </p>
            </div>
            <Activity className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">CPU Usage</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.cpu_usage_percent?.value?.toFixed(1) || '0'}%
              </p>
            </div>
            <Cpu className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Memory Usage</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.memory_usage_percent?.value?.toFixed(1) || '0'}%
              </p>
            </div>
            <HardDrive className="w-8 h-8 text-purple-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-gray-900">
                {alerts.filter(alert => !alert.resolved).length}
              </p>
            </div>
            <AlertTriangle className="w-8 h-8 text-yellow-500" />
          </div>
        </motion.div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Metrics Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">System Metrics</h3>
            <div className="flex items-center space-x-2">
              <select
                value={selectedMetrics[0]}
                onChange={(e) => setSelectedMetrics([e.target.value, selectedMetrics[1]])}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="cpu_usage_percent">CPU Usage</option>
                <option value="memory_usage_percent">Memory Usage</option>
                <option value="process_memory_mb">Process Memory</option>
              </select>
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={metricsHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey={selectedMetrics[0]}
                stroke="#3B82F6"
                strokeWidth={2}
                dot={false}
              />
              {selectedMetrics[1] && (
                <Line
                  type="monotone"
                  dataKey={selectedMetrics[1]}
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={false}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Alerts Panel */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Alerts</h3>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            <AnimatePresence>
              {alerts.slice(0, 10).map((alert) => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`p-3 rounded-lg border-l-4 ${
                    alert.resolved 
                      ? 'bg-gray-50 border-gray-300' 
                      : alert.level === 'critical' 
                        ? 'bg-red-50 border-red-400'
                        : 'bg-yellow-50 border-yellow-400'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-2">
                      {getAlertIcon(alert.level)}
                      <div>
                        <p className="font-medium text-gray-900">{alert.title}</p>
                        <p className="text-sm text-gray-600">{alert.message}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(alert.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    
                    {!alert.resolved && (
                      <button
                        onClick={() => resolveAlert(alert.id)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        Resolve
                      </button>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {alerts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="w-12 h-12 mx-auto mb-2 text-green-500" />
                <p>No alerts</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Logs Panel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-6 rounded-lg shadow-sm border"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Real-time Logs</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setAutoScroll(!autoScroll)}
              className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
                autoScroll 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {autoScroll ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              <span>Auto-scroll</span>
            </button>
          </div>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-4 h-80 overflow-y-auto font-mono text-sm">
          <AnimatePresence>
            {logs.map((log) => (
              <motion.div
                key={log.id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                className="mb-1"
              >
                <span className="text-gray-400">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
                <span className={`ml-2 font-medium ${getLogLevelColor(log.level)}`}>
                  [{log.level.toUpperCase()}]
                </span>
                <span className="text-gray-300 ml-2">
                  [{log.category}]
                </span>
                <span className="text-white ml-2">{log.message}</span>
              </motion.div>
            ))}
          </AnimatePresence>
          <div ref={logsEndRef} />
        </div>
      </motion.div>
    </div>
  );
};

export default RealTimeMonitor;

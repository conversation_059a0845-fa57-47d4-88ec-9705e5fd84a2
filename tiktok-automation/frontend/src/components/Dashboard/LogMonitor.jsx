/**
 * LogMonitor Component - Real-time log monitoring
 */

import React, { useState, useEffect, useRef } from 'react';
import { FiRefreshCw, FiSettings, FiDownload, FiTrash2 } from 'react-icons/fi';

const LogMonitor = ({ logs, onClearLogs, onExportLogs }) => {
  const [autoScroll, setAutoScroll] = useState(true);
  const [logLevel, setLogLevel] = useState('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const logContainerRef = useRef(null);

  const logLevels = [
    { value: 'ALL', label: 'All Logs' },
    { value: 'INFO', label: 'Info' },
    { value: 'WARNING', label: 'Warning' },
    { value: 'ERROR', label: 'Error' },
    { value: 'DEBUG', label: 'Debug' }
  ];

  // Auto scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, autoScroll]);

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'INFO':
        return 'text-green-400';
      case 'WARNING':
        return 'text-yellow-400';
      case 'ERROR':
        return 'text-red-400';
      case 'DEBUG':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesLevel = logLevel === 'ALL' || log.level === logLevel;
    const matchesSearch = searchTerm === '' || 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.level.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesLevel && matchesSearch;
  });

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
    setAutoScroll(isAtBottom);
  };

  const handleExport = () => {
    const logData = filteredLogs.map(log => 
      `[${log.time}] ${log.level}: ${log.message}`
    ).join('\n');
    
    const blob = new Blob([logData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tiktok-automation-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    if (onExportLogs) {
      onExportLogs();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Log hệ thống ({filteredLogs.length})
          </h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="Refresh"
            >
              <FiRefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={handleExport}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="Export Logs"
            >
              <FiDownload className="w-4 h-4" />
            </button>
            <button
              onClick={onClearLogs}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="Clear Logs"
            >
              <FiTrash2 className="w-4 h-4" />
            </button>
            <button className="p-2 text-gray-500 hover:text-gray-700 transition-colors">
              <FiSettings className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <select
              value={logLevel}
              onChange={(e) => setLogLevel(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {logLevels.map(level => (
                <option key={level.value} value={level.value}>
                  {level.label}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoScroll"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
              className="rounded border-gray-300"
            />
            <label htmlFor="autoScroll" className="text-sm text-gray-600">
              Auto scroll
            </label>
          </div>
        </div>
      </div>

      {/* Log Content */}
      <div className="p-4">
        <div 
          ref={logContainerRef}
          onScroll={handleScroll}
          className="bg-black rounded text-green-400 font-mono text-xs p-3 h-64 overflow-y-auto"
        >
          {filteredLogs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              {searchTerm || logLevel !== 'ALL' ? 'No logs match the current filter' : 'No logs available'}
            </div>
          ) : (
            filteredLogs.map((log) => (
              <div key={log.id} className="mb-1 hover:bg-gray-900 px-1 rounded">
                <span className="text-gray-500">[{log.time}]</span>{' '}
                <span className={`font-semibold ${getLogLevelColor(log.level)}`}>
                  {log.level}
                </span>{' '}
                <span className="text-gray-300">{log.message}</span>
              </div>
            ))
          )}
          
          {/* Auto scroll indicator */}
          {!autoScroll && (
            <div className="sticky bottom-0 text-center py-2">
              <button
                onClick={() => {
                  setAutoScroll(true);
                  if (logContainerRef.current) {
                    logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
                  }
                }}
                className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
              >
                Scroll to bottom
              </button>
            </div>
          )}
        </div>
        
        <div className="mt-3 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Showing {filteredLogs.length} of {logs.length} logs
          </div>
          <button 
            onClick={handleExport}
            className="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <FiDownload className="w-4 h-4" />
            <span>Xuất nhật ký</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default LogMonitor;

import React, { useState, useEffect } from 'react';
import { settingsService } from '../../services/settingsService';

const FollowSettings = () => {
  const [settings, setSettings] = useState({
    // Cài đặt kịch bản
    targetProfileUrl: '',
    videosToWatch: 3,
    watchTimeSeconds: 30,
    
    // Cài đặt giới hạn
    followsPerDay: 50,
    followsPerSession: 10,
    breakTimeMinutes: 3600, // 1 hour in seconds

    // Cài đặt thời gian delay (giây)
    delayBetweenFollowsMin: 30,
    delayBetweenFollowsMax: 60,
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const data = await settingsService.getFollowSettings();
      setSettings(data);
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      await settingsService.saveFollowSettings(settings);
      setMessage('Cài đặt đã được lưu thành công!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setMessage('Lỗi khi lưu cài đặt: ' + error.message);
      setTimeout(() => setMessage(''), 5000);
    } finally {
      setLoading(false);
    }
  };

  const formatBreakTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours} giờ ${minutes} phút`;
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Cài đặt kịch bản Follow</h2>
      
      {message && (
        <div className={`mb-4 p-3 rounded ${
          message.includes('thành công') 
            ? 'bg-green-100 text-green-700 border border-green-300' 
            : 'bg-red-100 text-red-700 border border-red-300'
        }`}>
          {message}
        </div>
      )}

      <div className="space-y-6">
        {/* Cài đặt kịch bản */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">Cài đặt kịch bản</h3>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Link profile đối thủ
            </label>
            <input
              type="url"
              value={settings.targetProfileUrl}
              onChange={(e) => handleInputChange('targetProfileUrl', e.target.value)}
              placeholder="https://www.tiktok.com/@shoptaikhoangiarc"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số video xem
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={settings.videosToWatch}
                onChange={(e) => handleInputChange('videosToWatch', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thời gian xem (giây)
              </label>
              <input
                type="number"
                min="10"
                max="120"
                value={settings.watchTimeSeconds}
                onChange={(e) => handleInputChange('watchTimeSeconds', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Cài đặt giới hạn */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">Cài đặt giới hạn</h3>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Follow tối đa/ngày
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={settings.followsPerDay}
                onChange={(e) => handleInputChange('followsPerDay', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Follow tối đa/phiên
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={settings.followsPerSession}
                onChange={(e) => handleInputChange('followsPerSession', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Thời gian nghỉ giữa phiên (giây)
            </label>
            <input
              type="number"
              min="300"
              max="14400"
              step="300"
              value={settings.breakTimeMinutes}
              onChange={(e) => handleInputChange('breakTimeMinutes', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-sm text-gray-500 mt-1">
              Khuyến nghị: {formatBreakTime(settings.breakTimeMinutes)}
            </p>
          </div>
        </div>

        {/* Cài đặt thời gian delay */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">Cài đặt thời gian delay</h3>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Delay tối thiểu (giây)
              </label>
              <input
                type="number"
                min="10"
                max="300"
                value={settings.delayBetweenFollowsMin}
                onChange={(e) => handleInputChange('delayBetweenFollowsMin', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Delay tối đa (giây)
              </label>
              <input
                type="number"
                min="10"
                max="300"
                value={settings.delayBetweenFollowsMax}
                onChange={(e) => handleInputChange('delayBetweenFollowsMax', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <p className="text-sm text-gray-500 mt-2">
            Thời gian chờ ngẫu nhiên giữa các lần follow để tránh bị phát hiện
          </p>
        </div>

        {/* Save button */}
        <div className="pt-4">
          <button
            onClick={handleSave}
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang lưu...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                Lưu cài đặt
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FollowSettings;

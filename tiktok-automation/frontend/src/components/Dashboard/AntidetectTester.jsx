/**
 * Antidetect Testing Component
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>lay, FiCheck, FiX, <PERSON>Loader, FiShield, FiAlertTriangle } from 'react-icons/fi';
import { antidetectAPI, handleApiError } from '../../services/api';

const AntidetectTester = ({ profileId, onClose }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [selectedTests, setSelectedTests] = useState({
    bot_sannysoft: true,
    intoli_headless: true,
    areyouheadless: true,
    pixelscan: true,
    browserleaks_webrtc: true,
    tiktok_access: true
  });
  const [testOptions, setTestOptions] = useState({
    headless: false,
    includeTikTok: true
  });

  const testSites = {
    bot_sannysoft: { name: 'Sannysoft Bot Detector', description: 'Comprehensive bot detection tests' },
    intoli_headless: { name: 'Intoli Headless Test', description: 'Headless browser detection' },
    areyouheadless: { name: 'Are You Headless', description: 'Advanced headless detection' },
    pixelscan: { name: 'PixelScan Fingerprint', description: 'Canvas and WebGL fingerprinting' },
    browserleaks_webrtc: { name: 'BrowserLeaks WebRTC', description: 'WebRTC leak detection' },
    tiktok_access: { name: 'TikTok Access Test', description: 'Direct TikTok bot detection test' }
  };

  const runAntidetectTest = async () => {
    setIsRunning(true);
    setTestResults(null);

    try {
      const selectedSiteKeys = Object.keys(selectedTests).filter(key =>
        selectedTests[key] && key !== 'tiktok_access'
      );

      const result = await antidetectAPI.testProfile(
        profileId,
        selectedSiteKeys,
        selectedTests.tiktok_access,
        testOptions.headless
      );

      setTestResults(result);
    } catch (error) {
      console.error('Antidetect test failed:', error);
      const apiError = handleApiError(error);
      setTestResults({
        success: false,
        error: apiError.message,
        overall_score: 0,
        test_results: {},
        recommendations: ['Failed to run test: ' + apiError.message]
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score) => {
    if (score >= 80) return <FiCheck className="text-green-600" />;
    if (score >= 60) return <FiAlertTriangle className="text-yellow-600" />;
    return <FiX className="text-red-600" />;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <FiShield className="text-blue-600 text-xl" />
            <h2 className="text-xl font-semibold">Antidetect Testing</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX size={24} />
          </button>
        </div>

        <div className="p-6">
          {/* Test Configuration */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-4">Test Configuration</h3>
            
            {/* Test Sites Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {Object.entries(testSites).map(([key, site]) => (
                <label key={key} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <input
                    type="checkbox"
                    checked={selectedTests[key]}
                    onChange={(e) => setSelectedTests(prev => ({
                      ...prev,
                      [key]: e.target.checked
                    }))}
                    className="mt-1"
                  />
                  <div>
                    <div className="font-medium">{site.name}</div>
                    <div className="text-sm text-gray-600">{site.description}</div>
                  </div>
                </label>
              ))}
            </div>

            {/* Test Options */}
            <div className="flex items-center space-x-6">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={testOptions.headless}
                  onChange={(e) => setTestOptions(prev => ({
                    ...prev,
                    headless: e.target.checked
                  }))}
                />
                <span>Run in headless mode</span>
              </label>
            </div>
          </div>

          {/* Run Test Button */}
          <div className="mb-6">
            <button
              onClick={runAntidetectTest}
              disabled={isRunning || Object.values(selectedTests).every(v => !v)}
              className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? (
                <>
                  <FiLoader className="animate-spin" />
                  <span>Running Tests...</span>
                </>
              ) : (
                <>
                  <FiPlay />
                  <span>Run Antidetect Test</span>
                </>
              )}
            </button>
          </div>

          {/* Test Results */}
          {testResults && (
            <div className="space-y-6">
              {/* Overall Score */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Overall Detection Score</h3>
                  <div className="flex items-center space-x-2">
                    {getScoreIcon(testResults.overall_score)}
                    <span className={`text-2xl font-bold ${getScoreColor(testResults.overall_score)}`}>
                      {testResults.overall_score}%
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        testResults.overall_score >= 80 ? 'bg-green-600' :
                        testResults.overall_score >= 60 ? 'bg-yellow-600' : 'bg-red-600'
                      }`}
                      style={{ width: `${testResults.overall_score}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Individual Test Results */}
              {testResults.success && testResults.test_results && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Test Results</h3>
                  <div className="space-y-4">
                    {Object.entries(testResults.test_results).map(([testKey, result]) => (
                      <div key={testKey} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">
                            {testSites[testKey]?.name || testKey}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {result.success ? getScoreIcon(result.score || 0) : <FiX className="text-red-600" />}
                            <span className={`font-bold ${getScoreColor(result.score || 0)}`}>
                              {result.score ? `${result.score}%` : 'Failed'}
                            </span>
                          </div>
                        </div>
                        
                        {result.success && result.check_results && (
                          <div className="text-sm text-gray-600">
                            <div>Checks passed: {result.checks_passed}/{result.total_checks}</div>
                            {result.detection_indicators && result.detection_indicators.length > 0 && (
                              <div className="text-red-600 mt-1">
                                Issues: {result.detection_indicators.join(', ')}
                              </div>
                            )}
                          </div>
                        )}
                        
                        {result.error && (
                          <div className="text-red-600 text-sm">
                            Error: {result.error}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {testResults.recommendations && testResults.recommendations.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Recommendations</h3>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <ul className="space-y-2">
                      {testResults.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-blue-600 mt-1">•</span>
                          <span className="text-blue-800">{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Error Display */}
              {!testResults.success && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 text-red-800">
                    <FiX />
                    <span className="font-medium">Test Failed</span>
                  </div>
                  <div className="text-red-700 mt-2">
                    {testResults.error || 'Unknown error occurred'}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AntidetectTester;

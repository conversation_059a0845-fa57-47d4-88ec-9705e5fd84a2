/**
 * DeleteConfirmDialog Component - Confirmation dialog for deleting profiles
 */

import React from 'react';
import { FiX, FiTrash2, FiAlertTriangle } from 'react-icons/fi';

const DeleteConfirmDialog = ({ isOpen, onClose, onConfirm, profile }) => {
  if (!isOpen || !profile) return null;

  const handleConfirm = () => {
    onConfirm(profile.id);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
            <FiAlertTriangle className="w-6 h-6 text-red-500" />
            <span>Xá<PERSON> nhận xóa</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="w-6 h-6" />
          </button>
        </div>

        <div className="mb-6">
          <p className="text-gray-700 mb-4">
            Bạn có chắc chắn muốn xóa profile này không?
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">
                  {profile.stt}
                </span>
              </div>
              <div>
                <div className="font-medium text-gray-900">
                  {profile.username}
                </div>
                <div className="text-sm text-gray-500">
                  Proxy: {profile.proxy}
                </div>
                <div className="text-sm text-gray-500">
                  Status: {profile.status}
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-start space-x-2">
              <FiAlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700">
                <div className="font-medium mb-1">Cảnh báo:</div>
                <ul className="list-disc list-inside space-y-1">
                  <li>Hành động này không thể hoàn tác</li>
                  <li>Tất cả dữ liệu liên quan sẽ bị xóa</li>
                  <li>Các task đang chạy sẽ bị dừng</li>
                  <li>Browser session sẽ bị đóng</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
          >
            Hủy
          </button>
          <button
            onClick={handleConfirm}
            className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
          >
            <FiTrash2 className="w-4 h-4" />
            <span>Xóa Profile</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmDialog;

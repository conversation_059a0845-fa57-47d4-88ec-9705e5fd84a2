/**
 * ProfileForm Component - Form for creating new browser profiles
 */

import React, { useState } from 'react';
import { FiX, FiCheck, FiGlobe } from 'react-icons/fi';
import profileService from '../../services/profileService';

const ProfileForm = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    proxyType: 'no-proxy',
    ipChecker: 'IP2Location',
    host: '',
    port: '',
    username: '',
    password: '',
    profileName: ''
  });
  const [proxyStatus, setProxyStatus] = useState(null);
  const [isChecking, setIsChecking] = useState(false);

  const proxyTypes = [
    { value: 'no-proxy', label: 'No proxy (local network)' },
    { value: 'http', label: 'HTTP' },
    { value: 'https', label: 'HTTPS' },
    { value: 'socks5', label: 'SOCKS5' },
    { value: 'ssh', label: 'SSH' }
  ];

  const ipCheckers = [
    { value: 'IP2Location', label: 'IP2Location' },
    { value: 'ip-api', label: 'ip-api' },
    { value: 'IPIDEA', label: 'IPIDEA' },
    { value: 'IPFoxy', label: 'IPFoxy' },
    { value: 'IPInfo', label: 'IPInfo' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckProxy = async () => {
    if (formData.proxyType === 'no-proxy') {
      setProxyStatus({
        success: true,
        message: 'Local network connection',
        ip: 'Local IP',
        location: 'Local Network'
      });
      return;
    }

    if (!formData.host || !formData.port) {
      alert('Vui lòng nhập Host và Port');
      return;
    }

    setIsChecking(true);
    setProxyStatus(null);

    try {
      // Use profile service to validate proxy
      const result = await profileService.validateProxy(formData);

      if (result.success) {
        setProxyStatus({
          success: true,
          message: 'Proxy connection successful',
          ip: result.ip_address || 'Unknown',
          location: result.geolocation ?
            `${result.geolocation.city}, ${result.geolocation.country}` :
            'Unknown Location',
          responseTime: result.response_time
        });
      } else {
        setProxyStatus({
          success: false,
          message: 'Proxy connection failed',
          error: result.error || 'Unknown error'
        });
      }
    } catch (error) {
      setProxyStatus({
        success: false,
        message: 'Proxy validation failed',
        error: error.message
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!formData.profileName) {
      alert('Vui lòng nhập tên profile');
      return;
    }

    if (formData.proxyType !== 'no-proxy' && (!formData.host || !formData.port)) {
      alert('Vui lòng nhập thông tin proxy');
      return;
    }

    onSubmit(formData);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      proxyType: 'no-proxy',
      ipChecker: 'IP2Location',
      host: '',
      port: '',
      username: '',
      password: '',
      profileName: ''
    });
    setProxyStatus(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Tạo Profile Mới</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tên Profile
            </label>
            <input
              type="text"
              name="profileName"
              value={formData.profileName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nhập tên profile"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Proxy Type
            </label>
            <select
              name="proxyType"
              value={formData.proxyType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {proxyTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              IP Checker
            </label>
            <select
              name="ipChecker"
              value={formData.ipChecker}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {ipCheckers.map(checker => (
                <option key={checker.value} value={checker.value}>
                  {checker.label}
                </option>
              ))}
            </select>
          </div>

          {formData.proxyType !== 'no-proxy' && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Host
                  </label>
                  <input
                    type="text"
                    name="host"
                    value={formData.host}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="***********"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Port
                  </label>
                  <input
                    type="text"
                    name="port"
                    value={formData.port}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="8080"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Username
                  </label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="proxy username"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="proxy password"
                  />
                </div>
              </div>
            </>
          )}

          <div>
            <button
              type="button"
              onClick={handleCheckProxy}
              disabled={isChecking}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isChecking ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Đang kiểm tra...</span>
                </>
              ) : (
                <>
                  <FiGlobe className="w-4 h-4" />
                  <span>Kiểm tra Proxy</span>
                </>
              )}
            </button>
          </div>

          {proxyStatus && (
            <div className={`p-3 rounded-md ${proxyStatus.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <div className="flex items-center space-x-2">
                {proxyStatus.success ? (
                  <FiCheck className="w-5 h-5 text-green-600" />
                ) : (
                  <FiX className="w-5 h-5 text-red-600" />
                )}
                <span className={`text-sm font-medium ${proxyStatus.success ? 'text-green-800' : 'text-red-800'}`}>
                  {proxyStatus.message}
                </span>
              </div>
              {proxyStatus.success && (
                <div className="mt-2 text-sm text-green-700">
                  <p>IP: {proxyStatus.ip}</p>
                  <p>Location: {proxyStatus.location}</p>
                  {proxyStatus.responseTime && <p>Response Time: {proxyStatus.responseTime}ms</p>}
                  {proxyStatus.provider && <p>Provider: {proxyStatus.provider}</p>}
                </div>
              )}
              {!proxyStatus.success && proxyStatus.error && (
                <p className="mt-2 text-sm text-red-700">{proxyStatus.error}</p>
              )}
            </div>
          )}

          <div className="flex space-x-4 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
            >
              Tạo tài khoản
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfileForm;

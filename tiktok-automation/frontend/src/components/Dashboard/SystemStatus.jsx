/**
 * System Status Component for Dashboard
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiServer,
  FiWifi,
  FiCpu,
  FiHardDrive,
  FiActivity,
  FiCheckCircle,
  FiAlertTriangle,
  FiXCircle
} from 'react-icons/fi';

const SystemStatus = () => {
  const [systemData, setSystemData] = useState({
    backend: 'connecting',
    database: 'connecting',
    memory: { used: 0, total: 0, percent: 0 },
    activeBrowsers: 0,
    activeConnections: 0
  });
  const [loading, setLoading] = useState(true);

  // Fetch system status
  useEffect(() => {
    const fetchSystemStatus = async () => {
      try {
        let baseUrl = 'http://localhost:8000';
        if (window.electronAPI) {
          baseUrl = await window.electronAPI.getBackendUrl();
        }

        const response = await fetch(`${baseUrl}/api/v1/system/status`);
        const data = await response.json();
        
        const memoryData = data.memory_usage || { used: 0, total: 0, percent: 0 };
        setSystemData({
          backend: data.status === 'healthy' ? 'connected' : 'error',
          database: data.database === 'connected' ? 'connected' : 'error',
          memory: {
            used: memoryData.used || 0,
            total: memoryData.total || 0,
            percent: memoryData.percent || 0
          },
          activeBrowsers: data.browser_instances || 0,
          activeConnections: data.websocket?.connections || 0
        });
      } catch (error) {
        console.error('Failed to fetch system status:', error);
        setSystemData(prev => ({
          ...prev,
          backend: 'error',
          database: 'error'
        }));
      } finally {
        setLoading(false);
      }
    };

    fetchSystemStatus();
    const interval = setInterval(fetchSystemStatus, 15000); // Refresh every 15 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
        return <FiCheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning':
        return <FiAlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'error':
        return <FiXCircle className="w-4 h-4 text-red-600" />;
      default:
        return <FiActivity className="w-4 h-4 text-gray-600 animate-pulse" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'connected':
        return 'Online';
      case 'warning':
        return 'Warning';
      case 'error':
        return 'Offline';
      default:
        return 'Connecting...';
    }
  };

  const getMemoryStatus = (percent) => {
    if (percent > 90) return 'error';
    if (percent > 70) return 'warning';
    return 'connected';
  };

  const statusItems = [
    {
      label: 'Backend Server',
      status: systemData.backend,
      icon: FiServer,
      description: 'Python FastAPI server'
    },
    {
      label: 'Database',
      status: systemData.database,
      icon: FiHardDrive,
      description: 'SQLite database connection'
    },
    {
      label: 'Memory Usage',
      status: getMemoryStatus(systemData.memory.percent),
      icon: FiCpu,
      description: `${(systemData.memory.percent || 0).toFixed(1)}% used`,
      value: `${systemData.memory.used} MB`
    },
    {
      label: 'WebSocket',
      status: systemData.activeConnections > 0 ? 'connected' : 'warning',
      icon: FiWifi,
      description: `${systemData.activeConnections} active connections`
    }
  ];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">System Status</h2>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">System Status</h2>

      <div className="space-y-4">
        {statusItems.map((item, index) => {
          const Icon = item.icon;
          return (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                <Icon className="w-4 h-4 text-gray-600" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900">{item.label}</h3>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(item.status)}
                    <span className={`text-sm font-medium ${getStatusColor(item.status)}`}>
                      {getStatusText(item.status)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-sm text-gray-600">{item.description}</p>
                  {item.value && (
                    <span className="text-sm text-gray-500">{item.value}</span>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Memory Usage Progress Bar */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Memory Usage</span>
          <span className="text-sm text-gray-600">
            {systemData.memory.used || 0} MB / {systemData.memory.total || 0} MB
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className={`h-2 rounded-full ${
              (systemData.memory.percent || 0) > 90
                ? 'bg-red-500'
                : (systemData.memory.percent || 0) > 70
                ? 'bg-yellow-500'
                : 'bg-green-500'
            }`}
            initial={{ width: 0 }}
            animate={{ width: `${systemData.memory.percent || 0}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Quick Stats */}
      <div className="mt-6 grid grid-cols-2 gap-4">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {systemData.activeBrowsers}
          </div>
          <div className="text-sm text-blue-600">Active Browsers</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {systemData.activeConnections}
          </div>
          <div className="text-sm text-green-600">Connections</div>
        </div>
      </div>
    </div>
  );
};

export default SystemStatus;

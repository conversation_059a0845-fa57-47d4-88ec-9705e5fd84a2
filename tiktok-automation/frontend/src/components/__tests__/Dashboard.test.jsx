/**
 * Tests for Dashboard Component
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { jest } from '@jest/globals';
import Dashboard from '../pages/Dashboard';

// Mock the outlet context
const mockSetPageTitle = jest.fn();
const mockSetPageSubtitle = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useOutletContext: () => ({
    setPageTitle: mockSetPageTitle,
    setPageSubtitle: mockSetPageSubtitle
  })
}));

// Mock fetch
global.fetch = jest.fn();

// Mock Electron API
global.window.electronAPI = {
  getBackendUrl: jest.fn().mockResolvedValue('http://localhost:8000')
};

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>
  }
}));

describe('Dashboard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful API responses
    fetch.mockImplementation((url) => {
      if (url.includes('/profiles/')) {
        return Promise.resolve({
          json: () => Promise.resolve([
            { id: 1, name: 'Profile 1', is_active: true },
            { id: 2, name: 'Profile 2', is_active: false }
          ])
        });
      }
      if (url.includes('/proxies/')) {
        return Promise.resolve({
          json: () => Promise.resolve([
            { id: 1, name: 'Proxy 1', status: 'active' },
            { id: 2, name: 'Proxy 2', status: 'inactive' }
          ])
        });
      }
      if (url.includes('/accounts/')) {
        return Promise.resolve({
          json: () => Promise.resolve([
            { id: 1, username: 'user1', is_logged_in: true },
            { id: 2, username: 'user2', is_logged_in: false }
          ])
        });
      }
      if (url.includes('/tasks/')) {
        return Promise.resolve({
          json: () => Promise.resolve([
            { id: 1, name: 'Task 1', status: 'running' },
            { id: 2, name: 'Task 2', status: 'completed' }
          ])
        });
      }
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  const renderDashboard = () => {
    return render(
      <BrowserRouter>
        <Dashboard />
      </BrowserRouter>
    );
  };

  test('renders dashboard title and subtitle', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(mockSetPageTitle).toHaveBeenCalledWith('Dashboard');
      expect(mockSetPageSubtitle).toHaveBeenCalledWith('Overview of your TikTok automation system');
    });
  });

  test('displays loading state initially', () => {
    renderDashboard();
    
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  test('fetches and displays stats cards', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Browser Profiles')).toBeInTheDocument();
      expect(screen.getByText('Proxies')).toBeInTheDocument();
      expect(screen.getByText('TikTok Accounts')).toBeInTheDocument();
      expect(screen.getByText('Active Tasks')).toBeInTheDocument();
    });

    // Check stats values
    await waitFor(() => {
      expect(screen.getByText('2')).toBeInTheDocument(); // Total profiles
      expect(screen.getByText('1 active')).toBeInTheDocument(); // Active profiles
    });
  });

  test('displays quick actions section', async () => {
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
      expect(screen.getByText('Create Profile')).toBeInTheDocument();
      expect(screen.getByText('Add Proxy')).toBeInTheDocument();
      expect(screen.getByText('New Task')).toBeInTheDocument();
      expect(screen.getByText('View Analytics')).toBeInTheDocument();
    });
  });

  test('quick action buttons navigate correctly', async () => {
    // Mock window.location.href
    delete window.location;
    window.location = { href: '' };
    
    renderDashboard();
    
    await waitFor(() => {
      const createProfileButton = screen.getByText('Create Profile').closest('button');
      fireEvent.click(createProfileButton);
      expect(window.location.href).toBe('/profiles/new');
    });
  });

  test('handles API errors gracefully', async () => {
    // Mock fetch to reject
    fetch.mockRejectedValue(new Error('API Error'));
    
    renderDashboard();
    
    // Should still render without crashing
    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });
  });

  test('refreshes data periodically', async () => {
    jest.useFakeTimers();
    
    renderDashboard();
    
    // Initial fetch
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledTimes(4);
    });
    
    // Fast forward 30 seconds
    jest.advanceTimersByTime(30000);
    
    // Should fetch again
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledTimes(8);
    });
    
    jest.useRealTimers();
  });

  test('calculates stats correctly', async () => {
    renderDashboard();
    
    await waitFor(() => {
      // Should show correct totals and active counts
      const profilesCard = screen.getByText('Browser Profiles').closest('div');
      expect(profilesCard).toHaveTextContent('2'); // Total
      expect(profilesCard).toHaveTextContent('1 active'); // Active
      
      const accountsCard = screen.getByText('TikTok Accounts').closest('div');
      expect(accountsCard).toHaveTextContent('2'); // Total
      expect(accountsCard).toHaveTextContent('1 logged in'); // Logged in
    });
  });

  test('displays trend indicators', async () => {
    renderDashboard();
    
    await waitFor(() => {
      // Should show trend percentages
      expect(screen.getByText('+12%')).toBeInTheDocument();
      expect(screen.getByText('+5%')).toBeInTheDocument();
      expect(screen.getByText('+8%')).toBeInTheDocument();
      expect(screen.getByText('+15%')).toBeInTheDocument();
    });
  });

  test('renders tasks overview component', async () => {
    renderDashboard();
    
    await waitFor(() => {
      // TasksOverview component should be rendered
      expect(screen.getByText('Active Tasks')).toBeInTheDocument();
    });
  });

  test('renders system status component', async () => {
    renderDashboard();
    
    await waitFor(() => {
      // SystemStatus component should be rendered
      expect(screen.getByText('System Status')).toBeInTheDocument();
    });
  });

  test('renders recent activity component', async () => {
    renderDashboard();
    
    await waitFor(() => {
      // RecentActivity component should be rendered
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
    });
  });

  test('handles missing electron API gracefully', async () => {
    // Remove electron API
    delete window.electronAPI;
    
    renderDashboard();
    
    // Should still work with fallback URL
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('localhost:8000'));
    });
  });

  test('stats cards have correct colors', async () => {
    renderDashboard();
    
    await waitFor(() => {
      const profilesIcon = screen.getByText('Browser Profiles').closest('div').querySelector('[data-testid="icon"]');
      expect(profilesIcon).toHaveClass('text-blue-600');
      
      const proxiesIcon = screen.getByText('Proxies').closest('div').querySelector('[data-testid="icon"]');
      expect(proxiesIcon).toHaveClass('text-green-600');
    });
  });

  test('quick actions have hover effects', async () => {
    renderDashboard();
    
    await waitFor(() => {
      const createProfileButton = screen.getByText('Create Profile').closest('button');
      expect(createProfileButton).toHaveClass('hover:border-gray-300');
      expect(createProfileButton).toHaveClass('hover:shadow-md');
    });
  });

  test('displays correct grid layout', async () => {
    renderDashboard();
    
    await waitFor(() => {
      // Stats cards should be in a grid
      const statsGrid = screen.getByText('Browser Profiles').closest('div').parentElement;
      expect(statsGrid).toHaveClass('grid');
      expect(statsGrid).toHaveClass('grid-cols-1');
      expect(statsGrid).toHaveClass('md:grid-cols-2');
      expect(statsGrid).toHaveClass('lg:grid-cols-4');
    });
  });

  test('animation delays are applied correctly', async () => {
    renderDashboard();
    
    // Motion components should have delay props
    // This is more of an integration test to ensure animations work
    await waitFor(() => {
      expect(screen.getByText('Browser Profiles')).toBeInTheDocument();
    });
  });

  test('handles empty data states', async () => {
    // Mock empty responses
    fetch.mockImplementation(() => 
      Promise.resolve({
        json: () => Promise.resolve([])
      })
    );
    
    renderDashboard();
    
    await waitFor(() => {
      // Should show 0 for all stats
      const statsCards = screen.getAllByText('0');
      expect(statsCards.length).toBeGreaterThan(0);
    });
  });

  test('cleanup on unmount', async () => {
    jest.useFakeTimers();
    
    const { unmount } = renderDashboard();
    
    // Fast forward to ensure interval is set
    jest.advanceTimersByTime(1000);
    
    // Unmount component
    unmount();
    
    // Fast forward past interval time
    jest.advanceTimersByTime(30000);
    
    // Should not make additional requests after unmount
    const initialCallCount = fetch.mock.calls.length;
    jest.advanceTimersByTime(30000);
    expect(fetch.mock.calls.length).toBe(initialCallCount);
    
    jest.useRealTimers();
  });
});

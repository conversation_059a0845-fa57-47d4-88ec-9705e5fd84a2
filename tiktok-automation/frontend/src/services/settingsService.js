// Note: Using localStorage for now, will integrate with backend API later

class SettingsService {
  async getFollowSettings() {
    try {
      // For now, use localStorage until backend API is ready
      const saved = localStorage.getItem('followSettings');
      if (saved) {
        return JSON.parse(saved);
      }

      // Return default settings
      return {
        targetProfileUrl: 'https://www.tiktok.com/@shoptaikhoangiarc',
        videosToWatch: 3,
        watchTimeSeconds: 30,
        followsPerDay: 50,
        followsPerSession: 10,
        breakTimeMinutes: 3600,
        delayBetweenFollowsMin: 30,
        delayBetweenFollowsMax: 60,
      };
    } catch (error) {
      console.error('Failed to get follow settings:', error);
      // Return default settings if error
      return {
        targetProfileUrl: 'https://www.tiktok.com/@shoptaikhoangiarc',
        videosToWatch: 3,
        watchTimeSeconds: 30,
        followsPerDay: 50,
        followsPerSession: 10,
        breakTimeMinutes: 3600,
        delayBetweenFollowsMin: 30,
        delayBetweenFollowsMax: 60,
      };
    }
  }

  async saveFollowSettings(settings) {
    try {
      // For now, save to localStorage until backend API is ready
      localStorage.setItem('followSettings', JSON.stringify(settings));
      return { success: true, message: 'Settings saved successfully' };
    } catch (error) {
      console.error('Failed to save follow settings:', error);
      throw error;
    }
  }

  async getSystemSettings() {
    try {
      // For now, use localStorage until backend API is ready
      const saved = localStorage.getItem('systemSettings');
      if (saved) {
        return JSON.parse(saved);
      }

      // Return default system settings
      return {
        autoStart: false,
        maxConcurrentProfiles: 5,
        logLevel: 'info',
        enableNotifications: true,
      };
    } catch (error) {
      console.error('Failed to get system settings:', error);
      // Return default settings if error
      return {
        autoStart: false,
        maxConcurrentProfiles: 5,
        logLevel: 'info',
        enableNotifications: true,
      };
    }
  }

  async saveSystemSettings(settings) {
    try {
      // For now, save to localStorage until backend API is ready
      localStorage.setItem('systemSettings', JSON.stringify(settings));
      return { success: true, message: 'System settings saved successfully' };
    } catch (error) {
      console.error('Failed to save system settings:', error);
      throw error;
    }
  }
}

const settingsService = new SettingsService();
export { settingsService };
export default settingsService;

/**
 * Lazy loading hook for React components and data
 */

import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Hook for lazy loading components
 */
export const useLazyComponent = (importFunc, fallback = null) => {
  const [Component, setComponent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const loadComponent = useCallback(async () => {
    if (Component || loading) return;

    setLoading(true);
    setError(null);

    try {
      const module = await importFunc();
      
      if (mountedRef.current) {
        setComponent(() => module.default || module);
      }
    } catch (err) {
      if (mountedRef.current) {
        setError(err);
        console.error('Error loading component:', err);
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [importFunc, Component, loading]);

  return {
    Component,
    loading,
    error,
    loadComponent
  };
};

/**
 * Hook for lazy loading data with intersection observer
 */
export const useLazyData = (fetchFunc, options = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    enabled = true,
    dependencies = []
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  const elementRef = useRef(null);
  const observerRef = useRef(null);

  const loadData = useCallback(async () => {
    if (!enabled || hasLoaded || loading) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetchFunc();
      setData(result);
      setHasLoaded(true);
    } catch (err) {
      setError(err);
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  }, [fetchFunc, enabled, hasLoaded, loading, ...dependencies]);

  useEffect(() => {
    if (!enabled || hasLoaded) return;

    const element = elementRef.current;
    if (!element) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          loadData();
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(element);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadData, threshold, rootMargin, enabled, hasLoaded]);

  const retry = useCallback(() => {
    setError(null);
    setHasLoaded(false);
    loadData();
  }, [loadData]);

  return {
    data,
    loading,
    error,
    hasLoaded,
    elementRef,
    retry,
    loadData
  };
};

/**
 * Hook for virtual scrolling with lazy loading
 */
export const useVirtualList = (items, options = {}) => {
  const {
    itemHeight = 50,
    containerHeight = 400,
    overscan = 5,
    getItemHeight
  } = options;

  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState(null);

  const totalHeight = getItemHeight 
    ? items.reduce((acc, _, index) => acc + getItemHeight(index), 0)
    : items.length * itemHeight;

  const startIndex = Math.max(
    0,
    Math.floor(scrollTop / itemHeight) - overscan
  );

  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    item,
    index: startIndex + index,
    style: {
      position: 'absolute',
      top: getItemHeight 
        ? items.slice(0, startIndex + index).reduce((acc, _, i) => acc + getItemHeight(i), 0)
        : (startIndex + index) * itemHeight,
      height: getItemHeight ? getItemHeight(startIndex + index) : itemHeight,
      width: '100%'
    }
  }));

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight,
    containerRef: setContainerRef,
    onScroll: handleScroll,
    scrollTop
  };
};

/**
 * Hook for image lazy loading
 */
export const useLazyImage = (src, placeholder = null) => {
  const [imageSrc, setImageSrc] = useState(placeholder);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef(null);

  useEffect(() => {
    if (!src) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setLoading(true);
          
          const img = new Image();
          img.onload = () => {
            setImageSrc(src);
            setLoading(false);
            setError(false);
          };
          img.onerror = () => {
            setLoading(false);
            setError(true);
          };
          img.src = src;
          
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [src]);

  return {
    imageSrc,
    loading,
    error,
    imgRef
  };
};

/**
 * Hook for debounced lazy search
 */
export const useLazySearch = (searchFunc, delay = 300) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const timeoutRef = useRef(null);
  const abortControllerRef = useRef(null);

  const search = useCallback(async (searchQuery) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    setLoading(true);
    setError(null);

    try {
      const result = await searchFunc(searchQuery, {
        signal: abortControllerRef.current.signal
      });
      setResults(result);
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err);
        console.error('Search error:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [searchFunc]);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      search(query);
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [query, search, delay]);

  const setSearchQuery = useCallback((newQuery) => {
    setQuery(newQuery);
  }, []);

  const clearResults = useCallback(() => {
    setQuery('');
    setResults([]);
    setError(null);
  }, []);

  return {
    query,
    results,
    loading,
    error,
    setQuery: setSearchQuery,
    clearResults
  };
};

/**
 * Hook for pagination with lazy loading
 */
export const useLazyPagination = (fetchFunc, options = {}) => {
  const {
    pageSize = 20,
    initialPage = 1,
    enabled = true
  } = options;

  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState(null);

  const loadPage = useCallback(async (page) => {
    if (!enabled || loading) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetchFunc({
        page,
        pageSize,
        offset: (page - 1) * pageSize
      });

      if (page === 1) {
        setData(result.data || []);
      } else {
        setData(prev => [...prev, ...(result.data || [])]);
      }

      setHasMore(result.hasMore !== false && (result.data?.length || 0) === pageSize);
      setCurrentPage(page);
    } catch (err) {
      setError(err);
      console.error('Error loading page:', err);
    } finally {
      setLoading(false);
    }
  }, [fetchFunc, pageSize, enabled, loading]);

  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      loadPage(currentPage + 1);
    }
  }, [loadPage, currentPage, hasMore, loading]);

  const refresh = useCallback(() => {
    setData([]);
    setCurrentPage(initialPage);
    setHasMore(true);
    setError(null);
    loadPage(initialPage);
  }, [loadPage, initialPage]);

  useEffect(() => {
    if (enabled) {
      loadPage(initialPage);
    }
  }, [enabled]); // Only run on enabled change

  return {
    data,
    loading,
    error,
    hasMore,
    currentPage,
    loadMore,
    refresh
  };
};

/**
 * Hook for memory-efficient table with lazy loading
 */
export const useLazyTable = (fetchFunc, options = {}) => {
  const {
    pageSize = 50,
    sortBy = null,
    sortOrder = 'asc',
    filters = {}
  } = options;

  const [data, setData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadData = useCallback(async (page = 1) => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchFunc({
        page,
        pageSize,
        sortBy,
        sortOrder,
        filters,
        offset: (page - 1) * pageSize
      });

      setData(result.data || []);
      setTotalCount(result.totalCount || 0);
      setCurrentPage(page);
    } catch (err) {
      setError(err);
      console.error('Error loading table data:', err);
    } finally {
      setLoading(false);
    }
  }, [fetchFunc, pageSize, sortBy, sortOrder, filters]);

  const goToPage = useCallback((page) => {
    loadData(page);
  }, [loadData]);

  const refresh = useCallback(() => {
    loadData(currentPage);
  }, [loadData, currentPage]);

  useEffect(() => {
    loadData(1);
  }, [sortBy, sortOrder, filters]); // Reload when sort/filter changes

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    data,
    loading,
    error,
    currentPage,
    totalPages,
    totalCount,
    pageSize,
    goToPage,
    refresh
  };
};

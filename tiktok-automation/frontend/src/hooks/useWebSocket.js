/**
 * WebSocket Hook for real-time communication
 */

import { useState, useEffect, useRef, useCallback } from 'react';

export const useWebSocket = (url = null) => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState(null);
  const [connectionError, setConnectionError] = useState(null);
  const ws = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Get WebSocket URL
  const getWebSocketUrl = useCallback(async () => {
    if (url) return url;
    
    try {
      if (window.electronAPI) {
        const backendUrl = await window.electronAPI.getBackendUrl();
        return backendUrl.replace('http', 'ws') + '/ws';
      } else {
        // Fallback for web version
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        return `${protocol}//${window.location.hostname}:8000/ws`;
      }
    } catch (error) {
      console.error('Failed to get WebSocket URL:', error);
      return 'ws://localhost:8000/ws';
    }
  }, [url]);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    try {
      const wsUrl = await getWebSocketUrl();
      console.log('Connecting to WebSocket:', wsUrl);
      
      ws.current = new WebSocket(wsUrl);

      ws.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;
      };

      ws.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          setLastMessage(message);
          
          // Handle different message types
          handleMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          console.log(`Attempting to reconnect in ${delay}ms...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        }
      };

      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionError('Connection failed');
      };

    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      setConnectionError(error.message);
    }
  }, [getWebSocketUrl]);

  // Handle incoming messages
  const handleMessage = useCallback((message) => {
    switch (message.type) {
      case 'connection':
        console.log('WebSocket connection confirmed:', message.message);
        break;
      
      case 'task_update':
        // Handle task updates
        console.log('Task update:', message.data);
        // You can dispatch events or update global state here
        break;
      
      case 'profile_update':
        // Handle profile updates
        console.log('Profile update:', message.data);
        break;
      
      case 'proxy_update':
        // Handle proxy updates
        console.log('Proxy update:', message.data);
        break;
      
      case 'system_update':
        // Handle system updates
        console.log('System update:', message.data);
        break;
      
      case 'log_message':
        // Handle log messages
        console.log('Log message:', message.level, message.message);
        break;
      
      case 'error':
        console.error('WebSocket error message:', message.error);
        break;
      
      default:
        console.log('Unknown message type:', message.type);
    }
  }, []);

  // Send message
  const sendMessage = useCallback((message) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }, []);

  // Subscribe to events
  const subscribe = useCallback((subscription) => {
    sendMessage({
      type: 'subscribe',
      subscription: subscription
    });
  }, [sendMessage]);

  // Unsubscribe from events
  const unsubscribe = useCallback((subscription) => {
    sendMessage({
      type: 'unsubscribe',
      subscription: subscription
    });
  }, [sendMessage]);

  // Get status
  const getStatus = useCallback((statusType) => {
    sendMessage({
      type: 'get_status',
      status_type: statusType
    });
  }, [sendMessage]);

  // Send ping
  const ping = useCallback(() => {
    sendMessage({
      type: 'ping',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  // Disconnect
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (ws.current) {
      ws.current.close(1000, 'Manual disconnect');
    }
  }, []);

  // Initialize connection
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Ping interval to keep connection alive
  useEffect(() => {
    if (isConnected) {
      const pingInterval = setInterval(() => {
        ping();
      }, 30000); // Ping every 30 seconds

      return () => clearInterval(pingInterval);
    }
  }, [isConnected, ping]);

  return {
    isConnected,
    lastMessage,
    connectionError,
    sendMessage,
    subscribe,
    unsubscribe,
    getStatus,
    ping,
    disconnect,
    reconnect: connect
  };
};

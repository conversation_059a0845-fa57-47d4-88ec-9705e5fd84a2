#!/bin/bash
set -e

# TikTok Automation Docker Entrypoint

echo "🚀 Starting TikTok Automation..."

# Set default environment variables
export PYTHONPATH=${PYTHONPATH:-/app/backend}
export NODE_ENV=${NODE_ENV:-production}
export DISPLAY=${DISPLAY:-:99}

# Create necessary directories
mkdir -p /app/data/profiles
mkdir -p /app/data/cookies
mkdir -p /app/data/logs
mkdir -p /tmp/.X11-unix

# Set permissions
chmod 755 /app/data
chmod 755 /app/data/profiles
chmod 755 /app/data/cookies
chmod 755 /app/data/logs

# Start Xvfb for headless browser support
echo "🖥️  Starting virtual display..."
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
XVFB_PID=$!

# Wait for X server to start
sleep 2

# Start VNC server for remote access (optional)
if [ "${ENABLE_VNC}" = "true" ]; then
    echo "🔗 Starting VNC server..."
    x11vnc -display :99 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever -bg
fi

# Start window manager
echo "🪟 Starting window manager..."
fluxbox -display :99 &

# Initialize database if needed
echo "🗄️  Initializing database..."
cd /app/backend
python -c "
import asyncio
from core.database import init_database

async def init():
    try:
        await init_database()
        print('Database initialized successfully')
    except Exception as e:
        print(f'Database initialization error: {e}')

asyncio.run(init())
"

# Check if this is a development environment
if [ "${NODE_ENV}" = "development" ]; then
    echo "🔧 Development mode detected"
    
    # Install development dependencies if needed
    if [ -f "/app/backend/requirements-dev.txt" ]; then
        pip install -r /app/backend/requirements-dev.txt
    fi
fi

# Set up logging
export LOGURU_LEVEL=${LOG_LEVEL:-INFO}

# Function to handle shutdown
cleanup() {
    echo "🛑 Shutting down..."
    
    # Kill background processes
    if [ ! -z "$XVFB_PID" ]; then
        kill $XVFB_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "camoufox" 2>/dev/null || true
    pkill -f "x11vnc" 2>/dev/null || true
    pkill -f "fluxbox" 2>/dev/null || true
    
    echo "✅ Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Check if we should run in standalone mode
if [ "${STANDALONE_MODE}" = "true" ]; then
    echo "🔄 Running in standalone mode..."
    
    # Start backend directly
    cd /app/backend
    exec python main.py
else
    # Run with supervisor (default)
    echo "👥 Running with supervisor..."
    exec "$@"
fi

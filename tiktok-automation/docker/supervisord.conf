[supervisord]
nodaemon=true
user=appuser
logfile=/app/data/logs/supervisord.log
pidfile=/tmp/supervisord.pid
childlogdir=/app/data/logs

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:backend]
command=python main.py
directory=/app/backend
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/data/logs/backend.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PYTHONPATH="/app/backend",DISPLAY=":99"

[program:nginx]
command=nginx -g "daemon off;"
user=root
autostart=false
autorestart=true
redirect_stderr=true
stdout_logfile=/app/data/logs/nginx.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3

[group:tiktok-automation]
programs=backend
priority=999

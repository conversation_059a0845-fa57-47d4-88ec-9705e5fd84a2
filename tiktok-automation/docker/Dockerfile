# Multi-stage Dockerfile for TikTok Automation

# Stage 1: Build frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copy frontend package files
COPY frontend/package*.json ./
RUN npm ci --only=production

# Copy frontend source
COPY frontend/ ./

# Build frontend
RUN npm run build

# Stage 2: Build backend
FROM python:3.11-slim AS backend-builder

WORKDIR /app/backend

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy backend requirements
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend source
COPY backend/ ./

# Stage 3: Runtime image
FROM python:3.11-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    xvfb \
    x11vnc \
    fluxbox \
    && rm -rf /var/lib/apt/lists/*

# Install Camoufox
RUN wget -O camoufox.tar.bz2 "https://camoufox.com/releases/latest/linux" \
    && tar -xjf camoufox.tar.bz2 \
    && mv camoufox /opt/ \
    && ln -s /opt/camoufox/camoufox /usr/local/bin/camoufox \
    && rm camoufox.tar.bz2

# Create app user
RUN useradd -m -u 1000 appuser && \
    mkdir -p /app /app/data /app/logs && \
    chown -R appuser:appuser /app

# Set working directory
WORKDIR /app

# Copy built backend
COPY --from=backend-builder --chown=appuser:appuser /app/backend ./backend

# Copy built frontend
COPY --from=frontend-builder --chown=appuser:appuser /app/frontend/build ./frontend

# Copy additional files
COPY --chown=appuser:appuser docker/entrypoint.sh ./
COPY --chown=appuser:appuser docker/supervisord.conf ./

# Install supervisor for process management
RUN pip install supervisor

# Make entrypoint executable
RUN chmod +x entrypoint.sh

# Create data directories
RUN mkdir -p /app/data/profiles /app/data/cookies /app/data/logs && \
    chown -R appuser:appuser /app/data

# Switch to app user
USER appuser

# Expose ports
EXPOSE 8000 3000 5900

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Set environment variables
ENV PYTHONPATH=/app/backend
ENV NODE_ENV=production
ENV DISPLAY=:99

# Start application
ENTRYPOINT ["./entrypoint.sh"]
CMD ["supervisord", "-c", "supervisord.conf"]

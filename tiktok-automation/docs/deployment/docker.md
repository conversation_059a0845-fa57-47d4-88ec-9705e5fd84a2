# Docker Deployment Guide

This guide covers deploying TikTok Automation using Docker containers for production environments.

## 📋 Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 10GB+ storage

## 🐳 Docker Setup

### Quick Start with Docker Compose

1. **Clone the repository:**
```bash
git clone https://github.com/your-username/tiktok-automation.git
cd tiktok-automation
```

2. **Create environment file:**
```bash
cp docker/.env.example docker/.env
```

3. **Configure environment:**
```bash
nano docker/.env
```

4. **Start services:**
```bash
docker-compose up -d
```

### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: tiktok-automation-backend
    environment:
      - DATABASE_URL=****************************************/tiktok_automation
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: tiktok-automation-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    container_name: tiktok-automation-db
    environment:
      - POSTGRES_DB=tiktok_automation
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: tiktok-automation-redis
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: tiktok-automation-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## 🏗️ Building Custom Images

### Backend Dockerfile

```dockerfile
# docker/Dockerfile.backend
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Camoufox
RUN wget -O camoufox.tar.bz2 "https://camoufox.com/releases/latest/linux" \
    && tar -xjf camoufox.tar.bz2 \
    && mv camoufox /opt/ \
    && ln -s /opt/camoufox/camoufox /usr/local/bin/camoufox \
    && rm camoufox.tar.bz2

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend/ .

# Create data directories
RUN mkdir -p /app/data /app/logs

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["python", "main.py"]
```

### Frontend Dockerfile

```dockerfile
# docker/Dockerfile.frontend
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY frontend/package*.json ./
RUN npm ci --only=production

# Copy source code
COPY frontend/ .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx-frontend.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 3000

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

## ⚙️ Configuration

### Environment Variables

```bash
# docker/.env

# Application
DEBUG=false
SECRET_KEY=your-production-secret-key
ENVIRONMENT=production

# Database
DATABASE_URL=****************************************/tiktok_automation
POSTGRES_DB=tiktok_automation
POSTGRES_USER=user
POSTGRES_PASSWORD=secure-password

# Redis
REDIS_URL=redis://redis:6379

# TikTok Settings
TIKTOK_FOLLOW_LIMIT_PER_HOUR=50
TIKTOK_FOLLOW_LIMIT_PER_DAY=200

# Security
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Performance
MAX_CONCURRENT_BROWSERS=5
MEMORY_LIMIT_MB=2048
```

### Nginx Configuration

```nginx
# docker/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Backend API
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 🚀 Deployment Commands

### Development Deployment

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild and restart
docker-compose up -d --build
```

### Production Deployment

```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 📊 Monitoring

### Health Checks

```bash
# Check service health
docker-compose ps

# Check individual service
docker exec tiktok-automation-backend curl http://localhost:8000/health

# View resource usage
docker stats
```

### Logging

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f backend

# View last 100 lines
docker-compose logs --tail=100 backend
```

## 🔧 Maintenance

### Database Backup

```bash
# Create backup
docker exec tiktok-automation-db pg_dump -U user tiktok_automation > backup.sql

# Restore backup
docker exec -i tiktok-automation-db psql -U user tiktok_automation < backup.sql
```

### Updates

```bash
# Pull latest images
docker-compose pull

# Restart with new images
docker-compose up -d

# Clean old images
docker image prune -f
```

### Scaling

```bash
# Scale backend horizontally
docker-compose up -d --scale backend=3

# Add load balancer configuration
# Update nginx.conf with multiple backend servers
```

## 🔒 Security

### SSL/TLS Setup

1. **Obtain SSL certificates:**
```bash
# Using Let's Encrypt
certbot certonly --standalone -d yourdomain.com
```

2. **Copy certificates:**
```bash
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem docker/ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem docker/ssl/key.pem
```

### Security Headers

```nginx
# Add to nginx.conf
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

## 🚨 Troubleshooting

### Common Issues

**1. Container won't start:**
```bash
# Check logs
docker-compose logs service-name

# Check resource usage
docker system df
```

**2. Database connection issues:**
```bash
# Test database connection
docker exec -it tiktok-automation-backend python -c "from core.database import check_db_health; print(check_db_health())"
```

**3. Permission issues:**
```bash
# Fix volume permissions
sudo chown -R 1000:1000 ./data ./logs
```

### Performance Optimization

```yaml
# docker-compose.yml optimizations
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

## 📚 Additional Resources

- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Docker Compose Reference](https://docs.docker.com/compose/compose-file/)
- [Production Deployment Guide](production.md)
- [Security Best Practices](security.md)

# Getting Started with TikTok Automation

Welcome to TikTok Automation! This guide will help you get up and running quickly with the platform.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Python 3.11+** - [Download here](https://python.org/)
- **Git** - [Download here](https://git-scm.com/)

### System Requirements

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| RAM | 4GB | 8GB+ |
| Storage | 2GB free | 5GB+ free |
| CPU | Dual-core | Quad-core+ |
| OS | Windows 10, macOS 10.15, Ubuntu 18.04 | Latest versions |

## 🚀 Installation

### Step 1: Clone the Repository

```bash
git clone https://github.com/your-username/tiktok-automation.git
cd tiktok-automation
```

### Step 2: Install Dependencies

```bash
# Install all dependencies (backend, frontend, electron)
npm run install-all
```

This command will:
- Install Node.js dependencies for Electron
- Install React dependencies for the frontend
- Install Python dependencies for the backend

### Step 3: Environment Configuration

1. **Copy the environment template:**
```bash
cp backend/.env.example backend/.env
```

2. **Edit the configuration file:**
```bash
# On Windows
notepad backend/.env

# On macOS/Linux
nano backend/.env
```

3. **Configure essential settings:**
```env
# Basic Configuration
DEBUG=true
SECRET_KEY=your-unique-secret-key-here
HOST=127.0.0.1
PORT=8000

# Database
DATABASE_URL=sqlite+aiosqlite:///./tiktok_automation.db

# TikTok Automation Limits
TIKTOK_FOLLOW_LIMIT_PER_HOUR=50
TIKTOK_FOLLOW_LIMIT_PER_DAY=200
TIKTOK_API_DELAY_MIN=2
TIKTOK_API_DELAY_MAX=5

# Browser Settings
BROWSER_HEADLESS=false
BROWSER_TIMEOUT=30000
```

### Step 4: Initialize Database

```bash
cd backend
python -c "from core.database import init_database; import asyncio; asyncio.run(init_database())"
cd ..
```

### Step 5: Start the Application

```bash
# Start all services (backend, frontend, electron)
npm start
```

This will:
1. Start the Python backend server
2. Start the React development server
3. Launch the Electron desktop application

## 🎯 First Steps

### 1. Create Your First Browser Profile

1. **Open the application** - The Electron app should launch automatically
2. **Navigate to Profiles** - Click on "Profiles" in the sidebar
3. **Click "Create Profile"** - Start the profile creation wizard
4. **Configure settings:**
   - **Name**: Give your profile a descriptive name
   - **OS Preference**: Choose Windows, macOS, or Linux
   - **Browser**: Select Chrome, Firefox, or Safari simulation
   - **Auto-generate fingerprint**: Enable for automatic configuration

5. **Save the profile** - Your profile is now ready to use

### 2. Configure Proxy (Optional but Recommended)

1. **Navigate to Proxies** - Click on "Proxies" in the sidebar
2. **Add New Proxy:**
   - **Name**: Descriptive name for your proxy
   - **Type**: HTTP, HTTPS, SOCKS4, SOCKS5, or SSH
   - **Host**: Proxy server address
   - **Port**: Proxy server port
   - **Credentials**: Username and password (if required)

3. **Test Connection** - Verify the proxy works correctly
4. **Link to Profile** - Associate the proxy with your browser profile

### 3. Add TikTok Account

1. **Navigate to Accounts** - Click on "Accounts" in the sidebar
2. **Add New Account:**
   - **Username**: Your TikTok username
   - **Display Name**: Friendly name for identification
   - **Email**: Associated email (optional)
   - **Browser Profile**: Select the profile created earlier

3. **Login Process:**
   - Click "Login" next to your account
   - A browser window will open with your configured profile
   - Log in to TikTok manually
   - Cookies will be automatically saved

### 4. Create Your First Automation Task

1. **Navigate to Tasks** - Click on "Tasks" in the sidebar
2. **Create New Task:**
   - **Task Name**: Descriptive name
   - **Type**: Choose "Follow Followers" for your first task
   - **TikTok Account**: Select your configured account
   - **Target**: Enter a competitor's username
   - **Count**: Start with a small number (10-20)
   - **Delays**: Use default values (2-5 seconds)

3. **Start the Task** - Click the play button to begin automation

## 📊 Monitoring Your Automation

### Dashboard Overview

The main dashboard provides:
- **Real-time statistics** - Active tasks, accounts, profiles
- **System status** - Backend health, memory usage
- **Recent activity** - Latest actions and results

### Task Monitoring

- **Progress tracking** - See completion percentage
- **Live logs** - Real-time action logs
- **Performance metrics** - Success rates and timing

### Account Health

- **Login status** - Cookie validity
- **Rate limiting** - Current usage vs limits
- **Activity history** - Past actions and results

## ⚙️ Basic Configuration

### Rate Limiting

Configure safe automation speeds:

```env
# Conservative settings (recommended for beginners)
TIKTOK_FOLLOW_LIMIT_PER_HOUR=30
TIKTOK_FOLLOW_LIMIT_PER_DAY=150
TIKTOK_API_DELAY_MIN=3
TIKTOK_API_DELAY_MAX=7

# Aggressive settings (use with caution)
TIKTOK_FOLLOW_LIMIT_PER_HOUR=50
TIKTOK_FOLLOW_LIMIT_PER_DAY=200
TIKTOK_API_DELAY_MIN=2
TIKTOK_API_DELAY_MAX=5
```

### Browser Settings

Optimize browser performance:

```env
# For better stealth (slower)
BROWSER_HEADLESS=false
BROWSER_TIMEOUT=45000

# For better performance (faster)
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
```

## 🔧 Troubleshooting

### Common Issues

**1. Application won't start**
- Check Node.js and Python versions
- Ensure all dependencies are installed
- Check for port conflicts (8000, 3000)

**2. Browser profile creation fails**
- Verify Camoufox installation
- Check system permissions
- Try running as administrator (Windows)

**3. TikTok login issues**
- Clear browser data
- Try different proxy
- Check TikTok accessibility

**4. Automation tasks fail**
- Verify account login status
- Check rate limiting settings
- Review task configuration

### Getting Help

1. **Check the logs** - Look in the console for error messages
2. **Review documentation** - Check specific feature guides
3. **Community support** - Join our Discord server
4. **GitHub issues** - Report bugs and request features

## 🎓 Next Steps

Now that you have the basics working:

1. **Read the Profile Management guide** - Learn advanced fingerprinting
2. **Explore Proxy Configuration** - Set up proxy rotation
3. **Study Automation Strategies** - Optimize your targeting
4. **Review Security Best Practices** - Stay safe and undetected

## 📚 Additional Resources

- [Profile Management Guide](profiles.md)
- [Proxy Configuration](proxies.md)
- [Automation Tasks](automation.md)
- [Analytics & Monitoring](analytics.md)
- [API Reference](../api/README.md)
- [Troubleshooting Guide](troubleshooting.md)

---

**Need help?** Join our [Discord community](https://discord.gg/your-server) or [create an issue](https://github.com/your-username/tiktok-automation/issues) on GitHub.

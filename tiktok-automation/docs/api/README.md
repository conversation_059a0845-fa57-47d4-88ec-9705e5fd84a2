# TikTok Automation API Reference

Complete API documentation for the TikTok Automation platform.

## 🌐 Base URL

```
http://localhost:8000/api/v1
```

## 🔐 Authentication

Currently, the API uses simple authentication. In production, implement proper JWT or OAuth2.

```http
Authorization: Bearer your-api-token
```

## 📋 API Overview

| Endpoint | Description |
|----------|-------------|
| `/profiles/` | Browser profile management |
| `/proxies/` | Proxy server configuration |
| `/accounts/` | TikTok account management |
| `/tasks/` | Automation task control |
| `/system/` | System status and health |

## 🎭 Browser Profiles

### List Profiles

```http
GET /api/v1/profiles/
```

**Query Parameters:**
- `active_only` (boolean): Filter active profiles only
- `limit` (integer): Maximum number of results (1-100)
- `offset` (integer): Pagination offset

**Response:**
```json
[
  {
    "id": 1,
    "name": "Profile 1",
    "description": "Chrome Windows profile",
    "is_active": true,
    "fingerprint_config": {
      "user_agent": "Mozilla/5.0...",
      "screen_resolution": "1920x1080",
      "timezone": "America/New_York"
    },
    "usage_count": 15,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### Create Profile

```http
POST /api/v1/profiles/
```

**Request Body:**
```json
{
  "name": "New Profile",
  "description": "Profile description",
  "auto_generate_fingerprint": true,
  "os_preference": "windows",
  "browser_preference": "chrome",
  "fingerprint_config": {
    "user_agent": "custom user agent",
    "screen_resolution": "1920x1080",
    "timezone": "America/New_York",
    "language": "en-US"
  }
}
```

**Response:**
```json
{
  "id": 2,
  "name": "New Profile",
  "description": "Profile description",
  "is_active": true,
  "fingerprint_config": {
    "user_agent": "Mozilla/5.0...",
    "screen_resolution": "1920x1080",
    "timezone": "America/New_York",
    "language": "en-US",
    "canvas_fingerprint": "generated_hash",
    "webgl_fingerprint": "generated_hash"
  },
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Get Profile

```http
GET /api/v1/profiles/{profile_id}
```

### Update Profile

```http
PUT /api/v1/profiles/{profile_id}
```

### Delete Profile

```http
DELETE /api/v1/profiles/{profile_id}
```

### Generate Fingerprint

```http
POST /api/v1/profiles/{profile_id}/generate-fingerprint
```

**Request Body:**
```json
{
  "os_preference": "windows",
  "browser_preference": "chrome"
}
```

## 🌐 Proxies

### List Proxies

```http
GET /api/v1/proxies/
```

**Query Parameters:**
- `active_only` (boolean): Filter active proxies only
- `proxy_type` (string): Filter by type (http, https, socks4, socks5, ssh)

**Response:**
```json
[
  {
    "id": 1,
    "name": "Proxy 1",
    "proxy_type": "http",
    "host": "*************",
    "port": 8080,
    "username": "user",
    "is_active": true,
    "last_used": "2024-01-01T00:00:00Z",
    "success_rate": 95.5,
    "avg_response_time": 250
  }
]
```

### Create Proxy

```http
POST /api/v1/proxies/
```

**Request Body:**
```json
{
  "name": "New Proxy",
  "proxy_type": "http",
  "host": "*************",
  "port": 8080,
  "username": "user",
  "password": "password",
  "description": "Proxy description"
}
```

### Test Proxy

```http
POST /api/v1/proxies/{proxy_id}/test
```

**Response:**
```json
{
  "success": true,
  "response_time": 245,
  "ip_address": "*************",
  "location": "United States",
  "error": null
}
```

## 👤 TikTok Accounts

### List Accounts

```http
GET /api/v1/accounts/
```

**Query Parameters:**
- `active_only` (boolean): Filter active accounts
- `logged_in_only` (boolean): Filter logged in accounts

**Response:**
```json
[
  {
    "id": 1,
    "username": "user123",
    "display_name": "User 123",
    "email": "<EMAIL>",
    "is_active": true,
    "is_logged_in": true,
    "browser_profile_id": 1,
    "follower_count": 1500,
    "following_count": 800,
    "last_login": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

### Create Account

```http
POST /api/v1/accounts/
```

**Request Body:**
```json
{
  "username": "newuser123",
  "display_name": "New User",
  "email": "<EMAIL>",
  "browser_profile_id": 1,
  "notes": "Account notes"
}
```

### Login Account

```http
POST /api/v1/accounts/{account_id}/login
```

**Request Body:**
```json
{
  "manual_login": true,
  "save_cookies": true,
  "headless": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "cookies_saved": true,
  "browser_url": "http://localhost:9222"
}
```

### Check Login Status

```http
GET /api/v1/accounts/{account_id}/login-status
```

**Response:**
```json
{
  "valid": true,
  "cookie_count": 15,
  "last_validated": "2024-01-01T00:00:00Z",
  "expires_at": "2024-01-02T00:00:00Z"
}
```

### Cookie Management

#### Get Cookies Info

```http
GET /api/v1/accounts/{account_id}/cookies
```

#### Export Cookies

```http
POST /api/v1/accounts/{account_id}/cookies/export
```

**Request Body:**
```json
{
  "format": "json"
}
```

#### Import Cookies

```http
POST /api/v1/accounts/{account_id}/cookies/import
```

**Request Body:**
```json
{
  "cookies_data": "cookie data string",
  "format": "json"
}
```

## 🤖 Automation Tasks

### List Tasks

```http
GET /api/v1/tasks/
```

**Query Parameters:**
- `account_id` (integer): Filter by account
- `status` (string): Filter by status (pending, running, paused, completed, failed)
- `task_type` (string): Filter by type
- `active_only` (boolean): Filter active tasks only

**Response:**
```json
[
  {
    "id": 1,
    "name": "Follow Task 1",
    "description": "Follow competitor followers",
    "task_type": "follow_followers",
    "status": "running",
    "tiktok_account_id": 1,
    "competitor_id": 1,
    "target_count": 100,
    "total_processed": 45,
    "successful_actions": 42,
    "failed_actions": 3,
    "progress_percentage": 45.0,
    "started_at": "2024-01-01T00:00:00Z",
    "estimated_completion": "2024-01-01T02:00:00Z"
  }
]
```

### Create Task

```http
POST /api/v1/tasks/
```

**Request Body:**
```json
{
  "name": "New Follow Task",
  "task_type": "follow_followers",
  "tiktok_account_id": 1,
  "competitor_id": 1,
  "target_count": 50,
  "delay_min": 2,
  "delay_max": 5,
  "filter_criteria": {
    "min_followers": 100,
    "max_followers": 10000,
    "verified_only": false
  },
  "scheduled_start": "2024-01-01T12:00:00Z",
  "description": "Task description"
}
```

### Task Control

#### Start Task

```http
POST /api/v1/tasks/{task_id}/start
```

#### Pause Task

```http
POST /api/v1/tasks/{task_id}/pause
```

#### Resume Task

```http
POST /api/v1/tasks/{task_id}/resume
```

#### Stop Task

```http
POST /api/v1/tasks/{task_id}/stop
```

### Task Progress

```http
GET /api/v1/tasks/{task_id}/progress
```

**Response:**
```json
{
  "task_id": 1,
  "status": "running",
  "progress_percentage": 45.0,
  "total_processed": 45,
  "successful_actions": 42,
  "failed_actions": 3,
  "target_count": 100,
  "started_at": "2024-01-01T00:00:00Z",
  "estimated_completion": "2024-01-01T02:00:00Z",
  "current_action": {
    "action": "follow",
    "target": "@user123",
    "timestamp": "2024-01-01T01:30:00Z"
  }
}
```

### Task Logs

```http
GET /api/v1/tasks/{task_id}/logs
```

**Query Parameters:**
- `limit` (integer): Maximum number of log entries
- `offset` (integer): Pagination offset

## 🔧 System

### Health Check

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "database": "connected",
  "websocket": {
    "status": "active",
    "connections": 3
  },
  "memory_usage": {
    "rss": 150.5,
    "percent": 15.2
  },
  "version": "1.0.0",
  "environment": "production"
}
```

### System Status

```http
GET /api/v1/system/status
```

**Response:**
```json
{
  "status": "healthy",
  "uptime": 86400,
  "memory_usage": {
    "total": 8192,
    "used": 1024,
    "percent": 12.5
  },
  "active_browsers": 3,
  "active_tasks": 5,
  "database_status": "connected",
  "proxy_pool_status": "healthy"
}
```

## 📊 Statistics

### Task Statistics

```http
GET /api/v1/tasks/statistics/summary
```

### Account Statistics

```http
GET /api/v1/accounts/statistics/summary
```

### Profile Statistics

```http
GET /api/v1/profiles/statistics/summary
```

## 🔌 WebSocket Events

Connect to: `ws://localhost:8000/ws`

### Subscribe to Events

```json
{
  "type": "subscribe",
  "subscription": "tasks"
}
```

### Event Types

- `task_update` - Task progress updates
- `account_update` - Account status changes
- `system_update` - System status changes
- `log_message` - Real-time log messages

## ❌ Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error type",
  "message": "Detailed error message",
  "details": {
    "field": "validation error details"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

## 📚 Additional Resources

- [OpenAPI Specification](openapi.json)
- [Postman Collection](postman_collection.json)
- [SDK Documentation](sdk.md)
- [Rate Limiting Guide](rate-limiting.md)

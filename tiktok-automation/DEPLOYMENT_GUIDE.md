# TikTok Automation Deployment Guide

## 🚀 Quick Start

### Development Setup
```bash
# Clone and setup
git clone <repository-url>
cd tiktok-automation

# Run setup script
./setup-dev.sh

# Start development environment
./start-dev.sh
```

### Production Deployment
```bash
# Deploy with Docker
./deploy.sh prod

# Or step by step
./deploy.sh build
./deploy.sh prod
```

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS 10.15+, Windows 10+
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB free space
- **CPU**: 2 cores minimum, 4 cores recommended

### Software Dependencies
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: Latest version
- **curl**: For API testing

### For Development
- **Python**: 3.8+
- **Node.js**: 16+
- **npm**: 8+

## 🛠️ Installation Methods

### Method 1: Docker Deployment (Recommended)

#### 1. Clone Repository
```bash
git clone <repository-url>
cd tiktok-automation
```

#### 2. Configure Environment
```bash
# Create environment file
cp .env.example .env

# Edit configuration
nano .env
```

#### 3. Deploy
```bash
# Development
./deploy.sh dev

# Production
./deploy.sh prod

# With monitoring
./deploy.sh monitoring
```

### Method 2: Manual Installation

#### 1. Backend Setup
```bash
cd backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Initialize database
python -c "
import asyncio
from core.database import init_db
asyncio.run(init_db())
"

# Start backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000
```

#### 2. Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Build for production
npm run build

# Serve with nginx or
npm start
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```bash
# Security
SECRET_KEY=your-secret-key-here
DEBUG=False

# Database
DATABASE_URL=sqlite:///./data/tiktok_automation.db

# CORS
CORS_ORIGINS=["https://yourdomain.com"]

# Automation Limits
MAX_FOLLOWS_PER_DAY=100
MAX_UNFOLLOWS_PER_DAY=50
MIN_DELAY_BETWEEN_ACTIONS=30
MAX_DELAY_BETWEEN_ACTIONS=120

# Proxy Settings
DEFAULT_PROXY_TIMEOUT=30
MAX_PROXY_RETRIES=3

# Logging
LOG_LEVEL=INFO
```

#### Frontend (.env)
```bash
REACT_APP_API_URL=https://api.yourdomain.com
REACT_APP_WS_URL=wss://api.yourdomain.com
REACT_APP_ENV=production
```

### Docker Configuration

#### docker-compose.yml Profiles
```bash
# Development
docker-compose up -d

# Production with nginx
docker-compose --profile production up -d

# With monitoring
docker-compose --profile monitoring up -d
```

## 🌐 Production Deployment

### 1. Server Setup

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
```

#### CentOS/RHEL
```bash
# Install Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. SSL/HTTPS Setup

#### Using Let's Encrypt
```bash
# Install certbot
sudo apt install certbot

# Generate certificates
sudo certbot certonly --standalone -d yourdomain.com

# Update nginx configuration
# Copy certificates to nginx/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/key.pem

# Uncomment SSL configuration in nginx/nginx.conf
```

#### Using Custom Certificates
```bash
# Place certificates in nginx/ssl/
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem

# Update nginx configuration
nano nginx/nginx.conf
```

### 3. Domain Configuration

#### DNS Setup
```
A Record: yourdomain.com -> YOUR_SERVER_IP
A Record: api.yourdomain.com -> YOUR_SERVER_IP
```

#### Nginx Configuration
```nginx
server_name yourdomain.com api.yourdomain.com;
```

### 4. Firewall Setup
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# Firewalld (CentOS)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 📊 Monitoring Setup

### 1. Enable Monitoring
```bash
./deploy.sh monitoring
```

### 2. Access Monitoring
- **Prometheus**: http://your-server:9090
- **Grafana**: http://your-server:3001 (admin/admin)

### 3. Configure Alerts
```bash
# Edit prometheus rules
nano monitoring/prometheus.yml

# Add alerting rules
nano monitoring/alert-rules.yml
```

## 🔄 Maintenance

### Backup
```bash
# Create backup
./deploy.sh backup

# Automated backup (crontab)
0 2 * * * /path/to/tiktok-automation/deploy.sh backup
```

### Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and deploy
./deploy.sh build
./deploy.sh prod
```

### Logs
```bash
# View logs
./deploy.sh logs

# Follow logs
docker-compose logs -f

# Specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Health Checks
```bash
# Check service status
./deploy.sh status

# Manual health check
curl http://localhost:8000/api/v1/system/status
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port
sudo lsof -i :8000
sudo lsof -i :3000

# Kill process
sudo kill -9 PID
```

#### Permission Denied
```bash
# Fix Docker permissions
sudo chmod 666 /var/run/docker.sock

# Fix file permissions
sudo chown -R $USER:$USER .
```

#### Database Issues
```bash
# Reset database
rm -f data/tiktok_automation.db
docker-compose restart backend
```

#### SSL Certificate Issues
```bash
# Renew Let's Encrypt certificates
sudo certbot renew

# Test certificate
openssl x509 -in nginx/ssl/cert.pem -text -noout
```

### Performance Issues

#### High Memory Usage
```bash
# Check container stats
docker stats

# Limit container memory
# Add to docker-compose.yml:
deploy:
  resources:
    limits:
      memory: 1G
```

#### Slow Response Times
```bash
# Check nginx logs
docker-compose logs nginx

# Optimize nginx configuration
# Increase worker_connections
# Enable gzip compression
# Add caching headers
```

## 🔒 Security

### 1. Server Hardening
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Configure SSH
sudo nano /etc/ssh/sshd_config
# PermitRootLogin no
# PasswordAuthentication no

# Install fail2ban
sudo apt install fail2ban
```

### 2. Application Security
```bash
# Change default passwords
nano .env

# Use strong secrets
openssl rand -hex 32

# Enable HTTPS only
# Update CORS origins
# Set secure headers
```

### 3. Database Security
```bash
# Backup database regularly
./deploy.sh backup

# Encrypt sensitive data
# Use environment variables for secrets
# Limit database access
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Multiple backend instances
docker-compose up -d --scale backend=3

# Load balancer configuration
# Update nginx upstream
```

### Vertical Scaling
```bash
# Increase container resources
# Update docker-compose.yml
deploy:
  resources:
    limits:
      cpus: '2'
      memory: 4G
```

### Database Scaling
```bash
# Switch to PostgreSQL
# Update DATABASE_URL
# Migrate data
```

## 🎯 Production Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Domain DNS configured
- [ ] Firewall rules set
- [ ] Backup strategy planned

### Post-deployment
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Logs accessible
- [ ] Performance tested
- [ ] Security scan completed

### Ongoing
- [ ] Regular backups
- [ ] Security updates
- [ ] Performance monitoring
- [ ] Log rotation
- [ ] Certificate renewal

---

**Need Help?** Check the troubleshooting section or create an issue in the repository.

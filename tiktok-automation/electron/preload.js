/**
 * TikTok Automation - Electron Preload Script
 * Exposes secure APIs to the renderer process
 */

const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Backend communication
    getBackendUrl: () => ipcRenderer.invoke('get-backend-url'),
    checkBackendStatus: () => ipcRenderer.invoke('check-backend-status'),
    
    // File system operations
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    
    // External operations
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    
    // App information
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    
    // Menu actions
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-action', (event, action, data) => {
            callback(action, data);
        });
    },
    
    // Tray updates
    updateTrayStatus: (data) => {
        ipcRenderer.send('update-tray-status', data);
    },
    
    // Remove listeners
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    }
});

// Expose a limited set of Node.js APIs
contextBridge.exposeInMainWorld('nodeAPI', {
    platform: process.platform,
    arch: process.arch,
    versions: process.versions
});

// Security: Remove Node.js globals from renderer process
delete window.require;
delete window.exports;
delete window.module;

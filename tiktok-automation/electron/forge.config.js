const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');

module.exports = {
  packagerConfig: {
    asar: true,
    icon: './assets/icon',
    executableName: 'TikTokAutomation',
    appBundleId: 'com.tiktokautomation.app',
    appCategoryType: 'public.app-category.productivity',
    win32metadata: {
      CompanyName: 'TikTok Automation',
      ProductName: 'TikTok Automation',
      FileDescription: 'TikTok Automation Desktop Application',
      OriginalFilename: 'TikTokAutomation.exe'
    },
    osxSign: {
      identity: process.env.APPLE_IDENTITY,
      'hardened-runtime': true,
      'gatekeeper-assess': false,
      entitlements: './build/entitlements.plist',
      'entitlements-inherit': './build/entitlements.plist'
    },
    osxNotarize: {
      tool: 'notarytool',
      appleId: process.env.APPLE_ID,
      appleIdPassword: process.env.APPLE_PASSWORD,
      teamId: process.env.APPLE_TEAM_ID
    }
  },
  rebuildConfig: {},
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'TikTokAutomation',
        authors: 'TikTok Automation Team',
        description: 'Advanced TikTok automation platform with antidetect browser profiles',
        setupIcon: './assets/icon.ico',
        loadingGif: './assets/loading.gif',
        certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
        certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD
      }
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['darwin']
    },
    {
      name: '@electron-forge/maker-deb',
      config: {
        options: {
          maintainer: 'TikTok Automation Team',
          homepage: 'https://github.com/your-username/tiktok-automation',
          description: 'Advanced TikTok automation platform with antidetect browser profiles',
          productDescription: 'TikTok Automation provides advanced automation capabilities with antidetect browser profiles, proxy support, and human-like behavior simulation.',
          categories: ['Utility', 'Network'],
          icon: './assets/icon.png'
        }
      }
    },
    {
      name: '@electron-forge/maker-rpm',
      config: {
        options: {
          maintainer: 'TikTok Automation Team',
          homepage: 'https://github.com/your-username/tiktok-automation',
          description: 'Advanced TikTok automation platform with antidetect browser profiles',
          productDescription: 'TikTok Automation provides advanced automation capabilities with antidetect browser profiles, proxy support, and human-like behavior simulation.',
          categories: ['Utility', 'Network'],
          icon: './assets/icon.png'
        }
      }
    },
    {
      name: '@electron-forge/maker-dmg',
      config: {
        name: 'TikTok Automation',
        title: 'TikTok Automation ${version}',
        icon: './assets/icon.icns',
        background: './assets/dmg-background.png',
        contents: [
          {
            x: 130,
            y: 220,
            type: 'file',
            path: './out/TikTok Automation-darwin-x64/TikTok Automation.app'
          },
          {
            x: 410,
            y: 220,
            type: 'link',
            path: '/Applications'
          }
        ]
      }
    }
  ],
  publishers: [
    {
      name: '@electron-forge/publisher-github',
      config: {
        repository: {
          owner: 'your-username',
          name: 'tiktok-automation'
        },
        prerelease: false,
        draft: true
      }
    }
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {}
    },
    {
      name: '@electron-forge/plugin-webpack',
      config: {
        mainConfig: './webpack.main.config.js',
        renderer: {
          config: './webpack.renderer.config.js',
          entryPoints: [
            {
              html: './src/index.html',
              js: './src/renderer.js',
              name: 'main_window',
              preload: {
                js: './src/preload.js'
              }
            }
          ]
        },
        devContentSecurityPolicy: "connect-src 'self' * 'unsafe-eval'"
      }
    },
    // Fuses are used to enable/disable various Electron functionality
    // at package time, before code signing the application
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: true
    })
  ],
  buildIdentifier: process.env.IS_BETA ? 'beta' : 'prod'
};

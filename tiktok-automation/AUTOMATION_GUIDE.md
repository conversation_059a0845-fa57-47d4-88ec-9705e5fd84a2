# TikTok Automation Engine Guide

## 🤖 Automation Features

### Core Automation Capabilities
- **Follow/Unfollow Users**: Automated following and unfollowing with human-like behavior
- **Competitor Follower Targeting**: Follow followers of competitor accounts
- **Human-like Delays**: Randomized delays between actions to avoid detection
- **Rate Limiting**: Configurable daily limits and session controls
- **Multi-threading**: Support for multiple profiles running simultaneously
- **Session Management**: Automatic breaks and session duration controls

### Anti-Detection Features
- **Human-like Clicking**: Random click positions within elements
- **Variable Delays**: Randomized timing between actions
- **Session Breaks**: Automatic breaks to mimic human behavior
- **Browser Fingerprinting**: Unique fingerprints for each profile
- **Proxy Rotation**: Support for proxy switching
- **Cookie Persistence**: Automatic login state management

## 📋 Automation Configuration

### Default Settings
```python
max_follows_per_day: 100
max_unfollows_per_day: 50
min_delay_between_actions: 30  # seconds
max_delay_between_actions: 120  # seconds
human_like_delays: True
respect_rate_limits: True
max_concurrent_profiles: 3
session_duration_minutes: 60
break_duration_minutes: 15
```

### Customizing Automation
You can customize automation behavior through the API:

```bash
# Update automation config for a profile
curl -X PUT "http://localhost:8000/api/v1/automation/config/1" \
  -H "Content-Type: application/json" \
  -d '{
    "max_follows_per_day": 150,
    "min_delay_between_actions": 45,
    "max_delay_between_actions": 180,
    "session_duration_minutes": 90
  }'
```

## 🚀 Using the Automation Engine

### 1. Follow Specific Users
```bash
curl -X POST "http://localhost:8000/api/v1/automation/tasks/follow-users" \
  -H "Content-Type: application/json" \
  -d '{
    "profile_id": 1,
    "usernames": ["user1", "user2", "user3"],
    "config": {
      "max_follows_per_day": 50
    }
  }'
```

### 2. Unfollow Users
```bash
curl -X POST "http://localhost:8000/api/v1/automation/tasks/unfollow-users" \
  -H "Content-Type: application/json" \
  -d '{
    "profile_id": 1,
    "usernames": ["user1", "user2"],
    "config": {
      "max_unfollows_per_day": 30
    }
  }'
```

### 3. Follow Competitor's Followers
```bash
curl -X POST "http://localhost:8000/api/v1/automation/tasks/follow-competitors-followers" \
  -H "Content-Type: application/json" \
  -d '{
    "profile_id": 1,
    "competitor_username": "competitor_account",
    "max_followers": 100,
    "config": {
      "max_follows_per_day": 80
    }
  }'
```

### 4. Start/Stop Tasks
```bash
# Start a task
curl -X POST "http://localhost:8000/api/v1/automation/tasks/1/start"

# Stop a task
curl -X POST "http://localhost:8000/api/v1/automation/tasks/1/stop"

# Pause a task
curl -X POST "http://localhost:8000/api/v1/automation/tasks/1/pause"

# Resume a task
curl -X POST "http://localhost:8000/api/v1/automation/tasks/1/resume"
```

### 5. Monitor Progress
```bash
# Get task statistics
curl "http://localhost:8000/api/v1/automation/tasks/1/statistics"

# Get all active sessions
curl "http://localhost:8000/api/v1/automation/sessions/active"

# Get all tasks
curl "http://localhost:8000/api/v1/automation/tasks/"
```

## 🛡️ Safety Features

### Rate Limiting
- Daily follow/unfollow limits
- Minimum delays between actions
- Session duration limits
- Automatic breaks

### Human-like Behavior
- Random delays (30-120 seconds default)
- Random click positions
- Variable session lengths
- Natural break patterns

### Error Handling
- Automatic retry on failures
- Graceful handling of blocked accounts
- Session recovery
- Detailed error logging

## 📊 Monitoring and Analytics

### Real-time Statistics
- Follows completed
- Unfollows completed
- Errors encountered
- Session duration
- Success rate

### Logging
All automation activities are logged with:
- Timestamp
- Profile ID
- Action type
- Target username
- Result status
- Error details (if any)

## ⚠️ Best Practices

### Account Safety
1. **Start Slow**: Begin with low daily limits (20-30 actions)
2. **Use Quality Proxies**: Residential proxies work best
3. **Vary Timing**: Don't run automation at the same time daily
4. **Monitor Results**: Watch for blocks or restrictions
5. **Account Warm-up**: Gradually increase activity over time

### Profile Management
1. **Separate Profiles**: Use different profiles for different strategies
2. **Proxy Assignment**: Assign dedicated proxies to profiles
3. **Session Isolation**: Don't run too many profiles simultaneously
4. **Regular Breaks**: Take days off from automation

### Target Selection
1. **Relevant Audiences**: Target users interested in your niche
2. **Active Users**: Focus on recently active accounts
3. **Quality over Quantity**: Better to follow fewer, more relevant users
4. **Avoid Spam**: Don't target the same users repeatedly

## 🔧 Troubleshooting

### Common Issues

#### Task Not Starting
- Check if profile is logged in
- Verify proxy connection
- Ensure no other tasks are running for the profile

#### High Error Rate
- Check proxy quality
- Reduce action frequency
- Verify target usernames are valid
- Check for TikTok restrictions

#### Session Timeouts
- Increase session duration
- Check network stability
- Verify browser dependencies

#### Login Issues
- Clear cookies and re-login manually
- Check proxy IP changes
- Verify account credentials

### Debug Mode
Enable debug logging for detailed information:
```bash
export LOG_LEVEL=DEBUG
```

## 📈 Performance Optimization

### Multi-threading
- Run multiple profiles simultaneously
- Use different proxies for each profile
- Stagger start times to avoid detection

### Resource Management
- Monitor CPU and memory usage
- Limit concurrent browser instances
- Use headless mode when possible

### Network Optimization
- Use high-quality proxies
- Implement proxy rotation
- Monitor connection stability

## 🔒 Security Considerations

### Data Protection
- All data stored locally
- Encrypted cookie storage
- Secure proxy credentials
- No data transmission to third parties

### Account Security
- Use dedicated accounts for automation
- Enable 2FA where possible
- Regular password changes
- Monitor account health

### Compliance
- Respect TikTok's Terms of Service
- Follow local regulations
- Implement appropriate rate limits
- Monitor for policy changes

## 📚 API Reference

### Automation Endpoints
- `POST /api/v1/automation/tasks/` - Create automation task
- `GET /api/v1/automation/tasks/` - List automation tasks
- `GET /api/v1/automation/tasks/{id}` - Get specific task
- `POST /api/v1/automation/tasks/{id}/start` - Start task
- `POST /api/v1/automation/tasks/{id}/stop` - Stop task
- `POST /api/v1/automation/tasks/{id}/pause` - Pause task
- `POST /api/v1/automation/tasks/{id}/resume` - Resume task
- `DELETE /api/v1/automation/tasks/{id}` - Delete task
- `GET /api/v1/automation/tasks/{id}/statistics` - Get task stats
- `GET /api/v1/automation/sessions/active` - Get active sessions
- `POST /api/v1/automation/sessions/stop-all` - Stop all sessions
- `PUT /api/v1/automation/config/{profile_id}` - Update config

### Task Types
- `follow_users` - Follow specific users
- `unfollow_users` - Unfollow specific users
- `follow_competitors_followers` - Follow competitor's followers

### Task Status
- `created` - Task created but not started
- `running` - Task is currently running
- `paused` - Task is paused
- `stopped` - Task was stopped manually
- `completed` - Task completed successfully
- `failed` - Task failed with errors

## 🎯 Success Tips

1. **Quality Proxies**: Invest in good residential proxies
2. **Gradual Scaling**: Start small and gradually increase
3. **Content Strategy**: Combine automation with quality content
4. **Engagement**: Engage with followers you gain
5. **Monitoring**: Regularly check automation performance
6. **Adaptation**: Adjust strategy based on results
7. **Compliance**: Always follow platform guidelines

---

**Remember**: Automation is a tool to enhance your TikTok strategy, not replace genuine engagement and quality content creation.

name: Build and Release

on:
  push:
    tags:
      - 'v*'
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm run install-all

      - name: Run tests
        run: |
          npm run test

      - name: Run linting
        run: |
          npm run lint

      - name: Run security checks
        run: |
          npm run security

  build-windows:
    needs: test
    runs-on: windows-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm run install-all

      - name: Build Windows
        run: |
          npm run build:win
        env:
          WINDOWS_CERTIFICATE_FILE: ${{ secrets.WINDOWS_CERTIFICATE_FILE }}
          WINDOWS_CERTIFICATE_PASSWORD: ${{ secrets.WINDOWS_CERTIFICATE_PASSWORD }}

      - name: Upload Windows artifacts
        uses: actions/upload-artifact@v3
        with:
          name: windows-build
          path: |
            dist/**/*.exe
            dist/**/*.msi
            dist/**/checksums.json
            dist/**/RELEASE_NOTES.md

  build-macos:
    needs: test
    runs-on: macos-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm run install-all

      - name: Import Code-Signing Certificates
        if: env.APPLE_CERTIFICATE != ''
        uses: Apple-Actions/import-codesign-certs@v2
        with:
          p12-file-base64: ${{ secrets.APPLE_CERTIFICATE }}
          p12-password: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
        env:
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}

      - name: Build macOS
        run: |
          npm run build:mac
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          APPLE_IDENTITY: ${{ secrets.APPLE_IDENTITY }}

      - name: Upload macOS artifacts
        uses: actions/upload-artifact@v3
        with:
          name: macos-build
          path: |
            dist/**/*.dmg
            dist/**/*.zip
            dist/**/checksums.json
            dist/**/RELEASE_NOTES.md

  build-linux:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2

      - name: Install dependencies
        run: |
          npm run install-all

      - name: Build Linux
        run: |
          npm run build:linux

      - name: Upload Linux artifacts
        uses: actions/upload-artifact@v3
        with:
          name: linux-build
          path: |
            dist/**/*.AppImage
            dist/**/*.deb
            dist/**/*.rpm
            dist/**/checksums.json
            dist/**/RELEASE_NOTES.md

  release:
    needs: [build-windows, build-macos, build-linux]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Create release directory
        run: |
          mkdir -p release
          find . -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.rpm" | xargs -I {} cp {} release/
          find . -name "checksums.json" -o -name "RELEASE_NOTES.md" | head -1 | xargs -I {} cp {} release/

      - name: Generate combined checksums
        run: |
          cd release
          sha256sum * > SHA256SUMS
          cat SHA256SUMS

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: release/*
          draft: false
          prerelease: ${{ contains(github.ref, 'beta') || contains(github.ref, 'alpha') }}
          generate_release_notes: true
          body_path: release/RELEASE_NOTES.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload to release server
        if: env.RELEASE_SERVER_URL != ''
        run: |
          # Upload to custom release server
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.RELEASE_SERVER_TOKEN }}" \
            -F "version=${GITHUB_REF#refs/tags/}" \
            -F "files=@release.tar.gz" \
            "${{ secrets.RELEASE_SERVER_URL }}/upload"
        env:
          RELEASE_SERVER_URL: ${{ secrets.RELEASE_SERVER_URL }}

  docker:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/tiktok-automation
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  notify:
    needs: [release, docker]
    runs-on: ubuntu-latest
    if: always() && startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Notify Discord
        if: env.DISCORD_WEBHOOK != ''
        run: |
          curl -H "Content-Type: application/json" \
            -d "{\"content\": \"🚀 New release ${{ github.ref_name }} is now available!\\n\\nDownload: https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}\"}" \
            "${{ secrets.DISCORD_WEBHOOK }}"
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}

      - name: Update documentation
        if: env.DOCS_DEPLOY_KEY != ''
        run: |
          # Trigger documentation update
          curl -X POST \
            -H "Authorization: token ${{ secrets.DOCS_DEPLOY_KEY }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${{ github.repository }}/dispatches" \
            -d '{"event_type": "update-docs", "client_payload": {"version": "${{ github.ref_name }}"}}'
        env:
          DOCS_DEPLOY_KEY: ${{ secrets.DOCS_DEPLOY_KEY }}

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const Store = require('electron-store');
const { autoUpdater } = require('electron-updater');

// Initialize electron store for settings
const store = new Store();

// Keep a global reference of the window object
let mainWindow;
let backendProcess;

// Performance optimization: Enable hardware acceleration
app.commandLine.appendSwitch('enable-hardware-acceleration');
app.commandLine.appendSwitch('enable-gpu-rasterization');

// Memory optimization
app.commandLine.appendSwitch('max-old-space-size', '512');
app.commandLine.appendSwitch('memory-pressure-off');

const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';

function createWindow() {
  // Create the browser window with optimized settings
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    show: false, // Don't show until ready-to-show
    icon: path.join(__dirname, '../../assets/icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      // Performance optimizations
      experimentalFeatures: true,
      backgroundThrottling: false
    },
    titleBarStyle: 'default',
    frame: true
  });

  // Performance: Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Load the frontend
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
  } else {
    mainWindow.loadFile(path.join(__dirname, '../../frontend/build/index.html'));
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle window minimize to tray (optional)
  mainWindow.on('minimize', (event) => {
    if (store.get('minimizeToTray', false)) {
      event.preventDefault();
      mainWindow.hide();
    }
  });
}

function startBackendServer() {
  return new Promise((resolve, reject) => {
    const backendPath = isDev 
      ? path.join(__dirname, '../../backend')
      : path.join(process.resourcesPath, 'backend');
    
    const pythonExecutable = isDev ? 'python' : path.join(backendPath, 'main.exe');
    const args = isDev ? ['-m', 'uvicorn', 'main:app', '--port', '8000'] : [];

    console.log('Starting backend server...');
    console.log('Backend path:', backendPath);
    console.log('Python executable:', pythonExecutable);

    backendProcess = spawn(pythonExecutable, args, {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    backendProcess.stdout.on('data', (data) => {
      console.log(`Backend stdout: ${data}`);
      if (data.toString().includes('Uvicorn running')) {
        resolve();
      }
    });

    backendProcess.stderr.on('data', (data) => {
      console.error(`Backend stderr: ${data}`);
    });

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
    });

    backendProcess.on('error', (error) => {
      console.error('Failed to start backend:', error);
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      resolve(); // Continue even if backend doesn't respond
    }, 30000);
  });
}

// App event handlers
app.whenReady().then(async () => {
  try {
    // Start backend server first
    await startBackendServer();
    
    // Create main window
    createWindow();
    
    // Setup auto updater
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify();
    }
  } catch (error) {
    console.error('Failed to start application:', error);
    dialog.showErrorBox('Startup Error', 'Failed to start the application backend.');
  }
});

app.on('window-all-closed', () => {
  // Kill backend process
  if (backendProcess) {
    backendProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

app.on('before-quit', () => {
  // Cleanup backend process
  if (backendProcess) {
    backendProcess.kill();
  }
});

// IPC handlers for communication with renderer
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-setting', (event, key, defaultValue) => {
  return store.get(key, defaultValue);
});

ipcMain.handle('set-setting', (event, key, value) => {
  store.set(key, value);
  return true;
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// Auto updater events
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
  console.log('Update available.');
});

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available.');
});

autoUpdater.on('error', (err) => {
  console.log('Error in auto-updater. ' + err);
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "Download speed: " + progressObj.bytesPerSecond;
  log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
  console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
  console.log('Update downloaded');
  autoUpdater.quitAndInstall();
});

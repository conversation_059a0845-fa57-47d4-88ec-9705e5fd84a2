const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Settings management
  getSetting: (key, defaultValue) => ipcRenderer.invoke('get-setting', key, defaultValue),
  setSetting: (key, value) => ipcRenderer.invoke('set-setting', key, value),
  
  // Dialog
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.send('minimize-window'),
  maximizeWindow: () => ipcRenderer.send('maximize-window'),
  closeWindow: () => ipcRenderer.send('close-window'),
  
  // Backend communication
  sendToBackend: (channel, data) => ipcRenderer.send('backend-message', { channel, data }),
  onBackendMessage: (callback) => ipcRenderer.on('backend-response', callback),
  
  // File system (limited access)
  selectFile: (options) => ipcRenderer.invoke('select-file', options),
  selectDirectory: (options) => ipcRenderer.invoke('select-directory', options),
  
  // Notifications
  showNotification: (title, body, options) => ipcRenderer.invoke('show-notification', { title, body, options }),
  
  // Performance monitoring
  getMemoryUsage: () => ipcRenderer.invoke('get-memory-usage'),
  getCPUUsage: () => ipcRenderer.invoke('get-cpu-usage'),
  
  // Auto updater
  checkForUpdates: () => ipcRenderer.send('check-for-updates'),
  onUpdateAvailable: (callback) => ipcRenderer.on('update-available', callback),
  onUpdateDownloaded: (callback) => ipcRenderer.on('update-downloaded', callback),
  
  // Security: Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Performance monitoring
contextBridge.exposeInMainWorld('performance', {
  mark: (name) => performance.mark(name),
  measure: (name, startMark, endMark) => performance.measure(name, startMark, endMark),
  getEntriesByType: (type) => performance.getEntriesByType(type),
  clearMarks: (name) => performance.clearMarks(name),
  clearMeasures: (name) => performance.clearMeasures(name)
});

// Console logging for development
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('devTools', {
    log: (...args) => console.log(...args),
    error: (...args) => console.error(...args),
    warn: (...args) => console.warn(...args),
    info: (...args) => console.info(...args)
  });
}

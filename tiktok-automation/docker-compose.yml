version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tiktok-automation-backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=sqlite:///./data/tiktok_automation.db
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-this}
      - DEBUG=${DEBUG:-False}
      - CORS_ORIGINS=["http://localhost:3000","http://frontend:3000"]
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./profiles:/app/profiles
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    ports:
      - "8000:8000"
    networks:
      - tiktok-automation
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/system/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tiktok-automation-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - tiktok-automation
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and session management (optional)
  redis:
    image: redis:7-alpine
    container_name: tiktok-automation-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis-password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - tiktok-automation
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: tiktok-automation-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - tiktok-automation
    profiles:
      - production

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: tiktok-automation-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tiktok-automation
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: tiktok-automation-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - tiktok-automation
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  tiktok-automation:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

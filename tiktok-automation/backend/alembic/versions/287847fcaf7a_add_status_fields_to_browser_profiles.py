"""add_status_fields_to_browser_profiles

Revision ID: 287847fcaf7a
Revises: 46de6d128861
Create Date: 2025-07-04 22:20:32.098007

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '287847fcaf7a'
down_revision: Union[str, Sequence[str], None] = '46de6d128861'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add status, current_action, and is_logged_in columns to browser_profiles table
    op.add_column('browser_profiles', sa.Column('status', sa.String(50), nullable=False, server_default='inactive'))
    op.add_column('browser_profiles', sa.Column('current_action', sa.String(255), nullable=True, server_default='Chờ đăng nhập'))
    op.add_column('browser_profiles', sa.Column('is_logged_in', sa.<PERSON>(), nullable=False, server_default='false'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove the added columns
    op.drop_column('browser_profiles', 'is_logged_in')
    op.drop_column('browser_profiles', 'current_action')
    op.drop_column('browser_profiles', 'status')

"""
Optimized Follow Workflow for TikTok Automation
Implements natural follow patterns with pre-follow behavior and anti-detection
"""

import asyncio
import random
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from .human_behavior import HumanBehavior
from .rate_limiter import RateLimiter
from camoufox_integration.antidetect_config import AntidetectConfig


class OptimizedFollowWorkflow:
    """Optimized workflow for following TikTok users with natural behavior"""
    
    def __init__(self):
        self.human_behavior = HumanBehavior()
        self.rate_limiter = RateLimiter()
        self.antidetect_config = AntidetectConfig()
        
        # Follow statistics
        self.session_follows = 0
        self.session_start_time = datetime.utcnow()
        self.last_follow_time = None
        
        # Behavior patterns
        self.follow_patterns = {
            "conservative": {
                "daily_limit": 50,
                "hourly_limit": 8,
                "min_interval": 45,  # seconds
                "max_interval": 180,
                "engagement_probability": 0.15,
                "video_watch_probability": 0.8
            },
            "moderate": {
                "daily_limit": 100,
                "hourly_limit": 15,
                "min_interval": 30,
                "max_interval": 120,
                "engagement_probability": 0.25,
                "video_watch_probability": 0.9
            },
            "aggressive": {
                "daily_limit": 200,
                "hourly_limit": 25,
                "min_interval": 15,
                "max_interval": 60,
                "engagement_probability": 0.35,
                "video_watch_probability": 0.95
            }
        }
        
        self.current_pattern = "moderate"
    
    async def execute_follow_sequence(
        self,
        page,
        target_username: str,
        pattern: str = None,
        mobile_mode: bool = False
    ) -> Dict[str, Any]:
        """Execute complete follow sequence with natural behavior"""
        
        try:
            if pattern:
                self.current_pattern = pattern
            
            pattern_config = self.follow_patterns[self.current_pattern]
            
            logger.info(f"Starting follow sequence for @{target_username} with {self.current_pattern} pattern")
            
            # Check rate limits
            if not await self._check_rate_limits(pattern_config):
                return {"success": False, "error": "Rate limit exceeded"}
            
            # Step 1: Navigate to user profile
            navigation_result = await self._navigate_to_profile(page, target_username)
            if not navigation_result["success"]:
                return navigation_result
            
            # Step 2: Pre-follow behavior (explore profile)
            await self._pre_follow_behavior(page, pattern_config, mobile_mode)
            
            # Step 3: Watch videos (if configured)
            if random.random() < pattern_config["video_watch_probability"]:
                await self._watch_user_videos(page, pattern_config, mobile_mode)
            
            # Step 4: Optional engagement
            if random.random() < pattern_config["engagement_probability"]:
                await self._optional_engagement(page, mobile_mode)
            
            # Step 5: Execute follow action
            follow_result = await self._execute_follow_action(page, mobile_mode)
            if not follow_result["success"]:
                return follow_result
            
            # Step 6: Post-follow behavior
            await self._post_follow_behavior(page, mobile_mode)
            
            # Update statistics
            self._update_follow_statistics()
            
            # Calculate next follow interval
            next_interval = self._calculate_next_interval(pattern_config)
            
            logger.info(f"Follow sequence completed for @{target_username}")
            
            return {
                "success": True,
                "username": target_username,
                "pattern": self.current_pattern,
                "next_interval": next_interval,
                "session_follows": self.session_follows
            }
            
        except Exception as e:
            logger.error(f"Error in follow sequence for @{target_username}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _navigate_to_profile(self, page, username: str) -> Dict[str, Any]:
        """Navigate to user profile with natural behavior"""
        
        try:
            # Construct profile URL
            profile_url = f"https://www.tiktok.com/@{username}"
            
            logger.info(f"Navigating to profile: {profile_url}")
            
            # Navigate with human-like delay
            await self.human_behavior.human_delay(1.0, 3.0, "general")
            
            response = await page.goto(profile_url, timeout=30000)
            
            if not response or response.status != 200:
                return {"success": False, "error": f"Failed to load profile (status: {response.status if response else 'timeout'})"}
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=15000)
            
            # Check if profile exists
            profile_exists = await self._check_profile_exists(page)
            if not profile_exists:
                return {"success": False, "error": "Profile not found or private"}
            
            # Simulate reading profile info
            await self.human_behavior.simulate_user_attention(
                page, 
                "[data-e2e='user-title']", 
                random.uniform(2.0, 5.0)
            )
            
            return {"success": True}
            
        except Exception as e:
            logger.error(f"Error navigating to profile @{username}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _pre_follow_behavior(self, page, pattern_config: Dict[str, Any], mobile_mode: bool):
        """Execute pre-follow behavior to appear natural"""
        
        try:
            logger.debug("Executing pre-follow behavior")
            
            # Look at profile information
            await self.human_behavior.simulate_user_attention(
                page,
                "[data-e2e='user-bio']",
                random.uniform(1.0, 3.0)
            )
            
            # Check follower/following counts
            await self.human_behavior.simulate_user_attention(
                page,
                "[data-e2e='followers-count']",
                random.uniform(0.5, 2.0)
            )
            
            # Scroll to see videos
            if mobile_mode:
                await self.human_behavior.simulate_mobile_gestures(page, "swipe")
            else:
                await self.human_behavior.human_scroll(
                    page, 
                    direction="down", 
                    distance=random.randint(200, 600)
                )
            
            # Natural pause
            await self.human_behavior.human_delay(1.0, 4.0, "general")
            
            # Occasionally scroll back up
            if random.random() < 0.3:
                if mobile_mode:
                    await self.human_behavior.simulate_mobile_gestures(page, "swipe")
                else:
                    await self.human_behavior.human_scroll(
                        page,
                        direction="up",
                        distance=random.randint(100, 300)
                    )
            
        except Exception as e:
            logger.error(f"Error in pre-follow behavior: {e}")
    
    async def _watch_user_videos(self, page, pattern_config: Dict[str, Any], mobile_mode: bool):
        """Watch user videos before following"""
        
        try:
            logger.debug("Watching user videos")
            
            # Find video elements
            video_selectors = [
                "[data-e2e='user-post-item']",
                ".video-feed-item",
                "[data-e2e='video-item']"
            ]
            
            videos_found = False
            for selector in video_selectors:
                videos = await page.query_selector_all(selector)
                if videos:
                    videos_found = True
                    
                    # Watch 1-3 videos
                    videos_to_watch = min(len(videos), random.randint(1, 3))
                    
                    for i in range(videos_to_watch):
                        video = videos[i]
                        
                        # Click on video
                        await video.click()
                        await self.human_behavior.human_delay(0.5, 1.5, "click")
                        
                        # Watch video
                        await self.human_behavior.tiktok_natural_navigation(page, "video_watch")
                        
                        # Close video (go back)
                        await page.keyboard.press("Escape")
                        await self.human_behavior.human_delay(0.5, 2.0, "general")
                    
                    break
            
            if not videos_found:
                logger.warning("No videos found on profile")
                # Still simulate some watching behavior
                await self.human_behavior.human_delay(3.0, 8.0, "general")
            
        except Exception as e:
            logger.error(f"Error watching user videos: {e}")
    
    async def _optional_engagement(self, page, mobile_mode: bool):
        """Optional engagement actions (like, comment)"""
        
        try:
            logger.debug("Executing optional engagement")
            
            engagement_actions = ["like", "comment", "share"]
            action = random.choice(engagement_actions)
            
            if action == "like":
                # Try to like a video
                like_selectors = [
                    "[data-e2e='like-icon']",
                    ".like-button",
                    "[aria-label*='like']"
                ]
                
                for selector in like_selectors:
                    like_button = await page.query_selector(selector)
                    if like_button:
                        await like_button.click()
                        await self.human_behavior.human_delay(0.5, 1.5, "click")
                        break
            
            elif action == "comment":
                # Simulate opening comment section (but don't actually comment)
                comment_selectors = [
                    "[data-e2e='comment-icon']",
                    ".comment-button",
                    "[aria-label*='comment']"
                ]
                
                for selector in comment_selectors:
                    comment_button = await page.query_selector(selector)
                    if comment_button:
                        await comment_button.click()
                        await self.human_behavior.human_delay(1.0, 3.0, "general")
                        # Close comment section
                        await page.keyboard.press("Escape")
                        break
            
            # Natural pause after engagement
            await self.human_behavior.human_delay(1.0, 3.0, "general")
            
        except Exception as e:
            logger.error(f"Error in optional engagement: {e}")
    
    async def _execute_follow_action(self, page, mobile_mode: bool) -> Dict[str, Any]:
        """Execute the actual follow action"""
        
        try:
            logger.debug("Executing follow action")
            
            # Find follow button
            follow_selectors = [
                "[data-e2e='follow-button']",
                "button[data-e2e='follow-button']",
                "button:has-text('Follow')",
                "button:has-text('Theo dõi')",
                ".follow-button"
            ]
            
            follow_button = None
            for selector in follow_selectors:
                follow_button = await page.query_selector(selector)
                if follow_button:
                    break
            
            if not follow_button:
                return {"success": False, "error": "Follow button not found"}
            
            # Check if already following
            button_text = await follow_button.inner_text()
            if any(text in button_text.lower() for text in ["following", "đang theo dõi", "unfollow"]):
                return {"success": False, "error": "Already following this user"}
            
            # Human-like mouse movement to follow button
            await self.human_behavior.human_mouse_movement(page, follow_selectors[0])
            
            # Click follow button
            await follow_button.click()
            await self.human_behavior.human_delay(0.5, 1.5, "follow")
            
            # Wait for follow confirmation
            await page.wait_for_timeout(2000)
            
            # Verify follow was successful
            follow_success = await self._verify_follow_success(page)
            
            if follow_success:
                logger.info("Follow action completed successfully")
                return {"success": True}
            else:
                return {"success": False, "error": "Follow verification failed"}
            
        except Exception as e:
            logger.error(f"Error executing follow action: {e}")
            return {"success": False, "error": str(e)}
    
    async def _post_follow_behavior(self, page, mobile_mode: bool):
        """Execute post-follow behavior"""
        
        try:
            logger.debug("Executing post-follow behavior")
            
            # Brief pause after follow
            await self.human_behavior.human_delay(1.0, 3.0, "general")
            
            # Occasionally look at more content
            if random.random() < 0.4:
                if mobile_mode:
                    await self.human_behavior.simulate_mobile_gestures(page, "swipe")
                else:
                    await self.human_behavior.human_scroll(
                        page,
                        direction="down",
                        distance=random.randint(200, 500)
                    )
                
                await self.human_behavior.human_delay(2.0, 5.0, "general")
            
            # Random distraction simulation
            if random.random() < 0.2:
                await self.human_behavior.random_distraction(page)
            
        except Exception as e:
            logger.error(f"Error in post-follow behavior: {e}")
    
    async def _check_profile_exists(self, page) -> bool:
        """Check if profile exists and is accessible"""
        
        try:
            # Check for error indicators
            error_indicators = [
                "text=User not found",
                "text=This account is private",
                "text=Couldn't find this account",
                "[data-e2e='user-not-found']"
            ]
            
            for indicator in error_indicators:
                element = await page.query_selector(indicator)
                if element:
                    return False
            
            # Check for profile elements
            profile_indicators = [
                "[data-e2e='user-title']",
                "[data-e2e='user-bio']",
                "[data-e2e='follow-button']"
            ]
            
            for indicator in profile_indicators:
                element = await page.query_selector(indicator)
                if element:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking profile existence: {e}")
            return False
    
    async def _verify_follow_success(self, page) -> bool:
        """Verify that follow action was successful"""
        
        try:
            # Wait a bit for UI to update
            await page.wait_for_timeout(2000)
            
            # Check for follow confirmation indicators
            success_indicators = [
                "button:has-text('Following')",
                "button:has-text('Đang theo dõi')",
                "[data-e2e='follow-button']:has-text('Following')"
            ]
            
            for indicator in success_indicators:
                element = await page.query_selector(indicator)
                if element:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying follow success: {e}")
            return False
    
    async def _check_rate_limits(self, pattern_config: Dict[str, Any]) -> bool:
        """Check if rate limits allow following"""
        
        try:
            # Check daily limit
            if self.session_follows >= pattern_config["daily_limit"]:
                logger.warning(f"Daily follow limit reached: {self.session_follows}")
                return False
            
            # Check time-based limits
            if self.last_follow_time:
                time_since_last = (datetime.utcnow() - self.last_follow_time).total_seconds()
                if time_since_last < pattern_config["min_interval"]:
                    logger.warning(f"Follow interval too short: {time_since_last}s")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limits: {e}")
            return False
    
    def _update_follow_statistics(self):
        """Update follow statistics"""
        
        self.session_follows += 1
        self.last_follow_time = datetime.utcnow()
    
    def _calculate_next_interval(self, pattern_config: Dict[str, Any]) -> int:
        """Calculate next follow interval with randomization"""
        
        base_interval = random.randint(
            pattern_config["min_interval"],
            pattern_config["max_interval"]
        )
        
        # Add fatigue factor
        fatigue_multiplier = 1.0 + (self.human_behavior.fatigue_level * 0.3)
        
        # Add time-of-day factor
        current_hour = datetime.utcnow().hour
        if 22 <= current_hour or current_hour <= 6:  # Night time
            fatigue_multiplier *= 1.2
        
        return int(base_interval * fatigue_multiplier)
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """Get current session statistics"""
        
        session_duration = (datetime.utcnow() - self.session_start_time).total_seconds()
        
        return {
            "session_follows": self.session_follows,
            "session_duration": session_duration,
            "follows_per_hour": (self.session_follows / (session_duration / 3600)) if session_duration > 0 else 0,
            "current_pattern": self.current_pattern,
            "fatigue_level": self.human_behavior.fatigue_level,
            "last_follow_time": self.last_follow_time.isoformat() if self.last_follow_time else None
        }

"""
Human Behavior Simulation for TikTok Automation
"""

import random
import asyncio
import math
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta
from loguru import logger

from camoufox_integration.antidetect_config import AntidetectConfig


class HumanBehavior:
    """Simulates human-like behavior patterns for TikTok automation"""
    
    def __init__(self):
        self.antidetect_config = AntidetectConfig()
        self.session_start_time = datetime.utcnow()
        self.action_history = []
        self.fatigue_level = 0.0  # 0.0 = fresh, 1.0 = exhausted
        
        # Behavior patterns
        self.typing_speed_wpm = random.randint(40, 80)
        self.mouse_speed = random.uniform(0.8, 1.5)
        self.attention_span = random.randint(300, 1800)  # 5-30 minutes
        
    async def human_delay(
        self,
        min_seconds: float = 1.0,
        max_seconds: float = 3.0,
        action_type: str = "general"
    ) -> float:
        """Generate human-like delay with fatigue consideration"""
        
        # Base delay from antidetect config
        base_delay = self.antidetect_config.get_human_delay(min_seconds, max_seconds)
        
        # Apply fatigue factor
        fatigue_multiplier = 1.0 + (self.fatigue_level * 0.5)
        
        # Apply action-specific modifiers
        action_modifiers = {
            "follow": random.uniform(0.8, 1.2),
            "unfollow": random.uniform(1.0, 1.4),
            "like": random.uniform(0.6, 1.0),
            "comment": random.uniform(1.5, 2.5),
            "scroll": random.uniform(0.3, 0.8),
            "click": random.uniform(0.5, 1.0),
            "type": random.uniform(1.2, 2.0)
        }
        
        modifier = action_modifiers.get(action_type, 1.0)
        final_delay = base_delay * fatigue_multiplier * modifier
        
        # Add occasional longer pauses (thinking/distraction)
        if random.random() < 0.05:  # 5% chance
            final_delay += random.uniform(2.0, 8.0)
        
        # Update fatigue
        self._update_fatigue()
        
        logger.debug(f"Human delay for {action_type}: {final_delay:.2f}s (fatigue: {self.fatigue_level:.2f})")
        
        await asyncio.sleep(final_delay)
        return final_delay
    
    async def human_mouse_movement(
        self,
        page,
        target_selector: str,
        approach: str = "natural"
    ) -> bool:
        """Simulate human-like mouse movement to target element"""
        
        try:
            # Get target element
            target = await page.wait_for_selector(target_selector, timeout=10000)
            if not target:
                return False
            
            # Get element position
            box = await target.bounding_box()
            if not box:
                return False
            
            # Calculate target position (center of element with slight randomness)
            target_x = box["x"] + box["width"] / 2 + random.uniform(-10, 10)
            target_y = box["y"] + box["height"] / 2 + random.uniform(-5, 5)
            
            # Get current mouse position (approximate)
            current_pos = await page.evaluate("() => ({ x: window.mouseX || 0, y: window.mouseY || 0 })")
            start_x = current_pos.get("x", random.randint(100, 500))
            start_y = current_pos.get("y", random.randint(100, 300))
            
            # Generate movement path
            if approach == "natural":
                path = self._generate_natural_mouse_path(
                    start_x, start_y, target_x, target_y
                )
            else:
                path = self._generate_direct_mouse_path(
                    start_x, start_y, target_x, target_y
                )
            
            # Execute mouse movement
            for i, (x, y) in enumerate(path):
                await page.mouse.move(x, y)
                
                # Add small delays between movements
                if i < len(path) - 1:
                    delay = random.uniform(0.01, 0.03) * self.mouse_speed
                    await asyncio.sleep(delay)
            
            # Small pause before action
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            return True
            
        except Exception as e:
            logger.error(f"Error in mouse movement: {e}")
            return False
    
    async def human_typing(
        self,
        page,
        selector: str,
        text: str,
        clear_first: bool = True
    ) -> bool:
        """Simulate human-like typing"""
        
        try:
            # Focus on input field
            await page.focus(selector)
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # Clear field if requested
            if clear_first:
                await page.keyboard.press("Control+A")
                await asyncio.sleep(random.uniform(0.05, 0.15))
                await page.keyboard.press("Delete")
                await asyncio.sleep(random.uniform(0.1, 0.2))
            
            # Generate typing pattern
            typing_pattern = self.antidetect_config.get_typing_pattern(text)
            
            # Type each character with human-like timing
            for char_data in typing_pattern:
                char = char_data["char"]
                delay = char_data["delay"] / 1000  # Convert to seconds
                
                # Apply typing speed variation
                speed_factor = 60 / self.typing_speed_wpm  # Base speed
                actual_delay = delay * speed_factor * random.uniform(0.7, 1.3)
                
                # Type character
                if char == " ":
                    await page.keyboard.press("Space")
                elif char.isupper():
                    # Handle capital letters
                    await page.keyboard.down("Shift")
                    await page.keyboard.press(char.lower())
                    await page.keyboard.up("Shift")
                else:
                    await page.keyboard.press(char)
                
                # Wait before next character
                await asyncio.sleep(actual_delay)
            
            # Small pause after typing
            await asyncio.sleep(random.uniform(0.2, 0.5))
            
            return True
            
        except Exception as e:
            logger.error(f"Error in human typing: {e}")
            return False
    
    async def human_scroll(
        self,
        page,
        direction: str = "down",
        distance: int = None,
        smooth: bool = True
    ) -> bool:
        """Simulate human-like scrolling"""
        
        try:
            if distance is None:
                distance = random.randint(200, 800)
            
            if direction == "up":
                distance = -distance
            
            if smooth:
                # Generate scroll pattern
                scroll_pattern = self.antidetect_config.get_scroll_pattern(distance)
                
                for scroll_data in scroll_pattern:
                    amount = scroll_data["amount"]
                    delay = scroll_data["delay"] / 1000
                    
                    # Execute scroll
                    await page.mouse.wheel(0, amount)
                    await asyncio.sleep(delay)
            else:
                # Direct scroll
                await page.mouse.wheel(0, distance)
            
            # Pause after scrolling
            await asyncio.sleep(random.uniform(0.3, 1.0))
            
            return True

    async def tiktok_natural_navigation(
        self,
        page,
        action_type: str = "explore"
    ) -> bool:
        """Simulate natural TikTok navigation patterns"""

        try:
            if action_type == "explore":
                # Simulate exploring TikTok like a real user

                # Random scroll to see content
                for _ in range(random.randint(2, 5)):
                    await self.human_scroll(page, direction="down", distance=random.randint(300, 800))
                    await self.human_delay(1.0, 3.0, "scroll")

                # Occasionally scroll back up
                if random.random() < 0.3:
                    await self.human_scroll(page, direction="up", distance=random.randint(200, 500))
                    await self.human_delay(0.5, 1.5, "scroll")

                # Random pause to "read" or "watch"
                await self.human_delay(2.0, 8.0, "general")

            elif action_type == "profile_visit":
                # Simulate visiting a user profile

                # Look at profile info
                await self.human_delay(1.0, 3.0, "general")

                # Scroll to see videos
                for _ in range(random.randint(1, 3)):
                    await self.human_scroll(page, direction="down", distance=random.randint(200, 600))
                    await self.human_delay(0.8, 2.0, "scroll")

                # Maybe scroll back to top
                if random.random() < 0.4:
                    await self.human_scroll(page, direction="up", distance=random.randint(300, 800))
                    await self.human_delay(0.5, 1.5, "scroll")

            elif action_type == "video_watch":
                # Simulate watching a video

                # Initial pause (video loading/starting)
                await self.human_delay(0.5, 2.0, "general")

                # Watch duration based on video length (simulate)
                watch_duration = random.uniform(3.0, 15.0)  # 3-15 seconds

                # During watching, occasionally move mouse or scroll slightly
                start_time = asyncio.get_event_loop().time()
                while (asyncio.get_event_loop().time() - start_time) < watch_duration:
                    # Small random movements
                    if random.random() < 0.3:
                        await page.mouse.move(
                            random.randint(100, 800),
                            random.randint(100, 600)
                        )

                    await asyncio.sleep(random.uniform(0.5, 2.0))

            return True

        except Exception as e:
            logger.error(f"Error in TikTok natural navigation: {e}")
            return False

    async def simulate_user_attention(
        self,
        page,
        element_selector: str,
        attention_duration: float = None
    ) -> bool:
        """Simulate user attention on specific element"""

        try:
            if attention_duration is None:
                attention_duration = random.uniform(1.0, 5.0)

            # Move mouse to element area
            element = await page.query_selector(element_selector)
            if element:
                box = await element.bounding_box()
                if box:
                    # Move to random point within element
                    target_x = box['x'] + random.uniform(0.2, 0.8) * box['width']
                    target_y = box['y'] + random.uniform(0.2, 0.8) * box['height']

                    await page.mouse.move(target_x, target_y)

            # Simulate attention with small movements
            start_time = asyncio.get_event_loop().time()
            while (asyncio.get_event_loop().time() - start_time) < attention_duration:
                # Small random movements to simulate reading/looking
                if random.random() < 0.4:
                    current_pos = await page.evaluate("() => ({ x: window.mouseX || 0, y: window.mouseY || 0 })")
                    new_x = current_pos.get('x', 400) + random.randint(-20, 20)
                    new_y = current_pos.get('y', 300) + random.randint(-10, 10)
                    await page.mouse.move(new_x, new_y)

                await asyncio.sleep(random.uniform(0.3, 1.0))

            return True

        except Exception as e:
            logger.error(f"Error simulating user attention: {e}")
            return False

    async def random_distraction(self, page) -> bool:
        """Simulate random user distractions"""

        try:
            distraction_type = random.choice([
                "mouse_movement",
                "scroll_pause",
                "tab_switch_simulation",
                "typing_pause"
            ])

            if distraction_type == "mouse_movement":
                # Random mouse movements
                for _ in range(random.randint(2, 5)):
                    await page.mouse.move(
                        random.randint(100, 1200),
                        random.randint(100, 800)
                    )
                    await asyncio.sleep(random.uniform(0.2, 0.8))

            elif distraction_type == "scroll_pause":
                # Scroll and pause (like reading something)
                await self.human_scroll(page, distance=random.randint(100, 300))
                await self.human_delay(2.0, 6.0, "general")

            elif distraction_type == "tab_switch_simulation":
                # Simulate brief tab switch (pause activity)
                await self.human_delay(1.0, 4.0, "general")

            elif distraction_type == "typing_pause":
                # Simulate typing somewhere else (brief pause)
                await self.human_delay(0.5, 2.0, "general")

            return True

        except Exception as e:
            logger.error(f"Error in random distraction: {e}")
            return False

    async def simulate_mobile_gestures(
        self,
        page,
        gesture_type: str = "swipe"
    ) -> bool:
        """Simulate mobile-like gestures for mobile view"""

        try:
            if gesture_type == "swipe":
                # Simulate swipe gesture
                start_x = random.randint(200, 600)
                start_y = random.randint(400, 600)
                end_x = start_x + random.randint(-50, 50)
                end_y = start_y - random.randint(200, 400)  # Swipe up

                await page.mouse.move(start_x, start_y)
                await page.mouse.down()

                # Smooth swipe movement
                steps = random.randint(10, 20)
                for i in range(steps):
                    progress = i / steps
                    current_x = start_x + (end_x - start_x) * progress
                    current_y = start_y + (end_y - start_y) * progress
                    await page.mouse.move(current_x, current_y)
                    await asyncio.sleep(random.uniform(0.01, 0.03))

                await page.mouse.up()

            elif gesture_type == "tap":
                # Simulate tap gesture
                tap_x = random.randint(100, 700)
                tap_y = random.randint(100, 600)

                await page.mouse.move(tap_x, tap_y)
                await page.mouse.click(tap_x, tap_y)

            elif gesture_type == "long_press":
                # Simulate long press
                press_x = random.randint(200, 600)
                press_y = random.randint(200, 500)

                await page.mouse.move(press_x, press_y)
                await page.mouse.down()
                await asyncio.sleep(random.uniform(0.8, 1.5))
                await page.mouse.up()

            return True

        except Exception as e:
            logger.error(f"Error simulating mobile gesture: {e}")
            return False
            
        except Exception as e:
            logger.error(f"Error in human scroll: {e}")
            return False
    
    async def human_click(
        self,
        page,
        selector: str,
        button: str = "left",
        hover_first: bool = True
    ) -> bool:
        """Simulate human-like clicking"""
        
        try:
            # Move mouse to element first
            if hover_first:
                success = await self.human_mouse_movement(page, selector)
                if not success:
                    return False
            
            # Get element
            element = await page.wait_for_selector(selector, timeout=5000)
            if not element:
                return False
            
            # Random click position within element
            box = await element.bounding_box()
            if box:
                click_x = box["x"] + random.uniform(0.2, 0.8) * box["width"]
                click_y = box["y"] + random.uniform(0.2, 0.8) * box["height"]
                
                # Click with human-like timing
                await page.mouse.click(click_x, click_y, button=button)
            else:
                # Fallback to element click
                await element.click(button=button)
            
            # Post-click delay
            await asyncio.sleep(random.uniform(0.1, 0.4))
            
            return True
            
        except Exception as e:
            logger.error(f"Error in human click: {e}")
            return False
    
    async def simulate_reading(self, content_length: int = 100) -> float:
        """Simulate time spent reading content"""
        
        # Average reading speed: 200-300 words per minute
        words_per_minute = random.randint(200, 300)
        estimated_words = content_length / 5  # Rough estimate: 5 chars per word
        
        # Calculate reading time
        reading_time = (estimated_words / words_per_minute) * 60  # Convert to seconds
        
        # Add variance and minimum time
        reading_time *= random.uniform(0.5, 1.5)
        reading_time = max(reading_time, 1.0)  # Minimum 1 second
        
        # Apply attention span factor
        session_duration = (datetime.utcnow() - self.session_start_time).total_seconds()
        if session_duration > self.attention_span:
            reading_time *= random.uniform(0.3, 0.7)  # Reduced attention
        
        await asyncio.sleep(reading_time)
        return reading_time
    
    async def take_break(self, reason: str = "fatigue") -> float:
        """Take a human-like break"""
        
        break_duration = self.antidetect_config.get_break_duration()
        
        logger.info(f"Taking break for {break_duration:.1f}s due to {reason}")
        
        # Reset fatigue during break
        self.fatigue_level = max(0.0, self.fatigue_level - 0.3)
        
        await asyncio.sleep(break_duration)
        return break_duration
    
    def should_take_break(self) -> bool:
        """Determine if a break should be taken"""
        
        session_duration = (datetime.utcnow() - self.session_start_time).total_seconds()
        
        # Check various break conditions
        conditions = [
            self.fatigue_level > 0.7,  # High fatigue
            session_duration > self.attention_span,  # Exceeded attention span
            len(self.action_history) > 50 and random.random() < 0.1,  # Random break
            self.antidetect_config.should_take_break(session_duration / 60)  # Config-based
        ]
        
        return any(conditions)
    
    def _update_fatigue(self):
        """Update fatigue level based on activity"""
        
        # Increase fatigue slightly with each action
        self.fatigue_level = min(1.0, self.fatigue_level + random.uniform(0.01, 0.03))
        
        # Fatigue increases faster during long sessions
        session_duration = (datetime.utcnow() - self.session_start_time).total_seconds()
        if session_duration > 3600:  # After 1 hour
            self.fatigue_level = min(1.0, self.fatigue_level + 0.02)
    
    def _generate_natural_mouse_path(
        self,
        start_x: float,
        start_y: float,
        end_x: float,
        end_y: float,
        num_points: int = 10
    ) -> List[Tuple[float, float]]:
        """Generate natural mouse movement path with curves"""
        
        path = []
        
        # Calculate distance and direction
        distance = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)
        
        # Adjust number of points based on distance
        num_points = max(5, min(20, int(distance / 50)))
        
        for i in range(num_points + 1):
            t = i / num_points
            
            # Bezier curve with random control points
            if i == 0:
                # Add slight curve to movement
                control_x = start_x + (end_x - start_x) * 0.3 + random.uniform(-50, 50)
                control_y = start_y + (end_y - start_y) * 0.3 + random.uniform(-30, 30)
            
            # Calculate position along curve
            x = start_x + (end_x - start_x) * t
            y = start_y + (end_y - start_y) * t
            
            # Add slight randomness
            if 0 < i < num_points:
                x += random.uniform(-5, 5)
                y += random.uniform(-3, 3)
            
            path.append((x, y))
        
        return path
    
    def _generate_direct_mouse_path(
        self,
        start_x: float,
        start_y: float,
        end_x: float,
        end_y: float,
        num_points: int = 5
    ) -> List[Tuple[float, float]]:
        """Generate direct mouse movement path"""
        
        path = []
        
        for i in range(num_points + 1):
            t = i / num_points
            x = start_x + (end_x - start_x) * t
            y = start_y + (end_y - start_y) * t
            path.append((x, y))
        
        return path
    
    def log_action(self, action_type: str, target: str, result: str):
        """Log action for behavior analysis"""
        
        action_data = {
            "timestamp": datetime.utcnow(),
            "action_type": action_type,
            "target": target,
            "result": result,
            "fatigue_level": self.fatigue_level
        }
        
        self.action_history.append(action_data)
        
        # Keep only recent actions
        if len(self.action_history) > 100:
            self.action_history = self.action_history[-100:]

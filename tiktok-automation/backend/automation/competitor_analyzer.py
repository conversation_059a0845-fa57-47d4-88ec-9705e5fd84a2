"""
Competitor Analyzer for TikTok automation
"""

import asyncio
import random
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from models.competitor import Competitor
from models.tiktok_account import Tik<PERSON>okAccount
from .tiktok_bot import TikTokBot


class CompetitorAnalyzer:
    """Analyzes competitors and manages follower targeting"""
    
    def __init__(self):
        self.tiktok_bot = TikTokBot()
        
        # Analysis cache
        self.follower_cache = {}
        self.analysis_cache = {}
        
        # Filtering strategies
        self.filter_strategies = {
            "engagement_based": self._filter_by_engagement,
            "follower_count": self._filter_by_follower_count,
            "activity_level": self._filter_by_activity,
            "content_similarity": self._filter_by_content_similarity,
            "geographic": self._filter_by_geographic,
            "demographic": self._filter_by_demographic
        }
    
    async def analyze_competitor(
        self,
        competitor: Competitor,
        account: TikTokAccount,
        analysis_depth: str = "basic"
    ) -> Dict[str, Any]:
        """Perform comprehensive competitor analysis"""
        
        try:
            logger.info(f"Starting competitor analysis for {competitor.username}")
            
            analysis_result = {
                "competitor_id": competitor.id,
                "username": competitor.username,
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "analysis_depth": analysis_depth,
                "profile_data": {},
                "follower_analysis": {},
                "content_analysis": {},
                "engagement_metrics": {},
                "recommendations": {}
            }
            
            # Initialize bot session
            profile = account.browser_profile
            if not profile:
                return {"error": "No browser profile assigned to account"}
            
            proxy = None
            if profile.proxy_id:
                # Get proxy from database
                pass  # TODO: Implement proxy retrieval
            
            session_success = await self.tiktok_bot.initialize_session(
                account, profile, proxy, headless=True
            )
            
            if not session_success:
                return {"error": "Failed to initialize bot session"}
            
            try:
                # 1. Profile Analysis
                profile_data = await self._analyze_profile(competitor.username)
                analysis_result["profile_data"] = profile_data
                
                # 2. Follower Analysis (if requested)
                if analysis_depth in ["detailed", "comprehensive"]:
                    follower_analysis = await self._analyze_followers(
                        competitor.username, 
                        sample_size=100 if analysis_depth == "detailed" else 500
                    )
                    analysis_result["follower_analysis"] = follower_analysis
                
                # 3. Content Analysis (if comprehensive)
                if analysis_depth == "comprehensive":
                    content_analysis = await self._analyze_content(competitor.username)
                    analysis_result["content_analysis"] = content_analysis
                
                # 4. Engagement Metrics
                engagement_metrics = await self._calculate_engagement_metrics(
                    profile_data, analysis_result.get("content_analysis", {})
                )
                analysis_result["engagement_metrics"] = engagement_metrics
                
                # 5. Generate Recommendations
                recommendations = await self._generate_recommendations(
                    competitor, analysis_result
                )
                analysis_result["recommendations"] = recommendations
                
                # Cache results
                self.analysis_cache[competitor.id] = analysis_result
                
                logger.info(f"Completed competitor analysis for {competitor.username}")
                return analysis_result
                
            finally:
                await self.tiktok_bot.cleanup_session()
                
        except Exception as e:
            logger.error(f"Error analyzing competitor {competitor.username}: {e}")
            return {"error": str(e)}
    
    async def get_targeted_followers(
        self,
        competitor: Competitor,
        account: TikTokAccount,
        target_count: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get filtered list of competitor's followers for targeting"""
        
        try:
            logger.info(f"Getting targeted followers from {competitor.username}")
            
            # Get filter criteria
            filter_criteria = filters or competitor.get_filter_criteria()
            
            # Check cache first
            cache_key = f"{competitor.id}_{target_count}_{hash(str(filter_criteria))}"
            if cache_key in self.follower_cache:
                cached_data = self.follower_cache[cache_key]
                if (datetime.utcnow() - cached_data["timestamp"]).hours < 24:
                    logger.info("Using cached follower data")
                    return cached_data["followers"][:target_count]
            
            # Initialize bot session
            profile = account.browser_profile
            if not profile:
                return []
            
            session_success = await self.tiktok_bot.initialize_session(
                account, profile, headless=True
            )
            
            if not session_success:
                return []
            
            try:
                # Get raw followers list
                raw_followers = await self.tiktok_bot.get_user_followers(
                    competitor.username, 
                    limit=target_count * 3  # Get more to allow for filtering
                )
                
                if not raw_followers:
                    return []
                
                # Apply filters
                filtered_followers = await self._apply_filters(
                    raw_followers, filter_criteria
                )
                
                # Sort by targeting score
                scored_followers = await self._score_followers(
                    filtered_followers, competitor, account
                )
                
                # Sort by score (highest first) and limit
                targeted_followers = sorted(
                    scored_followers, 
                    key=lambda x: x.get("targeting_score", 0), 
                    reverse=True
                )[:target_count]
                
                # Cache results
                self.follower_cache[cache_key] = {
                    "followers": targeted_followers,
                    "timestamp": datetime.utcnow()
                }
                
                logger.info(f"Found {len(targeted_followers)} targeted followers")
                return targeted_followers
                
            finally:
                await self.tiktok_bot.cleanup_session()
                
        except Exception as e:
            logger.error(f"Error getting targeted followers: {e}")
            return []
    
    async def _analyze_profile(self, username: str) -> Dict[str, Any]:
        """Analyze competitor's profile data"""
        
        try:
            # Navigate to profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await self.tiktok_bot.current_page.goto(profile_url, timeout=30000)
            
            # Wait for page load
            await asyncio.sleep(3)
            
            # Extract profile data
            profile_data = await self.tiktok_bot.current_page.evaluate("""
                () => {
                    const data = {};
                    
                    // Basic profile info
                    const usernameEl = document.querySelector('[data-e2e="user-title"]');
                    if (usernameEl) data.display_name = usernameEl.textContent.trim();
                    
                    const bioEl = document.querySelector('[data-e2e="user-bio"]');
                    if (bioEl) data.bio = bioEl.textContent.trim();
                    
                    const avatarEl = document.querySelector('[data-e2e="user-avatar"] img');
                    if (avatarEl) data.avatar_url = avatarEl.src;
                    
                    // Follower counts
                    const followersEl = document.querySelector('[data-e2e="followers-count"]');
                    if (followersEl) {
                        data.followers = parseInt(followersEl.textContent.replace(/[^0-9]/g, '')) || 0;
                    }
                    
                    const followingEl = document.querySelector('[data-e2e="following-count"]');
                    if (followingEl) {
                        data.following = parseInt(followingEl.textContent.replace(/[^0-9]/g, '')) || 0;
                    }
                    
                    const likesEl = document.querySelector('[data-e2e="likes-count"]');
                    if (likesEl) {
                        data.likes = parseInt(likesEl.textContent.replace(/[^0-9]/g, '')) || 0;
                    }
                    
                    // Video count
                    const videoElements = document.querySelectorAll('[data-e2e="user-post-item"]');
                    data.video_count = videoElements.length;
                    
                    // Verification status
                    const verifiedEl = document.querySelector('[data-e2e="user-verified"]');
                    data.is_verified = !!verifiedEl;
                    
                    // Account type indicators
                    const businessEl = document.querySelector('[data-e2e="user-business"]');
                    data.is_business = !!businessEl;
                    
                    return data;
                }
            """)
            
            # Calculate additional metrics
            if profile_data.get("followers") and profile_data.get("likes"):
                profile_data["engagement_rate"] = (
                    profile_data["likes"] / profile_data["followers"]
                ) * 100
            
            if profile_data.get("followers") and profile_data.get("following"):
                profile_data["follower_ratio"] = (
                    profile_data["followers"] / max(profile_data["following"], 1)
                )
            
            return profile_data
            
        except Exception as e:
            logger.error(f"Error analyzing profile {username}: {e}")
            return {}
    
    async def _analyze_followers(
        self, 
        username: str, 
        sample_size: int = 100
    ) -> Dict[str, Any]:
        """Analyze competitor's followers"""
        
        try:
            # Get follower sample
            followers = await self.tiktok_bot.get_user_followers(username, sample_size)
            
            if not followers:
                return {"error": "Could not retrieve followers"}
            
            analysis = {
                "total_analyzed": len(followers),
                "demographics": {},
                "activity_patterns": {},
                "engagement_levels": {},
                "account_types": {}
            }
            
            # Analyze follower patterns
            verified_count = 0
            business_count = 0
            high_follower_count = 0
            
            for follower in followers:
                # Count verified accounts
                if follower.get("is_verified"):
                    verified_count += 1
                
                # Count business accounts
                if follower.get("is_business"):
                    business_count += 1
                
                # Count high-follower accounts
                if follower.get("follower_count", 0) > 10000:
                    high_follower_count += 1
            
            analysis["account_types"] = {
                "verified_percentage": (verified_count / len(followers)) * 100,
                "business_percentage": (business_count / len(followers)) * 100,
                "high_follower_percentage": (high_follower_count / len(followers)) * 100
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing followers for {username}: {e}")
            return {"error": str(e)}
    
    async def _analyze_content(self, username: str) -> Dict[str, Any]:
        """Analyze competitor's content strategy"""
        
        try:
            # This would involve analyzing recent videos
            # For now, return basic structure
            return {
                "posting_frequency": "daily",  # Would be calculated
                "content_types": ["dance", "comedy", "educational"],  # Would be detected
                "hashtag_strategy": ["#fyp", "#viral", "#trending"],  # Would be extracted
                "optimal_posting_times": ["18:00", "20:00", "22:00"]  # Would be analyzed
            }
            
        except Exception as e:
            logger.error(f"Error analyzing content for {username}: {e}")
            return {}
    
    async def _calculate_engagement_metrics(
        self, 
        profile_data: Dict[str, Any], 
        content_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate engagement metrics"""
        
        metrics = {}
        
        if profile_data.get("followers") and profile_data.get("likes"):
            metrics["engagement_rate"] = (
                profile_data["likes"] / profile_data["followers"]
            ) * 100
        
        if profile_data.get("video_count") and profile_data.get("likes"):
            metrics["avg_likes_per_video"] = (
                profile_data["likes"] / max(profile_data["video_count"], 1)
            )
        
        # Growth rate estimation (would need historical data)
        metrics["estimated_growth_rate"] = "5%"  # Placeholder
        
        return metrics
    
    async def _generate_recommendations(
        self, 
        competitor: Competitor, 
        analysis_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate targeting recommendations"""
        
        recommendations = {
            "follow_strategy": {},
            "timing": {},
            "filtering": {},
            "risk_assessment": {}
        }
        
        profile_data = analysis_result.get("profile_data", {})
        engagement_metrics = analysis_result.get("engagement_metrics", {})
        
        # Follow strategy recommendations
        follower_count = profile_data.get("followers", 0)
        if follower_count > 1000000:
            recommendations["follow_strategy"]["approach"] = "conservative"
            recommendations["follow_strategy"]["daily_limit"] = 20
        elif follower_count > 100000:
            recommendations["follow_strategy"]["approach"] = "moderate"
            recommendations["follow_strategy"]["daily_limit"] = 50
        else:
            recommendations["follow_strategy"]["approach"] = "aggressive"
            recommendations["follow_strategy"]["daily_limit"] = 100
        
        # Timing recommendations
        recommendations["timing"]["best_hours"] = ["18:00-20:00", "21:00-23:00"]
        recommendations["timing"]["avoid_hours"] = ["02:00-06:00"]
        
        # Risk assessment
        engagement_rate = engagement_metrics.get("engagement_rate", 0)
        if engagement_rate > 10:
            recommendations["risk_assessment"]["level"] = "low"
        elif engagement_rate > 5:
            recommendations["risk_assessment"]["level"] = "medium"
        else:
            recommendations["risk_assessment"]["level"] = "high"
        
        return recommendations
    
    async def _apply_filters(
        self, 
        followers: List[Dict[str, Any]], 
        filter_criteria: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Apply filtering criteria to followers list"""
        
        filtered = []
        
        for follower in followers:
            # Apply each filter
            if self._passes_filters(follower, filter_criteria):
                filtered.append(follower)
        
        return filtered
    
    def _passes_filters(
        self, 
        follower: Dict[str, Any], 
        criteria: Dict[str, Any]
    ) -> bool:
        """Check if follower passes all filter criteria"""
        
        # Minimum followers filter
        min_followers = criteria.get("min_followers", 0)
        if follower.get("follower_count", 0) < min_followers:
            return False
        
        # Maximum followers filter
        max_followers = criteria.get("max_followers")
        if max_followers and follower.get("follower_count", 0) > max_followers:
            return False
        
        # Verified only filter
        if criteria.get("verified_only") and not follower.get("is_verified"):
            return False
        
        # Exclude private accounts
        if criteria.get("exclude_private") and follower.get("is_private"):
            return False
        
        # Exclude business accounts
        if criteria.get("exclude_business") and follower.get("is_business"):
            return False
        
        return True
    
    async def _score_followers(
        self, 
        followers: List[Dict[str, Any]], 
        competitor: Competitor, 
        account: TikTokAccount
    ) -> List[Dict[str, Any]]:
        """Score followers for targeting priority"""
        
        scored_followers = []
        
        for follower in followers:
            score = 0
            
            # Base score
            score += 50
            
            # Follower count scoring
            follower_count = follower.get("follower_count", 0)
            if 1000 <= follower_count <= 50000:
                score += 30  # Sweet spot
            elif follower_count < 1000:
                score += 10  # Too small
            else:
                score += 5   # Too big
            
            # Engagement scoring
            engagement_rate = follower.get("engagement_rate", 0)
            if engagement_rate > 5:
                score += 20
            elif engagement_rate > 2:
                score += 10
            
            # Verification penalty (harder to convert)
            if follower.get("is_verified"):
                score -= 10
            
            # Business account bonus (more likely to follow back)
            if follower.get("is_business"):
                score += 15
            
            # Recent activity bonus
            if follower.get("last_active_days", 30) < 7:
                score += 10
            
            # Random factor for natural distribution
            score += random.randint(-5, 5)
            
            follower["targeting_score"] = max(0, score)
            scored_followers.append(follower)
        
        return scored_followers
    
    # Filter strategy implementations
    async def _filter_by_engagement(self, followers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter by engagement rate"""
        return [f for f in followers if f.get("engagement_rate", 0) > 2]
    
    async def _filter_by_follower_count(self, followers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter by follower count range"""
        return [f for f in followers if 1000 <= f.get("follower_count", 0) <= 100000]
    
    async def _filter_by_activity(self, followers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter by recent activity"""
        return [f for f in followers if f.get("last_active_days", 30) < 14]
    
    async def _filter_by_content_similarity(self, followers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter by content similarity (placeholder)"""
        return followers  # Would implement content analysis
    
    async def _filter_by_geographic(self, followers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter by geographic location (placeholder)"""
        return followers  # Would implement geo filtering
    
    async def _filter_by_demographic(self, followers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter by demographic data (placeholder)"""
        return followers  # Would implement demographic filtering

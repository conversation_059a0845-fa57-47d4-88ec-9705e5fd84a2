"""
Rate Limiter for TikTok Automation to prevent detection and bans
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from collections import defaultdict, deque
from loguru import logger

from core.config import settings


class RateLimiter:
    """Advanced rate limiter with multiple strategies to avoid detection"""
    
    def __init__(self):
        # Action counters per account
        self.action_counters = defaultdict(lambda: defaultdict(int))
        self.action_timestamps = defaultdict(lambda: defaultdict(deque))
        
        # Global rate limiting
        self.global_counters = defaultdict(int)
        self.global_timestamps = defaultdict(deque)
        
        # Account-specific limits (can be customized per account)
        self.account_limits = {}
        
        # Default limits from settings
        self.default_limits = {
            "follow": {
                "per_hour": settings.TIKTOK_FOLLOW_LIMIT_PER_HOUR,
                "per_day": settings.TIKTOK_FOLLOW_LIMIT_PER_DAY,
                "burst_limit": 5,  # Max consecutive actions
                "burst_window": 300,  # 5 minutes
                "cooldown_after_burst": 600  # 10 minutes
            },
            "unfollow": {
                "per_hour": 30,
                "per_day": 100,
                "burst_limit": 3,
                "burst_window": 300,
                "cooldown_after_burst": 900
            },
            "like": {
                "per_hour": 100,
                "per_day": 500,
                "burst_limit": 10,
                "burst_window": 180,
                "cooldown_after_burst": 300
            },
            "comment": {
                "per_hour": 20,
                "per_day": 50,
                "burst_limit": 2,
                "burst_window": 600,
                "cooldown_after_burst": 1200
            },
            "view": {
                "per_hour": 500,
                "per_day": 2000,
                "burst_limit": 20,
                "burst_window": 120,
                "cooldown_after_burst": 180
            }
        }
        
        # Adaptive limits based on account age and activity
        self.adaptive_factors = {
            "new_account": 0.5,  # 50% of normal limits for new accounts
            "verified_account": 1.5,  # 150% for verified accounts
            "high_follower_account": 1.3,  # 130% for accounts with many followers
            "suspicious_activity": 0.3  # 30% if suspicious activity detected
        }
    
    async def can_perform_action(
        self,
        account_id: int,
        action_type: str,
        check_global: bool = True
    ) -> Dict[str, Any]:
        """Check if action can be performed within rate limits"""
        
        try:
            # Get limits for this account and action
            limits = self._get_limits_for_account(account_id, action_type)
            
            # Check various rate limiting conditions
            checks = {
                "hourly_limit": self._check_hourly_limit(account_id, action_type, limits),
                "daily_limit": self._check_daily_limit(account_id, action_type, limits),
                "burst_limit": self._check_burst_limit(account_id, action_type, limits),
                "cooldown": self._check_cooldown(account_id, action_type, limits),
                "global_limit": self._check_global_limit(action_type) if check_global else True
            }
            
            # Calculate wait time if any check fails
            wait_time = 0
            failed_checks = []
            
            for check_name, (passed, wait) in checks.items():
                if not passed:
                    failed_checks.append(check_name)
                    wait_time = max(wait_time, wait)
            
            can_proceed = all(passed for passed, _ in checks.values())
            
            result = {
                "can_proceed": can_proceed,
                "wait_time": wait_time,
                "failed_checks": failed_checks,
                "limits": limits,
                "current_counts": self._get_current_counts(account_id, action_type)
            }
            
            if not can_proceed:
                logger.warning(
                    f"Rate limit hit for account {account_id}, action {action_type}. "
                    f"Wait {wait_time}s. Failed: {failed_checks}"
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking rate limits: {e}")
            return {
                "can_proceed": False,
                "wait_time": 60,
                "failed_checks": ["error"],
                "error": str(e)
            }
    
    async def record_action(
        self,
        account_id: int,
        action_type: str,
        success: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record an action for rate limiting tracking"""
        
        try:
            now = datetime.utcnow()
            
            # Record for account-specific tracking
            self.action_counters[account_id][action_type] += 1
            self.action_timestamps[account_id][action_type].append({
                "timestamp": now,
                "success": success,
                "metadata": metadata or {}
            })
            
            # Record for global tracking
            self.global_counters[action_type] += 1
            self.global_timestamps[action_type].append({
                "timestamp": now,
                "account_id": account_id,
                "success": success
            })
            
            # Cleanup old records
            await self._cleanup_old_records(account_id, action_type)
            
            logger.debug(f"Recorded action: account={account_id}, type={action_type}, success={success}")
            
        except Exception as e:
            logger.error(f"Error recording action: {e}")
    
    async def wait_for_rate_limit(
        self,
        account_id: int,
        action_type: str,
        max_wait: int = 3600
    ) -> bool:
        """Wait until action can be performed within rate limits"""
        
        try:
            check_result = await self.can_perform_action(account_id, action_type)
            
            if check_result["can_proceed"]:
                return True
            
            wait_time = min(check_result["wait_time"], max_wait)
            
            if wait_time > max_wait:
                logger.warning(f"Wait time {wait_time}s exceeds max wait {max_wait}s")
                return False
            
            logger.info(f"Waiting {wait_time}s for rate limit: account={account_id}, action={action_type}")
            await asyncio.sleep(wait_time)
            
            # Check again after waiting
            final_check = await self.can_perform_action(account_id, action_type)
            return final_check["can_proceed"]
            
        except Exception as e:
            logger.error(f"Error waiting for rate limit: {e}")
            return False
    
    def set_account_limits(
        self,
        account_id: int,
        limits: Dict[str, Dict[str, int]]
    ):
        """Set custom limits for specific account"""
        self.account_limits[account_id] = limits
        logger.info(f"Set custom limits for account {account_id}")
    
    def get_account_status(self, account_id: int) -> Dict[str, Any]:
        """Get current rate limiting status for account"""
        
        status = {
            "account_id": account_id,
            "actions": {},
            "next_available": {}
        }
        
        for action_type in self.default_limits.keys():
            limits = self._get_limits_for_account(account_id, action_type)
            counts = self._get_current_counts(account_id, action_type)
            
            status["actions"][action_type] = {
                "limits": limits,
                "current": counts,
                "remaining": {
                    "hourly": max(0, limits["per_hour"] - counts["hourly"]),
                    "daily": max(0, limits["per_day"] - counts["daily"])
                }
            }
            
            # Calculate next available time
            check_result = self.can_perform_action(account_id, action_type)
            if not check_result["can_proceed"]:
                next_time = datetime.utcnow() + timedelta(seconds=check_result["wait_time"])
                status["next_available"][action_type] = next_time.isoformat()
        
        return status
    
    def _get_limits_for_account(
        self,
        account_id: int,
        action_type: str
    ) -> Dict[str, int]:
        """Get rate limits for specific account and action"""
        
        # Start with default limits
        limits = self.default_limits.get(action_type, {}).copy()
        
        # Apply account-specific limits if set
        if account_id in self.account_limits:
            account_limits = self.account_limits[account_id].get(action_type, {})
            limits.update(account_limits)
        
        # Apply adaptive factors (would be based on account analysis)
        # For now, using default factor
        adaptive_factor = 1.0
        
        # Apply factor to numeric limits
        for key in ["per_hour", "per_day", "burst_limit"]:
            if key in limits:
                limits[key] = int(limits[key] * adaptive_factor)
        
        return limits
    
    def _check_hourly_limit(
        self,
        account_id: int,
        action_type: str,
        limits: Dict[str, int]
    ) -> tuple[bool, int]:
        """Check hourly rate limit"""
        
        hourly_count = self._count_actions_in_window(
            account_id, action_type, hours=1
        )
        
        if hourly_count >= limits["per_hour"]:
            # Calculate wait time until oldest action expires
            wait_time = self._calculate_wait_time_for_window(
                account_id, action_type, hours=1
            )
            return False, wait_time
        
        return True, 0
    
    def _check_daily_limit(
        self,
        account_id: int,
        action_type: str,
        limits: Dict[str, int]
    ) -> tuple[bool, int]:
        """Check daily rate limit"""
        
        daily_count = self._count_actions_in_window(
            account_id, action_type, hours=24
        )
        
        if daily_count >= limits["per_day"]:
            # Calculate wait time until oldest action expires
            wait_time = self._calculate_wait_time_for_window(
                account_id, action_type, hours=24
            )
            return False, wait_time
        
        return True, 0
    
    def _check_burst_limit(
        self,
        account_id: int,
        action_type: str,
        limits: Dict[str, int]
    ) -> tuple[bool, int]:
        """Check burst rate limit"""
        
        burst_window = limits.get("burst_window", 300)  # 5 minutes default
        burst_count = self._count_actions_in_window(
            account_id, action_type, seconds=burst_window
        )
        
        if burst_count >= limits.get("burst_limit", 5):
            cooldown = limits.get("cooldown_after_burst", 600)
            return False, cooldown
        
        return True, 0
    
    def _check_cooldown(
        self,
        account_id: int,
        action_type: str,
        limits: Dict[str, int]
    ) -> tuple[bool, int]:
        """Check if account is in cooldown period"""
        
        # Get last action time
        timestamps = self.action_timestamps[account_id][action_type]
        if not timestamps:
            return True, 0
        
        last_action = timestamps[-1]["timestamp"]
        min_delay = settings.TIKTOK_API_DELAY_MIN
        
        time_since_last = (datetime.utcnow() - last_action).total_seconds()
        
        if time_since_last < min_delay:
            wait_time = min_delay - time_since_last
            return False, int(wait_time)
        
        return True, 0
    
    def _check_global_limit(self, action_type: str) -> tuple[bool, int]:
        """Check global rate limits across all accounts"""
        
        # Simple global limit: max 1000 actions per hour across all accounts
        global_hourly = self._count_global_actions_in_window(action_type, hours=1)
        
        if global_hourly >= 1000:
            return False, 300  # Wait 5 minutes
        
        return True, 0
    
    def _count_actions_in_window(
        self,
        account_id: int,
        action_type: str,
        hours: int = 0,
        seconds: int = 0
    ) -> int:
        """Count actions within time window"""
        
        if hours:
            cutoff = datetime.utcnow() - timedelta(hours=hours)
        else:
            cutoff = datetime.utcnow() - timedelta(seconds=seconds)
        
        timestamps = self.action_timestamps[account_id][action_type]
        return sum(1 for record in timestamps if record["timestamp"] > cutoff)
    
    def _count_global_actions_in_window(
        self,
        action_type: str,
        hours: int = 0,
        seconds: int = 0
    ) -> int:
        """Count global actions within time window"""
        
        if hours:
            cutoff = datetime.utcnow() - timedelta(hours=hours)
        else:
            cutoff = datetime.utcnow() - timedelta(seconds=seconds)
        
        timestamps = self.global_timestamps[action_type]
        return sum(1 for record in timestamps if record["timestamp"] > cutoff)
    
    def _calculate_wait_time_for_window(
        self,
        account_id: int,
        action_type: str,
        hours: int
    ) -> int:
        """Calculate wait time until window allows new action"""
        
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        timestamps = self.action_timestamps[account_id][action_type]
        
        # Find oldest action within window
        actions_in_window = [
            record for record in timestamps 
            if record["timestamp"] > cutoff
        ]
        
        if not actions_in_window:
            return 0
        
        oldest_action = min(actions_in_window, key=lambda x: x["timestamp"])
        wait_until = oldest_action["timestamp"] + timedelta(hours=hours)
        wait_time = (wait_until - datetime.utcnow()).total_seconds()
        
        return max(0, int(wait_time))
    
    def _get_current_counts(
        self,
        account_id: int,
        action_type: str
    ) -> Dict[str, int]:
        """Get current action counts for account"""
        
        return {
            "hourly": self._count_actions_in_window(account_id, action_type, hours=1),
            "daily": self._count_actions_in_window(account_id, action_type, hours=24),
            "total": len(self.action_timestamps[account_id][action_type])
        }
    
    async def _cleanup_old_records(self, account_id: int, action_type: str):
        """Remove old action records to prevent memory bloat"""
        
        cutoff = datetime.utcnow() - timedelta(days=7)  # Keep 7 days of history
        
        # Cleanup account records
        timestamps = self.action_timestamps[account_id][action_type]
        while timestamps and timestamps[0]["timestamp"] < cutoff:
            timestamps.popleft()
        
        # Cleanup global records
        global_timestamps = self.global_timestamps[action_type]
        while global_timestamps and global_timestamps[0]["timestamp"] < cutoff:
            global_timestamps.popleft()

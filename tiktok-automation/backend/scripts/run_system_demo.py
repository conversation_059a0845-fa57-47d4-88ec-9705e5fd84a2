#!/usr/bin/env python3
"""
System Demo Script for TikTok Automation
Demonstrates the complete optimized system with persistent sessions
"""

import asyncio
import sys
import os
from pathlib import Path
from loguru import logger

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from services.integrated_automation_service import integrated_automation_service
from services.monitoring_service import monitoring_service
from tests.test_integrated_system import run_integration_tests


async def demo_browserscan_test():
    """Demo anti-detection with browserscan.net"""
    
    logger.info("🔍 Demo: Testing Anti-Detection with browserscan.net")
    
    try:
        # Initialize service
        await integrated_automation_service.initialize()
        
        # Create demo profile
        from models.browser_profile import BrowserProfile
        from core.database import get_async_session
        from datetime import datetime
        
        async with get_async_session() as session:
            demo_profile = BrowserProfile(
                name=f"demo_browserscan_{int(datetime.utcnow().timestamp())}",
                description="Demo profile for browserscan test",
                fingerprint_config={},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                is_active=True
            )
            
            session.add(demo_profile)
            await session.commit()
            await session.refresh(demo_profile)
        
        # Start automation session
        session_result = await integrated_automation_service.start_automation_session(
            profile_id=demo_profile.id,
            automation_type="demo"
        )
        
        if not session_result["success"]:
            logger.error(f"Failed to start session: {session_result.get('error')}")
            return
        
        # Get browser context
        session_info = integrated_automation_service.active_sessions[demo_profile.id]
        context = session_info["context"]
        
        # Test browserscan.net
        page = await context.new_page()
        
        logger.info("📱 Navigating to browserscan.net...")
        await page.goto("https://www.browserscan.net/", timeout=30000)
        await page.wait_for_load_state("networkidle")
        
        logger.info("⏳ Waiting for scan to complete...")
        await asyncio.sleep(15)  # Wait for scan
        
        # Take screenshot
        screenshot_path = f"demo_browserscan_{int(datetime.utcnow().timestamp())}.png"
        await page.screenshot(path=screenshot_path)
        logger.info(f"📸 Screenshot saved: {screenshot_path}")
        
        # Check results
        page_content = await page.content()
        
        detection_indicators = [
            "automation detected",
            "bot detected", 
            "headless browser",
            "webdriver detected",
            "suspicious"
        ]
        
        detected = False
        for indicator in detection_indicators:
            if indicator.lower() in page_content.lower():
                detected = True
                logger.warning(f"⚠️ Detection indicator found: {indicator}")
                break
        
        if not detected:
            logger.info("✅ No detection indicators found - Anti-detection working!")
        
        await page.close()
        
        # Stop session
        await integrated_automation_service.stop_automation_session(demo_profile.id)
        
        # Shutdown
        await integrated_automation_service.shutdown()
        
        logger.info("🎉 Browserscan demo completed!")
        
    except Exception as e:
        logger.error(f"❌ Error in browserscan demo: {e}")
        try:
            await integrated_automation_service.shutdown()
        except:
            pass


async def demo_tiktok_automation():
    """Demo TikTok automation workflow"""
    
    logger.info("🎵 Demo: TikTok Automation Workflow")
    
    try:
        # Initialize service
        await integrated_automation_service.initialize()
        
        # Create demo profile
        from models.browser_profile import BrowserProfile
        from core.database import get_async_session
        from datetime import datetime
        
        async with get_async_session() as session:
            demo_profile = BrowserProfile(
                name=f"demo_tiktok_{int(datetime.utcnow().timestamp())}",
                description="Demo profile for TikTok automation",
                fingerprint_config={},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                is_active=True
            )
            
            session.add(demo_profile)
            await session.commit()
            await session.refresh(demo_profile)
        
        # Start automation session
        session_result = await integrated_automation_service.start_automation_session(
            profile_id=demo_profile.id,
            automation_type="demo"
        )
        
        if not session_result["success"]:
            logger.error(f"Failed to start session: {session_result.get('error')}")
            return
        
        # Get browser context
        session_info = integrated_automation_service.active_sessions[demo_profile.id]
        context = session_info["context"]
        
        # Demo TikTok navigation
        page = await context.new_page()
        
        logger.info("🌐 Navigating to TikTok...")
        response = await page.goto("https://www.tiktok.com/", timeout=30000)
        
        if response.status != 200:
            logger.error(f"Failed to load TikTok: {response.status}")
            return
        
        await page.wait_for_load_state("networkidle")
        
        # Check for detection
        detection_event = await monitoring_service.check_for_detection(
            page, demo_profile.id
        )
        
        if detection_event:
            logger.warning(f"⚠️ Detection event: {detection_event.detection_type}")
        else:
            logger.info("✅ No detection events - TikTok loaded successfully!")
        
        # Demo navigation to a profile
        logger.info("👤 Navigating to TikTok official profile...")
        await page.goto("https://www.tiktok.com/@tiktok", timeout=30000)
        await page.wait_for_load_state("networkidle")
        
        # Take screenshot
        screenshot_path = f"demo_tiktok_{int(datetime.utcnow().timestamp())}.png"
        await page.screenshot(path=screenshot_path)
        logger.info(f"📸 Screenshot saved: {screenshot_path}")
        
        # Demo human-like behavior
        logger.info("🤖 Demonstrating human-like behavior...")
        
        from automation.human_behavior import HumanBehavior
        human_behavior = HumanBehavior()
        
        # Simulate natural navigation
        await human_behavior.tiktok_natural_navigation(page, "profile_visit")
        
        await page.close()
        
        # Stop session
        await integrated_automation_service.stop_automation_session(demo_profile.id)
        
        # Shutdown
        await integrated_automation_service.shutdown()
        
        logger.info("🎉 TikTok automation demo completed!")
        
    except Exception as e:
        logger.error(f"❌ Error in TikTok demo: {e}")
        try:
            await integrated_automation_service.shutdown()
        except:
            pass


async def demo_persistent_sessions():
    """Demo persistent session functionality"""
    
    logger.info("💾 Demo: Persistent Session Management")
    
    try:
        # Initialize service
        await integrated_automation_service.initialize()
        
        # Create demo profile and account
        from models.browser_profile import BrowserProfile
        from models.tiktok_account import TikTokAccount
        from core.database import get_async_session
        from datetime import datetime
        
        async with get_async_session() as session:
            demo_profile = BrowserProfile(
                name=f"demo_persistent_{int(datetime.utcnow().timestamp())}",
                description="Demo profile for persistent sessions",
                fingerprint_config={},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                is_active=True
            )
            
            demo_account = TikTokAccount(
                username=f"demo_account_{int(datetime.utcnow().timestamp())}",
                email="<EMAIL>",
                is_active=True,
                is_logged_in=False
            )
            
            session.add(demo_profile)
            session.add(demo_account)
            await session.commit()
            await session.refresh(demo_profile)
            await session.refresh(demo_account)
        
        logger.info("🚀 Starting first session...")
        
        # Start first session
        session_result = await integrated_automation_service.start_automation_session(
            profile_id=demo_profile.id,
            account_id=demo_account.id,
            automation_type="demo"
        )
        
        if not session_result["success"]:
            logger.error(f"Failed to start session: {session_result.get('error')}")
            return
        
        # Get browser context
        session_info = integrated_automation_service.active_sessions[demo_profile.id]
        context = session_info["context"]
        
        # Navigate and set test data
        page = await context.new_page()
        await page.goto("https://www.tiktok.com/", timeout=30000)
        
        # Add test cookies
        test_cookies = [
            {
                "name": "demo_cookie",
                "value": f"demo_value_{int(datetime.utcnow().timestamp())}",
                "domain": ".tiktok.com",
                "path": "/"
            }
        ]
        
        await context.add_cookies(test_cookies)
        logger.info("🍪 Test cookies added")
        
        # Sync cookies
        sync_result = await integrated_automation_service.browser_pool.sync_persistent_session_cookies(
            session_info["instance_id"],
            demo_account.id
        )
        
        if sync_result:
            logger.info("✅ Cookies synced to database")
        
        await page.close()
        
        # Stop first session
        logger.info("⏹️ Stopping first session...")
        await integrated_automation_service.stop_automation_session(demo_profile.id, save_state=True)
        
        # Wait a moment
        await asyncio.sleep(2)
        
        logger.info("🔄 Starting second session to test persistence...")
        
        # Start second session
        session_result2 = await integrated_automation_service.start_automation_session(
            profile_id=demo_profile.id,
            account_id=demo_account.id,
            automation_type="demo"
        )
        
        if not session_result2["success"]:
            logger.error(f"Failed to start second session: {session_result2.get('error')}")
            return
        
        # Check if data persisted
        session_info2 = integrated_automation_service.active_sessions[demo_profile.id]
        context2 = session_info2["context"]
        
        page2 = await context2.new_page()
        await page2.goto("https://www.tiktok.com/", timeout=30000)
        
        # Get cookies
        cookies = await context2.cookies()
        demo_cookies = [cookie for cookie in cookies if cookie["name"] == "demo_cookie"]
        
        if demo_cookies:
            logger.info(f"✅ Persistent session working! Found {len(demo_cookies)} demo cookies")
        else:
            logger.warning("⚠️ Demo cookies not found - persistence may not be working")
        
        await page2.close()
        
        # Stop second session
        await integrated_automation_service.stop_automation_session(demo_profile.id)
        
        # Shutdown
        await integrated_automation_service.shutdown()
        
        logger.info("🎉 Persistent session demo completed!")
        
    except Exception as e:
        logger.error(f"❌ Error in persistent session demo: {e}")
        try:
            await integrated_automation_service.shutdown()
        except:
            pass


async def main():
    """Main demo function"""
    
    logger.info("🚀 TikTok Automation System Demo")
    logger.info("=" * 50)
    
    demos = [
        ("Anti-Detection Test (browserscan.net)", demo_browserscan_test),
        ("TikTok Automation Workflow", demo_tiktok_automation),
        ("Persistent Session Management", demo_persistent_sessions),
        ("Full Integration Tests", run_integration_tests)
    ]
    
    print("\nAvailable demos:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"{i}. {name}")
    print("0. Run all demos")
    
    try:
        choice = input("\nSelect demo to run (0-4): ").strip()
        
        if choice == "0":
            # Run all demos
            for name, demo_func in demos:
                logger.info(f"\n{'='*20} {name} {'='*20}")
                try:
                    await demo_func()
                except Exception as e:
                    logger.error(f"Demo failed: {e}")
                
                logger.info(f"{'='*60}")
        
        elif choice in ["1", "2", "3", "4"]:
            demo_index = int(choice) - 1
            name, demo_func = demos[demo_index]
            
            logger.info(f"\n{'='*20} {name} {'='*20}")
            await demo_func()
            logger.info(f"{'='*60}")
        
        else:
            logger.error("Invalid choice")
            return
    
    except KeyboardInterrupt:
        logger.info("\n👋 Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Demo error: {e}")
    
    logger.info("\n🎉 Demo completed!")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run demo
    asyncio.run(main())

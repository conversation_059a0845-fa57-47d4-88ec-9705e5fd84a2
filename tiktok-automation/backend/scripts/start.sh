#!/bin/bash

# TikTok Automation Backend Start Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Environment setup
export PYTHONPATH=/app:$PYTHONPATH

# Default values
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8000}
WORKERS=${WORKERS:-1}
LOG_LEVEL=${LOG_LEVEL:-info}
RELOAD=${RELOAD:-false}

log_info "Starting TikTok Automation Backend..."
log_info "Host: $HOST"
log_info "Port: $PORT"
log_info "Workers: $WORKERS"
log_info "Log Level: $LOG_LEVEL"

# Wait for dependencies (if any)
if [ ! -z "$WAIT_FOR_DB" ]; then
    log_info "Waiting for database..."
    sleep 5
fi

# Initialize database
log_info "Initializing database..."
python3 -c "
import asyncio
from core.database import init_db
asyncio.run(init_db())
print('Database initialized successfully')
" || {
    log_error "Database initialization failed"
    exit 1
}

# Create necessary directories
mkdir -p /app/data /app/logs /app/profiles
log_info "Directories created"

# Set up virtual display for browser automation (if needed)
if [ "$DISPLAY_MODE" = "virtual" ]; then
    log_info "Setting up virtual display..."
    export DISPLAY=:99
    Xvfb :99 -screen 0 1920x1080x24 &
    sleep 2
    fluxbox &
    log_success "Virtual display ready"
fi

# Health check function
health_check() {
    local max_attempts=30
    local attempt=1
    
    log_info "Performing health check..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:$PORT/api/v1/system/status &> /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Start the application
log_info "Starting FastAPI application..."

if [ "$RELOAD" = "true" ]; then
    # Development mode with auto-reload
    log_info "Running in development mode with auto-reload"
    python3 -m uvicorn main:app \
        --host $HOST \
        --port $PORT \
        --log-level $LOG_LEVEL \
        --reload \
        --reload-dir /app \
        --access-log &
else
    # Production mode with Gunicorn
    log_info "Running in production mode with Gunicorn"
    python3 -m gunicorn main:app \
        -w $WORKERS \
        -k uvicorn.workers.UvicornWorker \
        --bind $HOST:$PORT \
        --log-level $LOG_LEVEL \
        --access-logfile - \
        --error-logfile - \
        --timeout 120 \
        --keep-alive 5 \
        --max-requests 1000 \
        --max-requests-jitter 50 \
        --preload &
fi

# Store the PID
APP_PID=$!
log_info "Application started with PID: $APP_PID"

# Wait a moment for the app to start
sleep 5

# Perform health check
if health_check; then
    log_success "Backend started successfully!"
    log_info "API Documentation: http://$HOST:$PORT/docs"
    log_info "Health Check: http://$HOST:$PORT/api/v1/system/status"
else
    log_error "Backend failed to start properly"
    kill $APP_PID 2>/dev/null || true
    exit 1
fi

# Handle shutdown gracefully
shutdown() {
    log_info "Shutting down backend..."
    kill $APP_PID 2>/dev/null || true
    
    # Kill virtual display if running
    if [ "$DISPLAY_MODE" = "virtual" ]; then
        pkill Xvfb 2>/dev/null || true
        pkill fluxbox 2>/dev/null || true
    fi
    
    log_success "Backend shutdown complete"
    exit 0
}

# Set up signal handlers
trap shutdown SIGTERM SIGINT

# Wait for the application process
wait $APP_PID

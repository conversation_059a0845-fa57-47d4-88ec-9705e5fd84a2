# 🚀 Optimized TikTok Automation System

## 📋 Tổng Quan

Hệ thống TikTok automation được tối ưu hóa với **Camoufox Persistent Context**, **Multi-layer Cookie Management**, và **Advanced Anti-Detection** để thực hiện auto follow an toàn và hiệu quả.

## 🎯 Tính Năng Chính

### ✅ **Camoufox Persistent Context**
- **Session Persistence**: Tự động lưu cookies, localStorage, sessionStorage
- **Profile Directory**: Mỗi profile có riêng user data directory
- **Session Recovery**: Khôi phục session khi browser restart
- **Memory Optimization**: Quản lý memory hiệu quả

### ✅ **Enhanced Cookie Management**
- **Multi-layer Storage**: Database + File backup + Storage state
- **Encryption**: Mã hóa cookies với AES-256
- **Validation**: Kiểm tra cookie validity và expiration
- **Auto-sync**: Đồng bộ cookies real-time

### ✅ **Advanced Anti-Detection**
- **Enhanced Fingerprinting**: BrowserForge integration với statistical distribution
- **IP-Timezone Matching**: Đồng bộ timezone với proxy location
- **Human Behavior**: Mouse movement, typing patterns, scroll behavior
- **Network Protection**: DNS leak prevention, WebRTC spoofing

### ✅ **Optimized Follow Workflow**
- **Pre-follow Behavior**: Xem profile, videos trước khi follow
- **Natural Interactions**: Like, comment, scroll như người thật
- **Rate Limiting**: Dynamic intervals với fatigue modeling
- **Pattern Modes**: Conservative, Moderate, Aggressive

### ✅ **Monitoring & Recovery**
- **Detection Monitoring**: CAPTCHA, rate limit, account block detection
- **Performance Tracking**: Duration, success rate, memory usage
- **Auto Recovery**: Exponential backoff, session refresh
- **Health Status**: Profile và system health monitoring

## 🏗️ Kiến Trúc Hệ Thống

```
┌─────────────────────────────────────────────────────────────┐
│                 Integrated Automation Service               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Browser Pool    │  │ Monitoring      │  │ Follow       │ │
│  │ Management      │  │ Service         │  │ Workflow     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Persistent      │  │ Cookie          │  │ Human        │ │
│  │ Session Service │  │ Service         │  │ Behavior     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Browser         │  │ Antidetect      │  │ Fingerprint  │ │
│  │ Manager         │  │ Config          │  │ Generator    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Camoufox Integration                     │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Cài Đặt & Sử Dụng

### 1. **Cài Đặt Dependencies**

```bash
# Cài đặt Camoufox
pip install camoufox[geoip]

# Cài đặt các dependencies khác
pip install -r requirements.txt
```

### 2. **Khởi Tạo Database**

```bash
# Chạy migrations
alembic upgrade head
```

### 3. **Chạy Demo System**

```bash
# Chạy demo script
python scripts/run_system_demo.py
```

### 4. **Chạy Integration Tests**

```bash
# Chạy tests
python tests/test_integrated_system.py
```

## 💻 Sử Dụng API

### **Khởi Tạo Service**

```python
from services.integrated_automation_service import integrated_automation_service

# Khởi tạo
await integrated_automation_service.initialize()
```

### **Tạo Automation Session**

```python
# Tạo session cho profile
session_result = await integrated_automation_service.start_automation_session(
    profile_id=1,
    account_id=1,
    automation_type="follow"
)
```

### **Thực Hiện Auto Follow**

```python
# Auto follow users
follow_result = await integrated_automation_service.execute_follow_automation(
    profile_id=1,
    target_usernames=["user1", "user2", "user3"],
    pattern="moderate",  # conservative, moderate, aggressive
    mobile_mode=False
)
```

### **Monitoring & Health Check**

```python
# Kiểm tra health status
health = await monitoring_service.get_profile_health_status(profile_id=1)
system_health = await monitoring_service.get_system_health_overview()
```

## 📊 Follow Patterns

### **Conservative Pattern**
- **Daily Limit**: 50 follows
- **Interval**: 45-180 seconds
- **Engagement**: 15% probability
- **Video Watch**: 80% probability

### **Moderate Pattern** (Recommended)
- **Daily Limit**: 100 follows
- **Interval**: 30-120 seconds
- **Engagement**: 25% probability
- **Video Watch**: 90% probability

### **Aggressive Pattern**
- **Daily Limit**: 200 follows
- **Interval**: 15-60 seconds
- **Engagement**: 35% probability
- **Video Watch**: 95% probability

## 🔧 Configuration

### **Environment Variables**

```bash
# Database
DATABASE_URL=sqlite+aiosqlite:///./tiktok_automation.db

# Camoufox
CAMOUFOX_HEADLESS=true
CAMOUFOX_TIMEOUT=30000
MAX_CONCURRENT_BROWSERS=10

# Data Directories
DATA_DIR=./data
PROFILES_DIR=./data/profiles
COOKIES_DIR=./data/cookies
```

### **Antidetect Configuration**

```python
# Trong antidetect_config.py
enhanced_config = {
    "timezone": "America/New_York",
    "locale:language": "en",
    "locale:region": "US",
    "geolocation:latitude": 40.7128,
    "geolocation:longitude": -74.0060,
    "canvas:noise": True,
    "webgl:noise": True,
    "audioContext:noise": True
}
```

## 📈 Performance Monitoring

### **Metrics Tracked**
- **Follow Duration**: Thời gian thực hiện follow
- **Success Rate**: Tỷ lệ thành công
- **Memory Usage**: Sử dụng RAM
- **CPU Usage**: Sử dụng CPU
- **Detection Events**: Số lần bị phát hiện

### **Health Status**
- **Healthy**: Hoạt động bình thường
- **Warning**: Có vấn đề nhỏ
- **Critical**: Cần can thiệp ngay

## 🛡️ Anti-Detection Features

### **Fingerprint Protection**
- **Canvas Fingerprinting**: Noise injection
- **WebGL Fingerprinting**: Vendor/renderer spoofing
- **Audio Context**: Sample rate randomization
- **Font Fingerprinting**: Metrics randomization

### **Behavior Simulation**
- **Mouse Movement**: Bezier curves với acceleration
- **Typing Patterns**: Variable WPM với mistakes
- **Scroll Behavior**: Natural momentum
- **Attention Modeling**: Reading patterns

### **Network Protection**
- **DNS Leak Prevention**: Custom DNS settings
- **WebRTC IP Spoofing**: Hide real IP
- **TLS Fingerprinting**: Randomize handshake
- **HTTP/2 Fingerprinting**: Vary parameters

## 🔄 Session Recovery

### **Automatic Recovery**
- **Session Validation**: Kiểm tra session validity
- **Cookie Restoration**: Khôi phục từ backup
- **State Recovery**: Restore từ storage state
- **Fallback Mechanisms**: Multiple recovery strategies

### **Manual Recovery**
- **CAPTCHA Handling**: Screenshot + manual intervention
- **Account Block**: Disable automation
- **Rate Limit**: Exponential backoff
- **Login Required**: Re-authentication flow

## 📝 Logging & Debugging

### **Log Levels**
- **INFO**: Thông tin chung
- **WARNING**: Cảnh báo
- **ERROR**: Lỗi
- **CRITICAL**: Lỗi nghiêm trọng

### **Debug Mode**
```python
# Enable debug logging
import logging
logging.getLogger().setLevel(logging.DEBUG)

# Camoufox debug
camoufox_config = {"debug": True}
```

## 🧪 Testing

### **Test Categories**
1. **Anti-Detection**: browserscan.net test
2. **TikTok Navigation**: Basic functionality
3. **Cookie Persistence**: Session management
4. **Follow Workflow**: Complete automation
5. **Monitoring System**: Detection & recovery

### **Running Tests**
```bash
# Chạy tất cả tests
python tests/test_integrated_system.py

# Chạy specific test
python -m pytest tests/test_integrated_system.py::TestIntegratedSystem::test_browserscan_detection
```

## 📚 Best Practices

### **Profile Management**
- Sử dụng unique fingerprint cho mỗi profile
- Rotate proxy regularly
- Monitor health status
- Backup session data

### **Follow Strategy**
- Bắt đầu với conservative pattern
- Tăng dần intensity
- Monitor detection events
- Adjust based on success rate

### **Security**
- Encrypt sensitive data
- Use secure proxy
- Regular health checks
- Monitor for detection

## 🆘 Troubleshooting

### **Common Issues**

**1. Browser Launch Failed**
```bash
# Kiểm tra Camoufox installation
pip install --upgrade camoufox

# Kiểm tra permissions
chmod +x /path/to/camoufox/binary
```

**2. Cookie Not Persisting**
```python
# Kiểm tra persistent context
persistent_context = True
user_data_dir = "/path/to/profile/data"
```

**3. Detection Events**
```python
# Kiểm tra antidetect config
config = antidetect_config.get_enhanced_config(base_config)
```

**4. Performance Issues**
```python
# Optimize browser pool
max_instances = 5  # Giảm số browser instances
cleanup_interval = 300  # Tăng cleanup interval
```

## 📞 Support

Để được hỗ trợ:
1. Kiểm tra logs trong `./logs/`
2. Chạy health check
3. Review monitoring data
4. Check system requirements

---

## 🎉 Kết Luận

Hệ thống TikTok automation này cung cấp giải pháp tối ưu với:
- ✅ **Cookie Persistence** qua Camoufox persistent context
- ✅ **Advanced Anti-Detection** với enhanced fingerprinting
- ✅ **Human-like Behavior** simulation
- ✅ **Comprehensive Monitoring** và recovery
- ✅ **Scalable Architecture** cho production use

Hệ thống đã được test với browserscan.net và real TikTok scenarios để đảm bảo hiệu quả và an toàn.

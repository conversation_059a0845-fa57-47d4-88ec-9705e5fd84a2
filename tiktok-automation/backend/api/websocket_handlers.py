"""
WebSocket event handlers for real-time communication
"""

import json
import asyncio
from typing import Dict, Any, Optional
from fastapi import WebSocket
from loguru import logger

from core.websocket_manager import WebSocketManager
from services.task_service import TaskService
from services.account_service import AccountService
from services.profile_service import ProfileService
from services.proxy_service import ProxyService


class WebSocketEventHandler:
    """Handles WebSocket events and real-time updates"""
    
    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.task_service = TaskService()
        self.account_service = AccountService()
        self.profile_service = ProfileService()
        self.proxy_service = ProxyService()
    
    async def handle_message(self, websocket: WebSocket, message: str):
        """Handle incoming WebSocket message"""
        
        try:
            data = json.loads(message)
            event_type = data.get("type")
            
            if event_type == "subscribe":
                await self._handle_subscribe(websocket, data)
            elif event_type == "unsubscribe":
                await self._handle_unsubscribe(websocket, data)
            elif event_type == "get_status":
                await self._handle_get_status(websocket, data)
            elif event_type == "task_action":
                await self._handle_task_action(websocket, data)
            elif event_type == "ping":
                await self._handle_ping(websocket, data)
            else:
                await self._send_error(websocket, f"Unknown event type: {event_type}")
                
        except json.JSONDecodeError:
            await self._send_error(websocket, "Invalid JSON format")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self._send_error(websocket, "Internal server error")
    
    async def _handle_subscribe(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle subscription request"""
        
        subscription = data.get("subscription")
        if not subscription:
            await self._send_error(websocket, "Subscription name required")
            return
        
        # Add subscription to WebSocket manager
        await self.websocket_manager._handle_subscribe(websocket, data)
        
        # Send initial data based on subscription type
        if subscription == "tasks":
            await self._send_initial_task_data(websocket)
        elif subscription == "accounts":
            await self._send_initial_account_data(websocket)
        elif subscription == "profiles":
            await self._send_initial_profile_data(websocket)
        elif subscription == "proxies":
            await self._send_initial_proxy_data(websocket)
        elif subscription == "system":
            await self._send_initial_system_data(websocket)
    
    async def _handle_unsubscribe(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle unsubscription request"""
        await self.websocket_manager._handle_unsubscribe(websocket, data)
    
    async def _handle_get_status(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle status request"""
        
        status_type = data.get("status_type")
        
        if status_type == "tasks":
            stats = await self.task_service.get_task_statistics()
            await self._send_response(websocket, "task_status", stats)
        elif status_type == "accounts":
            stats = await self.account_service.get_account_statistics()
            await self._send_response(websocket, "account_status", stats)
        elif status_type == "proxies":
            stats = await self.proxy_service.get_proxy_statistics()
            await self._send_response(websocket, "proxy_status", stats)
        elif status_type == "system":
            # Get system status
            system_status = {
                "memory_usage": self._get_memory_usage(),
                "active_connections": len(self.websocket_manager.active_connections),
                "uptime": self.websocket_manager.get_connection_stats()["uptime"]
            }
            await self._send_response(websocket, "system_status", system_status)
        else:
            await self._send_error(websocket, f"Unknown status type: {status_type}")
    
    async def _handle_task_action(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle task action request"""
        
        action = data.get("action")
        task_id = data.get("task_id")
        
        if not task_id:
            await self._send_error(websocket, "Task ID required")
            return
        
        try:
            if action == "start":
                result = await self.task_service.start_task(task_id)
            elif action == "pause":
                result = await self.task_service.pause_task(task_id)
            elif action == "resume":
                result = await self.task_service.resume_task(task_id)
            elif action == "stop":
                result = await self.task_service.stop_task(task_id)
            else:
                await self._send_error(websocket, f"Unknown action: {action}")
                return
            
            await self._send_response(websocket, "task_action_result", {
                "task_id": task_id,
                "action": action,
                "result": result
            })
            
            # Broadcast task update to all subscribers
            if result.get("success"):
                await self.websocket_manager.broadcast_task_update(
                    task_id, action, result
                )
                
        except Exception as e:
            logger.error(f"Error handling task action: {e}")
            await self._send_error(websocket, str(e))
    
    async def _handle_ping(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle ping request"""
        await self.websocket_manager._handle_ping(websocket, data)
    
    async def _send_initial_task_data(self, websocket: WebSocket):
        """Send initial task data to subscriber"""
        
        try:
            # Get active tasks
            tasks = await self.task_service.get_tasks(active_only=True, limit=50)
            
            task_data = []
            for task in tasks:
                task_data.append({
                    "id": task.id,
                    "name": task.name,
                    "status": task.status.value,
                    "progress": task.progress_percentage,
                    "type": task.task_type.value
                })
            
            await self._send_response(websocket, "initial_tasks", {
                "tasks": task_data,
                "count": len(task_data)
            })
            
        except Exception as e:
            logger.error(f"Error sending initial task data: {e}")
    
    async def _send_initial_account_data(self, websocket: WebSocket):
        """Send initial account data to subscriber"""
        
        try:
            # Get account statistics
            stats = await self.account_service.get_account_statistics()
            
            await self._send_response(websocket, "initial_accounts", stats)
            
        except Exception as e:
            logger.error(f"Error sending initial account data: {e}")
    
    async def _send_initial_profile_data(self, websocket: WebSocket):
        """Send initial profile data to subscriber"""
        
        try:
            # Get recent profiles
            profiles = await self.profile_service.get_profiles(limit=20)
            
            profile_data = []
            for profile in profiles:
                profile_data.append({
                    "id": profile.id,
                    "name": profile.name,
                    "is_active": profile.is_active,
                    "usage_count": profile.usage_count
                })
            
            await self._send_response(websocket, "initial_profiles", {
                "profiles": profile_data,
                "count": len(profile_data)
            })
            
        except Exception as e:
            logger.error(f"Error sending initial profile data: {e}")
    
    async def _send_initial_proxy_data(self, websocket: WebSocket):
        """Send initial proxy data to subscriber"""
        
        try:
            # Get proxy statistics
            stats = await self.proxy_service.get_proxy_statistics()
            
            await self._send_response(websocket, "initial_proxies", stats)
            
        except Exception as e:
            logger.error(f"Error sending initial proxy data: {e}")
    
    async def _send_initial_system_data(self, websocket: WebSocket):
        """Send initial system data to subscriber"""
        
        try:
            system_data = {
                "memory_usage": self._get_memory_usage(),
                "connections": self.websocket_manager.get_connection_stats(),
                "version": "1.0.0",
                "uptime": asyncio.get_event_loop().time()
            }
            
            await self._send_response(websocket, "initial_system", system_data)
            
        except Exception as e:
            logger.error(f"Error sending initial system data: {e}")
    
    async def _send_response(self, websocket: WebSocket, event_type: str, data: Any):
        """Send response to WebSocket client"""
        
        response = {
            "type": event_type,
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await self.websocket_manager.send_personal_message(websocket, response)
    
    async def _send_error(self, websocket: WebSocket, error_message: str):
        """Send error message to WebSocket client"""
        
        error_response = {
            "type": "error",
            "error": error_message,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await self.websocket_manager.send_personal_message(websocket, error_response)
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage"""
        
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "rss": round(memory_info.rss / 1024 / 1024, 2),  # MB
                "vms": round(memory_info.vms / 1024 / 1024, 2),  # MB
                "percent": round(process.memory_percent(), 2)
            }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}
    
    # Event broadcasting methods
    
    async def broadcast_task_progress(self, task_id: int, progress_data: Dict[str, Any]):
        """Broadcast task progress update"""
        
        await self.websocket_manager.broadcast_task_update(
            task_id, "progress", progress_data
        )
    
    async def broadcast_account_update(self, account_id: int, update_data: Dict[str, Any]):
        """Broadcast account update"""
        
        await self.websocket_manager.broadcast({
            "type": "account_update",
            "account_id": account_id,
            "data": update_data,
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="accounts")
    
    async def broadcast_system_alert(self, alert_type: str, message: str, severity: str = "info"):
        """Broadcast system alert"""
        
        await self.websocket_manager.broadcast_system_update("alert", {
            "alert_type": alert_type,
            "message": message,
            "severity": severity
        })
    
    async def broadcast_log_message(self, level: str, message: str, context: Optional[Dict[str, Any]] = None):
        """Broadcast log message"""
        
        await self.websocket_manager.broadcast_log_message(level, message, context)

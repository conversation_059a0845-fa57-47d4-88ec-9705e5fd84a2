"""
Settings API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional

from core.database import get_db
from models.follow_settings import FollowSettings

router = APIRouter(prefix="/api/v1/settings", tags=["settings"])


class FollowSettingsRequest(BaseModel):
    target_profile_url: str = ""
    videos_to_watch: int = 3
    watch_time_seconds: int = 30
    follows_per_day: int = 50
    follows_per_session: int = 10
    break_time_minutes: int = 3600


class FollowSettingsResponse(BaseModel):
    id: Optional[int] = None
    target_profile_url: str
    videos_to_watch: int
    watch_time_seconds: int
    follows_per_day: int
    follows_per_session: int
    break_time_minutes: int
    is_active: bool = True


@router.get("/follow", response_model=FollowSettingsResponse)
async def get_follow_settings(db: Session = Depends(get_db)):
    """Get current follow settings"""
    try:
        # Get the most recent active settings
        settings = db.query(FollowSettings).filter(
            FollowSettings.is_active == True
        ).order_by(FollowSettings.updated_at.desc()).first()
        
        if not settings:
            # Return default settings if none exist
            return FollowSettingsResponse(
                target_profile_url="",
                videos_to_watch=3,
                watch_time_seconds=30,
                follows_per_day=50,
                follows_per_session=10,
                break_time_minutes=3600,
                is_active=True
            )
        
        return FollowSettingsResponse(
            id=settings.id,
            target_profile_url=settings.target_profile_url,
            videos_to_watch=settings.videos_to_watch,
            watch_time_seconds=settings.watch_time_seconds,
            follows_per_day=settings.follows_per_day,
            follows_per_session=settings.follows_per_session,
            break_time_minutes=settings.break_time_minutes,
            is_active=settings.is_active
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get follow settings: {str(e)}")


@router.post("/follow", response_model=FollowSettingsResponse)
async def save_follow_settings(
    settings_data: FollowSettingsRequest,
    db: Session = Depends(get_db)
):
    """Save follow settings"""
    try:
        # Deactivate all existing settings
        db.query(FollowSettings).update({"is_active": False})
        
        # Create new settings
        new_settings = FollowSettings(
            target_profile_url=settings_data.target_profile_url,
            videos_to_watch=settings_data.videos_to_watch,
            watch_time_seconds=settings_data.watch_time_seconds,
            follows_per_day=settings_data.follows_per_day,
            follows_per_session=settings_data.follows_per_session,
            break_time_minutes=settings_data.break_time_minutes,
            is_active=True
        )
        
        db.add(new_settings)
        db.commit()
        db.refresh(new_settings)
        
        return FollowSettingsResponse(
            id=new_settings.id,
            target_profile_url=new_settings.target_profile_url,
            videos_to_watch=new_settings.videos_to_watch,
            watch_time_seconds=new_settings.watch_time_seconds,
            follows_per_day=new_settings.follows_per_day,
            follows_per_session=new_settings.follows_per_session,
            break_time_minutes=new_settings.break_time_minutes,
            is_active=new_settings.is_active
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to save follow settings: {str(e)}")

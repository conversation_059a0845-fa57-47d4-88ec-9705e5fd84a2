"""
System management API endpoints
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from core.database import check_db_health
from core.startup_optimizer import startup_optimizer
from core.update_manager import update_manager
from core.fingerprint_cache import fingerprint_cache
from core.browser_pool_manager import browser_pool_manager
from camoufox_integration.browser_manager import BrowserManager

router = APIRouter()


class SystemStatusResponse(BaseModel):
    status: str
    version: str
    database: str
    memory_usage: Dict[str, Any]
    browser_instances: int


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get system status and health"""
    try:
        # Check database health
        db_healthy = await check_db_health()
        
        # Get browser manager instance
        browser_manager = BrowserManager()
        memory_usage = await browser_manager.get_memory_usage()
        
        return SystemStatusResponse(
            status="healthy" if db_healthy else "unhealthy",
            version="1.0.0",
            database="connected" if db_healthy else "disconnected",
            memory_usage=memory_usage,
            browser_instances=memory_usage.get("total_browsers", 0)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Simple health check endpoint"""
    return {"status": "ok", "message": "TikTok Automation API is running"}


@router.get("/memory")
async def get_memory_usage():
    """Get detailed memory usage"""
    try:
        browser_manager = BrowserManager()
        memory_usage = await browser_manager.get_memory_usage()
        return memory_usage
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/startup-status")
async def get_startup_status():
    """Get application startup status"""
    try:
        status = startup_optimizer.get_initialization_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/optimize-memory")
async def optimize_memory():
    """Optimize memory usage"""
    try:
        result = await startup_optimizer.optimize_memory_usage()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/updates")
async def get_update_status():
    """Get update status"""
    try:
        status = update_manager.get_update_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/check-updates")
async def check_for_updates(force: bool = False):
    """Check for available updates"""
    try:
        updates = await update_manager.check_for_updates(force=force)
        return updates
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/download-update/{component}")
async def download_update(component: str):
    """Download an available update"""
    try:
        success = await update_manager.download_update(component)
        return {"success": success, "component": component}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance-stats")
async def get_performance_stats():
    """Get performance statistics"""
    try:
        stats = {
            "fingerprint_cache": fingerprint_cache.get_cache_stats(),
            "browser_pool": browser_pool_manager.get_pool_stats(),
            "startup": startup_optimizer.get_initialization_status()
        }
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup")
async def cleanup_resources():
    """Cleanup system resources"""
    try:
        browser_manager = BrowserManager()
        await browser_manager.cleanup_all()
        return {"message": "Resources cleaned up successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

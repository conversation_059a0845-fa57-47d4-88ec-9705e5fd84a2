"""
Antidetect testing and validation API endpoints
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from services.antidetect_service import AntidetectService
from models.browser_profile import BrowserProfile

router = APIRouter()

async def get_antidetect_service() -> AntidetectService:
    return AntidetectService()


class AntidetectTestRequest(BaseModel):
    profile_id: int
    test_sites: Optional[list] = Field(default=[
        "https://bot.sannysoft.com/",
        "https://intoli.com/blog/not-possible-to-block-chrome-headless/chrome-headless-test.html",
        "https://arh.antoinevastel.com/bots/areyouheadless",
        "https://pixelscan.net/",
        "https://browserleaks.com/webrtc"
    ])
    include_tiktok_test: bool = True
    headless: bool = False


class AntidetectTestResponse(BaseModel):
    success: bool
    profile_id: int
    test_results: Dict[str, Any]
    overall_score: float
    recommendations: list
    timestamp: str


@router.post("/test", response_model=AntidetectTestResponse)
async def test_antidetect_capabilities(
    request: AntidetectTestRequest,
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Test antidetect capabilities of a browser profile"""
    try:
        result = await service.run_comprehensive_test(
            profile_id=request.profile_id,
            test_sites=request.test_sites,
            include_tiktok_test=request.include_tiktok_test,
            headless=request.headless
        )
        return AntidetectTestResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test-sites")
async def get_available_test_sites(
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Get list of available antidetect test sites"""
    try:
        sites = await service.get_test_sites()
        return {"test_sites": sites}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/optimize/{profile_id}")
async def optimize_profile_antidetect(
    profile_id: int,
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Optimize profile configuration for better antidetect performance"""
    try:
        result = await service.optimize_profile(profile_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/fingerprint/generate")
async def generate_fingerprint(
    os_type: str = Query("windows", description="Operating system type"),
    browser_type: str = Query("firefox", description="Browser type"),
    location: Optional[str] = Query(None, description="Geographic location"),
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Generate a new fingerprint configuration"""
    try:
        fingerprint = await service.generate_fingerprint(
            os_type=os_type,
            browser_type=browser_type,
            location=location
        )
        return {"fingerprint": fingerprint}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate/tiktok")
async def validate_tiktok_access(
    profile_id: int,
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Validate that profile can access TikTok without detection"""
    try:
        result = await service.validate_tiktok_access(profile_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/detection")
async def get_detection_statistics(
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Get detection statistics across all profiles"""
    try:
        stats = await service.get_detection_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/benchmark")
async def run_antidetect_benchmark(
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Run comprehensive antidetect benchmark across multiple profiles"""
    try:
        result = await service.run_benchmark()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommendations/{profile_id}")
async def get_antidetect_recommendations(
    profile_id: int,
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Get personalized antidetect recommendations for a profile"""
    try:
        recommendations = await service.get_recommendations(profile_id)
        return {"recommendations": recommendations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stealth/enhance/{profile_id}")
async def enhance_stealth_configuration(
    profile_id: int,
    service: AntidetectService = Depends(get_antidetect_service)
):
    """Enhance stealth configuration for maximum antidetect performance"""
    try:
        result = await service.enhance_stealth_config(profile_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

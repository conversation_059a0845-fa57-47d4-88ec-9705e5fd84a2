"""
Automation API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from services.automation_service import AutomationService
from models.automation_task import AutomationTask

router = APIRouter()
automation_service = AutomationService()


class AutomationTaskCreate(BaseModel):
    profile_id: int = Field(..., description="Browser profile ID")
    task_type: str = Field(..., description="Type of automation task")
    target_data: Dict[str, Any] = Field(..., description="Target data for automation")
    config: Optional[Dict[str, Any]] = Field(default={}, description="Task configuration")


class AutomationTaskResponse(BaseModel):
    id: int
    profile_id: int
    task_type: str
    target_data: Dict[str, Any]
    config: Dict[str, Any]
    status: str
    session_id: Optional[str]
    results: Dict[str, Any]
    created_at: str
    started_at: Optional[str]
    completed_at: Optional[str]
    profile_name: Optional[str]

    class Config:
        from_attributes = True


class AutomationConfigUpdate(BaseModel):
    max_follows_per_day: Optional[int] = Field(None, ge=1, le=1000)
    max_unfollows_per_day: Optional[int] = Field(None, ge=1, le=1000)
    min_delay_between_actions: Optional[int] = Field(None, ge=10, le=300)
    max_delay_between_actions: Optional[int] = Field(None, ge=30, le=600)
    human_like_delays: Optional[bool] = None
    respect_rate_limits: Optional[bool] = None
    max_concurrent_profiles: Optional[int] = Field(None, ge=1, le=10)
    session_duration_minutes: Optional[int] = Field(None, ge=30, le=480)
    break_duration_minutes: Optional[int] = Field(None, ge=5, le=60)


@router.post("/tasks/", response_model=Dict[str, Any])
async def create_automation_task(task_data: AutomationTaskCreate):
    """Create a new automation task"""
    try:
        result = await automation_service.create_automation_task(
            profile_id=task_data.profile_id,
            task_type=task_data.task_type,
            target_data=task_data.target_data,
            config=task_data.config
        )
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/", response_model=List[AutomationTaskResponse])
async def get_automation_tasks(
    profile_id: Optional[int] = Query(None, description="Filter by profile ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Limit number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination")
):
    """Get automation tasks with optional filters"""
    try:
        tasks = await automation_service.get_automation_tasks(
            profile_id=profile_id,
            status=status,
            limit=limit,
            offset=offset
        )
        
        return [AutomationTaskResponse(**task.to_dict()) for task in tasks]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=AutomationTaskResponse)
async def get_automation_task(task_id: int):
    """Get a specific automation task"""
    try:
        task = await automation_service.get_automation_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return AutomationTaskResponse(**task.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/start")
async def start_automation_task(task_id: int):
    """Start an automation task"""
    try:
        result = await automation_service.start_automation_task(task_id)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/stop")
async def stop_automation_task(task_id: int):
    """Stop an automation task"""
    try:
        result = await automation_service.stop_automation_task(task_id)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/pause")
async def pause_automation_task(task_id: int):
    """Pause an automation task"""
    try:
        result = await automation_service.pause_automation_task(task_id)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/resume")
async def resume_automation_task(task_id: int):
    """Resume a paused automation task"""
    try:
        result = await automation_service.resume_automation_task(task_id)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}")
async def delete_automation_task(task_id: int):
    """Delete an automation task"""
    try:
        result = await automation_service.delete_automation_task(task_id)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/statistics")
async def get_task_statistics(task_id: int):
    """Get real-time statistics for an automation task"""
    try:
        stats = await automation_service.get_task_statistics(task_id)
        
        if "error" in stats:
            raise HTTPException(status_code=404, detail=stats["error"])
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/active")
async def get_active_sessions():
    """Get all active automation sessions"""
    try:
        sessions = await automation_service.get_active_sessions()
        return {"active_sessions": sessions}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sessions/stop-all")
async def stop_all_automation():
    """Stop all running automation tasks"""
    try:
        result = await automation_service.stop_all_automation()
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config/{profile_id}")
async def update_automation_config(
    profile_id: int,
    config: AutomationConfigUpdate
):
    """Update automation configuration for a profile"""
    try:
        # Convert to dict, excluding None values
        config_dict = {k: v for k, v in config.dict().items() if v is not None}
        
        result = await automation_service.update_automation_config(
            profile_id=profile_id,
            config=config_dict
        )
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/follow-users")
async def create_follow_users_task(
    profile_id: int = Body(...),
    usernames: List[str] = Body(...),
    config: Optional[Dict[str, Any]] = Body(default={})
):
    """Create a task to follow specific users"""
    try:
        result = await automation_service.create_automation_task(
            profile_id=profile_id,
            task_type="follow_users",
            target_data={"usernames": usernames},
            config=config
        )
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/unfollow-users")
async def create_unfollow_users_task(
    profile_id: int = Body(...),
    usernames: List[str] = Body(...),
    config: Optional[Dict[str, Any]] = Body(default={})
):
    """Create a task to unfollow specific users"""
    try:
        result = await automation_service.create_automation_task(
            profile_id=profile_id,
            task_type="unfollow_users",
            target_data={"usernames": usernames},
            config=config
        )
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/follow-competitors-followers")
async def create_follow_competitors_followers_task(
    profile_id: int = Body(...),
    competitor_username: str = Body(...),
    max_followers: int = Body(default=100),
    config: Optional[Dict[str, Any]] = Body(default={})
):
    """Create a task to follow a competitor's followers"""
    try:
        result = await automation_service.create_automation_task(
            profile_id=profile_id,
            task_type="follow_competitors_followers",
            target_data={
                "competitor_username": competitor_username,
                "max_followers": max_followers
            },
            config=config
        )
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

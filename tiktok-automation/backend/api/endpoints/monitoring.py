"""
Monitoring API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Query, HTTPException
from pydantic import BaseModel

from core.monitoring import get_monitor, MetricType, AlertLevel
from core.logging_system import get_logger, LogLevel, LogCategory

router = APIRouter()


class MetricResponse(BaseModel):
    name: str
    value: float
    unit: str
    timestamp: str
    metric_type: str
    tags: Optional[Dict[str, str]] = None


class AlertResponse(BaseModel):
    id: str
    title: str
    message: str
    level: str
    metric_name: str
    threshold: float
    current_value: float
    timestamp: str
    resolved: bool


class LogResponse(BaseModel):
    id: str
    timestamp: str
    level: str
    category: str
    message: str
    context: Optional[Dict[str, Any]] = None
    task_id: Optional[int] = None
    account_id: Optional[int] = None
    profile_id: Optional[int] = None
    session_id: Optional[str] = None


@router.get("/metrics/summary")
async def get_metrics_summary():
    """Get current metrics summary"""
    monitor = get_monitor()
    return monitor.get_metrics_summary()


@router.get("/metrics/history")
async def get_metrics_history(
    metric_type: Optional[str] = Query(None, description="Filter by metric type"),
    since_minutes: int = Query(60, description="Minutes of history to retrieve"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of metrics")
):
    """Get metrics history"""
    monitor = get_monitor()
    
    # Calculate since timestamp
    since = datetime.utcnow() - timedelta(minutes=since_minutes)
    
    if metric_type:
        try:
            metric_type_enum = MetricType(metric_type)
            metrics = monitor.metrics_collector.get_metrics_by_type(metric_type_enum, since)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid metric type: {metric_type}")
    else:
        # Get all metrics since timestamp
        metrics = [m for m in monitor.metrics_collector.metrics_history if m.timestamp >= since]
    
    # Limit results
    metrics = metrics[-limit:]
    
    return {
        "metrics": [
            {
                "name": m.name,
                "value": m.value,
                "unit": m.unit,
                "timestamp": m.timestamp.isoformat(),
                "metric_type": m.metric_type.value,
                "tags": m.tags
            }
            for m in metrics
        ],
        "count": len(metrics),
        "since": since.isoformat()
    }


@router.get("/alerts")
async def get_alerts(
    active_only: bool = Query(True, description="Return only active alerts"),
    level: Optional[str] = Query(None, description="Filter by alert level"),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of alerts")
):
    """Get alerts"""
    monitor = get_monitor()
    
    if active_only:
        alerts = monitor.alert_manager.get_active_alerts()
    else:
        alerts = monitor.alert_manager.alerts
    
    # Filter by level if specified
    if level:
        try:
            level_enum = AlertLevel(level)
            alerts = [a for a in alerts if a.level == level_enum]
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid alert level: {level}")
    
    # Limit results
    alerts = alerts[-limit:]
    
    return {
        "alerts": [
            {
                "id": a.id,
                "title": a.title,
                "message": a.message,
                "level": a.level.value,
                "metric_name": a.metric_name,
                "threshold": a.threshold,
                "current_value": a.current_value,
                "timestamp": a.timestamp.isoformat(),
                "resolved": a.resolved
            }
            for a in alerts
        ],
        "count": len(alerts)
    }


@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: str):
    """Resolve an alert"""
    monitor = get_monitor()
    monitor.alert_manager.resolve_alert(alert_id)
    
    return {"message": f"Alert {alert_id} resolved"}


@router.get("/alerts/summary")
async def get_alerts_summary():
    """Get alerts summary"""
    monitor = get_monitor()
    return monitor.get_alerts_summary()


@router.get("/logs")
async def get_logs(
    level: Optional[str] = Query(None, description="Filter by log level"),
    category: Optional[str] = Query(None, description="Filter by log category"),
    since_minutes: int = Query(60, description="Minutes of history to retrieve"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of logs"),
    task_id: Optional[int] = Query(None, description="Filter by task ID"),
    account_id: Optional[int] = Query(None, description="Filter by account ID")
):
    """Get logs with filtering"""
    logger_instance = get_logger()
    
    # Parse filters
    level_filter = None
    if level:
        try:
            level_filter = LogLevel(level)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid log level: {level}")
    
    category_filter = None
    if category:
        try:
            category_filter = LogCategory(category)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid log category: {category}")
    
    # Calculate since timestamp
    since = datetime.utcnow() - timedelta(minutes=since_minutes)
    
    # Get logs
    logs = logger_instance.get_logs(level_filter, category_filter, since, limit)
    
    # Additional filtering
    if task_id is not None:
        logs = [log for log in logs if log.get("task_id") == task_id]
    
    if account_id is not None:
        logs = [log for log in logs if log.get("account_id") == account_id]
    
    return {
        "logs": logs,
        "count": len(logs),
        "since": since.isoformat()
    }


@router.get("/logs/statistics")
async def get_log_statistics():
    """Get logging statistics"""
    logger_instance = get_logger()
    return logger_instance.get_log_statistics()


@router.get("/system/status")
async def get_system_status():
    """Get comprehensive system status"""
    monitor = get_monitor()
    logger_instance = get_logger()
    
    # Get latest system metrics
    system_metrics = monitor.metrics_collector.get_metrics_by_type(MetricType.SYSTEM)
    latest_metrics = {}
    
    for metric in system_metrics:
        if metric.name not in latest_metrics or metric.timestamp > latest_metrics[metric.name]["timestamp"]:
            latest_metrics[metric.name] = {
                "value": metric.value,
                "unit": metric.unit,
                "timestamp": metric.timestamp
            }
    
    # Get alerts summary
    alerts_summary = monitor.get_alerts_summary()
    
    # Get log statistics
    log_stats = logger_instance.get_log_statistics()
    
    # Calculate uptime
    uptime_metric = monitor.metrics_collector.get_latest_metric("uptime_seconds")
    uptime = uptime_metric.value if uptime_metric else 0
    
    return {
        "status": "healthy" if alerts_summary["active_alerts"] == 0 else "warning",
        "uptime_seconds": uptime,
        "metrics": latest_metrics,
        "alerts": alerts_summary,
        "logs": log_stats,
        "monitoring": {
            "is_running": monitor.is_running,
            "collection_interval": monitor.collection_interval
        }
    }


@router.get("/performance/summary")
async def get_performance_summary():
    """Get performance summary"""
    monitor = get_monitor()
    
    # Get performance-related metrics
    cpu_metric = monitor.metrics_collector.get_latest_metric("cpu_usage_percent")
    memory_metric = monitor.metrics_collector.get_latest_metric("memory_usage_percent")
    process_memory_metric = monitor.metrics_collector.get_latest_metric("process_memory_mb")
    
    # Get task metrics
    task_metrics = monitor.metrics_collector.get_metrics_by_type(MetricType.TASK)
    running_tasks = 0
    for metric in task_metrics:
        if metric.name == "tasks_running":
            running_tasks = int(metric.value)
            break
    
    # Get browser metrics
    browser_metrics = monitor.metrics_collector.get_metrics_by_type(MetricType.BROWSER)
    active_profiles = 0
    for metric in browser_metrics:
        if metric.name == "profiles_active":
            active_profiles = int(metric.value)
            break
    
    return {
        "cpu_usage": cpu_metric.value if cpu_metric else 0,
        "memory_usage": memory_metric.value if memory_metric else 0,
        "process_memory_mb": process_memory_metric.value if process_memory_metric else 0,
        "running_tasks": running_tasks,
        "active_profiles": active_profiles,
        "performance_score": _calculate_performance_score(
            cpu_metric.value if cpu_metric else 0,
            memory_metric.value if memory_metric else 0
        )
    }


@router.post("/monitoring/start")
async def start_monitoring():
    """Start monitoring system"""
    monitor = get_monitor()
    
    if monitor.is_running:
        return {"message": "Monitoring is already running"}
    
    await monitor.start()
    return {"message": "Monitoring started"}


@router.post("/monitoring/stop")
async def stop_monitoring():
    """Stop monitoring system"""
    monitor = get_monitor()
    
    if not monitor.is_running:
        return {"message": "Monitoring is not running"}
    
    await monitor.stop()
    return {"message": "Monitoring stopped"}


@router.get("/monitoring/config")
async def get_monitoring_config():
    """Get monitoring configuration"""
    monitor = get_monitor()
    
    return {
        "collection_interval": monitor.collection_interval,
        "metrics_buffer_size": monitor.metrics_collector.max_history_size,
        "alert_cooldown": monitor.alert_manager.alert_cooldown,
        "thresholds": monitor.alert_manager.thresholds
    }


@router.put("/monitoring/config")
async def update_monitoring_config(
    collection_interval: Optional[int] = None,
    alert_cooldown: Optional[int] = None
):
    """Update monitoring configuration"""
    monitor = get_monitor()
    
    if collection_interval is not None:
        if collection_interval < 1 or collection_interval > 300:
            raise HTTPException(
                status_code=400, 
                detail="Collection interval must be between 1 and 300 seconds"
            )
        monitor.collection_interval = collection_interval
    
    if alert_cooldown is not None:
        if alert_cooldown < 60 or alert_cooldown > 3600:
            raise HTTPException(
                status_code=400,
                detail="Alert cooldown must be between 60 and 3600 seconds"
            )
        monitor.alert_manager.alert_cooldown = alert_cooldown
    
    return {"message": "Configuration updated"}


def _calculate_performance_score(cpu_usage: float, memory_usage: float) -> int:
    """Calculate performance score (0-100)"""
    # Simple scoring algorithm
    cpu_score = max(0, 100 - cpu_usage)
    memory_score = max(0, 100 - memory_usage)
    
    # Weighted average (CPU is more important)
    performance_score = int((cpu_score * 0.6) + (memory_score * 0.4))
    
    return max(0, min(100, performance_score))

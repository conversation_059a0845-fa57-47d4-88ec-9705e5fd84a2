"""
Task management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field

from services.task_service import TaskService
from models.follow_task import FollowTask, TaskStatus, TaskType

router = APIRouter()

async def get_task_service() -> TaskService:
    return TaskService()


# Pydantic models for request/response
class TaskCreateRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    task_type: TaskType
    tiktok_account_id: int
    target_count: int = Field(..., ge=1, le=1000)
    competitor_id: Optional[int] = None
    browser_profile_id: Optional[int] = None
    delay_min: int = Field(2, ge=1, le=60)
    delay_max: int = Field(5, ge=1, le=120)
    filter_criteria: Optional[Dict[str, Any]] = None
    target_usernames: Optional[List[str]] = None
    scheduled_start: Optional[datetime] = None
    description: Optional[str] = Field(None, max_length=1000)


class TaskUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    target_count: Optional[int] = Field(None, ge=1, le=1000)
    delay_min: Optional[int] = Field(None, ge=1, le=60)
    delay_max: Optional[int] = Field(None, ge=1, le=120)
    filter_criteria: Optional[Dict[str, Any]] = None
    target_usernames: Optional[List[str]] = None
    scheduled_start: Optional[datetime] = None
    description: Optional[str] = Field(None, max_length=1000)


class TaskResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    task_type: str
    status: str
    tiktok_account_id: int
    competitor_id: Optional[int]
    browser_profile_id: Optional[int]
    target_count: int
    total_processed: int
    successful_actions: int
    failed_actions: int
    progress_percentage: float
    delay_min: int
    delay_max: int
    filter_criteria: Dict[str, Any]
    target_usernames: List[str]
    scheduled_start: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]
    error_message: Optional[str]
    log_entries: List[Dict[str, Any]]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    account_id: Optional[int] = Query(None, description="Filter by TikTok account ID"),
    status: Optional[TaskStatus] = Query(None, description="Filter by task status"),
    task_type: Optional[TaskType] = Query(None, description="Filter by task type"),
    active_only: bool = Query(False, description="Filter active tasks only"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Limit number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    service: TaskService = Depends(get_task_service)
):
    """Get list of automation tasks"""
    try:
        tasks = await service.get_tasks(
            account_id=account_id,
            status=status,
            task_type=task_type,
            active_only=active_only,
            limit=limit,
            offset=offset
        )
        return [TaskResponse.from_orm(task) for task in tasks]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Get task by ID"""
    try:
        task = await service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        return TaskResponse.from_orm(task)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=TaskResponse)
async def create_task(
    request: TaskCreateRequest,
    service: TaskService = Depends(get_task_service)
):
    """Create a new automation task"""
    try:
        task = await service.create_task(
            name=request.name,
            task_type=request.task_type,
            tiktok_account_id=request.tiktok_account_id,
            target_count=request.target_count,
            competitor_id=request.competitor_id,
            browser_profile_id=request.browser_profile_id,
            delay_min=request.delay_min,
            delay_max=request.delay_max,
            filter_criteria=request.filter_criteria,
            target_usernames=request.target_usernames,
            scheduled_start=request.scheduled_start,
            description=request.description
        )
        return TaskResponse.from_orm(task)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    request: TaskUpdateRequest,
    service: TaskService = Depends(get_task_service)
):
    """Update task (only if not running)"""
    try:
        task = await service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        if task.status in [TaskStatus.RUNNING]:
            raise HTTPException(status_code=400, detail="Cannot update running task")

        # Convert request to dict, excluding None values
        updates = {k: v for k, v in request.dict().items() if v is not None}

        # Update task fields
        for key, value in updates.items():
            if hasattr(task, key):
                setattr(task, key, value)

        # Save updated task
        await service._save_task(task)

        return TaskResponse.from_orm(task)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Delete task"""
    try:
        success = await service.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        return {"message": "Task deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/start")
async def start_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Start automation task"""
    try:
        result = await service.start_task(task_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/pause")
async def pause_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Pause automation task"""
    try:
        result = await service.pause_task(task_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/resume")
async def resume_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Resume paused task"""
    try:
        result = await service.resume_task(task_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/stop")
async def stop_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Stop automation task"""
    try:
        result = await service.stop_task(task_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/summary")
async def get_task_statistics(
    service: TaskService = Depends(get_task_service)
):
    """Get task statistics"""
    try:
        stats = await service.get_task_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/logs")
async def get_task_logs(
    task_id: int,
    limit: Optional[int] = Query(100, ge=1, le=1000, description="Limit number of log entries"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    service: TaskService = Depends(get_task_service)
):
    """Get task execution logs"""
    try:
        task = await service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Get logs with pagination
        logs = task.log_entries[offset:offset + limit] if task.log_entries else []

        return {
            "task_id": task_id,
            "total_logs": len(task.log_entries) if task.log_entries else 0,
            "logs": logs
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/progress")
async def get_task_progress(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Get real-time task progress"""
    try:
        task = await service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        return {
            "task_id": task_id,
            "status": task.status.value,
            "progress_percentage": task.progress_percentage,
            "total_processed": task.total_processed,
            "successful_actions": task.successful_actions,
            "failed_actions": task.failed_actions,
            "target_count": task.target_count,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "estimated_completion": None,  # TODO: Calculate based on current rate
            "current_action": task.log_entries[-1] if task.log_entries else None
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

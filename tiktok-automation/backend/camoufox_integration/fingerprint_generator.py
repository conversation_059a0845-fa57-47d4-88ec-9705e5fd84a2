"""
Fingerprint Generator for creating realistic browser fingerprints
"""

import random
import json
from typing import Dict, Any, Optional, List
from loguru import logger

try:
    from browserforge import BrowserForge
    BROWSERFORGE_AVAILABLE = True
except ImportError:
    BROWSERFORGE_AVAILABLE = False
    logger.warning("BrowserForge not available, using fallback fingerprint generation")


class FingerprintGenerator:
    """Generates realistic browser fingerprints for antidetect purposes"""
    
    def __init__(self):
        self.browserforge = None
        if BROWSERFORGE_AVAILABLE:
            try:
                self.browserforge = BrowserForge()
            except Exception as e:
                logger.warning(f"Failed to initialize BrowserForge: {e}")
        
        # Fallback fingerprint data
        self.fallback_data = self._load_fallback_data()
    
    def generate_fingerprint(
        self,
        os_preference: str = "windows",
        browser_preference: str = "firefox",
        mobile: bool = False
    ) -> Dict[str, Any]:
        """Generate a complete browser fingerprint"""

        try:
            if self.browserforge:
                return self._generate_with_browserforge(
                    os_preference, browser_preference, mobile
                )
            else:
                return self._generate_fallback_fingerprint(
                    os_preference, browser_preference, mobile
                )
        except Exception as e:
            logger.error(f"Error generating fingerprint: {e}")
            return self._generate_fallback_fingerprint(
                os_preference, browser_preference, mobile
            )
    
    def _generate_with_browserforge(
        self,
        os_preference: str,
        browser_preference: str,
        mobile: bool
    ) -> Dict[str, Any]:
        """Generate fingerprint using BrowserForge"""

        try:
            # Generate fingerprint with BrowserForge
            fingerprint = self.browserforge.generate(
                browser=browser_preference,
                os=os_preference,
                mobile=mobile
            )

            # Convert to Camoufox format
            camoufox_config = self._convert_to_camoufox_format(fingerprint)

            logger.debug(f"Generated fingerprint with BrowserForge for {os_preference}/{browser_preference}")
            return camoufox_config

        except Exception as e:
            logger.error(f"BrowserForge generation failed: {e}")
            raise
    
    def _generate_fallback_fingerprint(
        self,
        os_preference: str,
        browser_preference: str,
        mobile: bool
    ) -> Dict[str, Any]:
        """Generate fingerprint using fallback data"""
        
        logger.info(f"Generating fallback fingerprint for {os_preference}/{browser_preference}")
        
        # Get base configuration for OS
        base_config = self.fallback_data.get(os_preference, self.fallback_data["windows"])
        
        # Generate navigator properties
        navigator_config = self._generate_navigator_config(base_config, mobile)
        
        # Generate screen configuration
        screen_config = self._generate_screen_config(mobile)
        
        # Generate window configuration
        window_config = self._generate_window_config(screen_config)
        
        # Generate WebGL configuration
        webgl_config = self._generate_webgl_config(os_preference)
        
        # Generate audio configuration
        audio_config = self._generate_audio_config()
        
        # Generate geolocation (random)
        geolocation_config = self._generate_geolocation_config()
        
        # Combine all configurations
        fingerprint = {
            **navigator_config,
            **screen_config,
            **window_config,
            **webgl_config,
            **audio_config,
            **geolocation_config,
        }
        
        return fingerprint
    
    def _generate_navigator_config(self, base_config: Dict[str, Any], mobile: bool) -> Dict[str, Any]:
        """Generate navigator properties"""
        
        config = {}
        
        # User agent - extract and store Firefox version for consistency
        user_agents = base_config.get("user_agents", [])
        if user_agents:
            selected_ua = random.choice(user_agents)
            config["navigator.userAgent"] = selected_ua

            # Extract Firefox version for consistency
            import re
            firefox_match = re.search(r'Firefox/(\d+)\.0', selected_ua)
            if firefox_match:
                config["firefox_version"] = firefox_match.group(1)
        
        # Platform
        platforms = base_config.get("platforms", ["Win32"])
        config["navigator.platform"] = random.choice(platforms)
        
        # Hardware concurrency
        config["navigator.hardwareConcurrency"] = random.choice([2, 4, 6, 8, 12, 16])

        # Note: navigator.deviceMemory is not supported by Camoufox, so we skip it
        
        # Language - more realistic variety
        primary_languages = ["en-US", "en-GB", "es-ES", "fr-FR", "de-DE", "it-IT", "pt-BR", "ru-RU", "ja-JP", "ko-KR", "zh-CN"]
        primary_lang = random.choice(primary_languages)

        languages = [primary_lang]
        if primary_lang != "en-US":
            languages.append("en-US")
        languages.append("en")

        # Add occasional additional language for realism
        if random.random() < 0.2:
            additional_langs = [lang for lang in primary_languages if lang not in languages]
            if additional_langs:
                languages.insert(-1, random.choice(additional_langs))

        config["navigator.language"] = primary_lang
        config["navigator.languages"] = languages
        
        # Other properties
        config["navigator.cookieEnabled"] = True
        # DoNotTrack: Most users don't enable it, some do, some browsers don't support it
        config["navigator.doNotTrack"] = random.choices(
            ["0", "1", "unspecified"],
            weights=[70, 20, 10]  # 70% don't track, 20% track, 10% unspecified
        )[0]
        config["navigator.maxTouchPoints"] = 0 if not mobile else random.choice([5, 10])

        # Debug: Log the final config to see what's being generated
        logger.info(f"Generated fingerprint config keys: {list(config.keys())}")
        if "navigator.deviceMemory" in config:
            logger.warning("Found navigator.deviceMemory in config - removing it")
            del config["navigator.deviceMemory"]

        return config
    
    def _generate_screen_config(self, mobile: bool) -> Dict[str, Any]:
        """Generate screen properties"""
        
        if mobile:
            # Mobile screen sizes with device pixel ratios
            screens = [
                {"width": 375, "height": 667, "dpr": 2},  # iPhone 6/7/8
                {"width": 414, "height": 736, "dpr": 3},  # iPhone 6/7/8 Plus
                {"width": 375, "height": 812, "dpr": 3},  # iPhone X/XS
                {"width": 360, "height": 640, "dpr": 3},  # Android
                {"width": 412, "height": 732, "dpr": 2.625},  # Android
                {"width": 390, "height": 844, "dpr": 3},  # iPhone 12
                {"width": 428, "height": 926, "dpr": 3},  # iPhone 12 Pro Max
            ]
        else:
            # Desktop screen sizes
            screens = [
                {"width": 1920, "height": 1080, "dpr": 1},
                {"width": 1366, "height": 768, "dpr": 1},
                {"width": 1440, "height": 900, "dpr": 1},
                {"width": 1536, "height": 864, "dpr": 1},
                {"width": 1600, "height": 900, "dpr": 1},
                {"width": 2560, "height": 1440, "dpr": 2},  # Retina display
            ]

        screen = random.choice(screens)

        return {
            "screen.width": screen["width"],
            "screen.height": screen["height"],
            "screen.availWidth": screen["width"],
            "screen.availHeight": screen["height"] - random.choice([0, 30, 40]),
            "screen.colorDepth": 24,
            "screen.pixelDepth": 24,
            "window.devicePixelRatio": screen["dpr"],
            "is_mobile": mobile,
        }
    
    def _generate_window_config(self, screen_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate window properties"""
        
        screen_width = screen_config["screen.width"]
        screen_height = screen_config["screen.height"]
        
        # Window size should be smaller than screen
        window_width = screen_width - random.randint(0, 100)
        window_height = screen_height - random.randint(50, 150)
        
        # Inner size (viewport)
        inner_width = window_width - random.randint(0, 20)
        inner_height = window_height - random.randint(80, 120)
        
        return {
            "window.outerWidth": window_width,
            "window.outerHeight": window_height,
            "window.innerWidth": inner_width,
            "window.innerHeight": inner_height,
            "window.screenX": random.randint(0, 100),
            "window.screenY": random.randint(0, 100),
            "window.devicePixelRatio": random.choice([1, 1.25, 1.5, 2]),
        }
    
    def _generate_webgl_config(self, os_preference: str) -> Dict[str, Any]:
        """Generate WebGL configuration"""
        
        # WebGL renderers by OS
        renderers = {
            "windows": [
                "ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)",
                "ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)",
            ],
            "macos": [
                "Intel Iris Pro OpenGL Engine",
                "AMD Radeon Pro 560 OpenGL Engine",
                "Apple M1 OpenGL Engine",
            ],
            "linux": [
                "Mesa DRI Intel(R) HD Graphics 620",
                "NVIDIA GeForce GTX 1060/PCIe/SSE2",
                "AMD Radeon RX 580",
            ]
        }
        
        vendors = {
            "windows": "Google Inc. (ANGLE)",
            "macos": "Intel Inc.",
            "linux": "Mesa/X.org"
        }
        
        renderer_list = renderers.get(os_preference, renderers["windows"])
        vendor = vendors.get(os_preference, vendors["windows"])
        
        return {
            "webGl:vendor": vendor,
            "webGl:renderer": random.choice(renderer_list),
        }
    
    def _generate_audio_config(self) -> Dict[str, Any]:
        """Generate audio context configuration"""
        
        return {
            "AudioContext:sampleRate": random.choice([44100, 48000]),
            "AudioContext:maxChannelCount": random.choice([2, 6, 8]),
            "AudioContext:outputLatency": round(random.uniform(0.01, 0.05), 4),
        }
    
    def _generate_geolocation_config(self) -> Dict[str, Any]:
        """Generate realistic geolocation with matching timezone"""

        # Realistic locations with matching timezones
        locations = [
            # US locations
            {"lat": 40.7128, "lng": -74.0060, "tz": "America/New_York"},  # New York
            {"lat": 34.0522, "lng": -118.2437, "tz": "America/Los_Angeles"},  # Los Angeles
            {"lat": 41.8781, "lng": -87.6298, "tz": "America/Chicago"},  # Chicago
            {"lat": 29.7604, "lng": -95.3698, "tz": "America/Chicago"},  # Houston
            {"lat": 39.9526, "lng": -75.1652, "tz": "America/New_York"},  # Philadelphia
            # EU locations
            {"lat": 51.5074, "lng": -0.1278, "tz": "Europe/London"},  # London
            {"lat": 48.8566, "lng": 2.3522, "tz": "Europe/Paris"},  # Paris
            {"lat": 52.5200, "lng": 13.4050, "tz": "Europe/Berlin"},  # Berlin
            {"lat": 41.9028, "lng": 12.4964, "tz": "Europe/Rome"},  # Rome
            {"lat": 40.4168, "lng": -3.7038, "tz": "Europe/Madrid"},  # Madrid
            # Asia locations
            {"lat": 35.6762, "lng": 139.6503, "tz": "Asia/Tokyo"},  # Tokyo
            {"lat": 37.5665, "lng": 126.9780, "tz": "Asia/Seoul"},  # Seoul
            {"lat": 1.3521, "lng": 103.8198, "tz": "Asia/Singapore"},  # Singapore
        ]

        location = random.choice(locations)

        # Add small random offset to avoid exact coordinates
        latitude = location["lat"] + random.uniform(-0.05, 0.05)
        longitude = location["lng"] + random.uniform(-0.05, 0.05)

        return {
            "geolocation:latitude": round(latitude, 6),
            "geolocation:longitude": round(longitude, 6),
            "geolocation:accuracy": random.randint(10, 100),
            "timezone": location["tz"]  # Store timezone for browser context
        }
    
    def _convert_to_camoufox_format(self, browserforge_fingerprint: Dict[str, Any]) -> Dict[str, Any]:
        """Convert BrowserForge fingerprint to Camoufox format"""
        
        config = {}
        
        # Map BrowserForge fields to Camoufox format
        if "userAgent" in browserforge_fingerprint:
            config["navigator.userAgent"] = browserforge_fingerprint["userAgent"]
        
        if "platform" in browserforge_fingerprint:
            config["navigator.platform"] = browserforge_fingerprint["platform"]
        
        if "screen" in browserforge_fingerprint:
            screen = browserforge_fingerprint["screen"]
            for key, value in screen.items():
                config[f"screen.{key}"] = value
        
        if "viewport" in browserforge_fingerprint:
            viewport = browserforge_fingerprint["viewport"]
            config["window.innerWidth"] = viewport.get("width")
            config["window.innerHeight"] = viewport.get("height")
        
        # Add other mappings as needed
        
        return config
    
    def _load_fallback_data(self) -> Dict[str, Any]:
        """Load fallback fingerprint data"""
        
        return {
            "windows": {
                "user_agents": [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0",
                    "Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
                    "Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
                ],
                "platforms": ["Win32"],
                "os": "windows"
            },
            "macos": {
                "user_agents": [
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/120.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Gecko/20100101 Firefox/121.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.2; rv:109.0) Gecko/20100101 Firefox/121.0",
                ],
                "platforms": ["MacIntel"],
                "os": "macos"
            },
            "linux": {
                "user_agents": [
                    "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0",
                    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0",
                    "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/120.0",
                    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/120.0",
                ],
                "platforms": ["Linux x86_64"],
                "os": "linux"
            },
            "android": {
                "user_agents": [
                    "Mozilla/5.0 (Mobile; rv:121.0) Gecko/121.0 Firefox/121.0",
                    "Mozilla/5.0 (Android 13; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0",
                    "Mozilla/5.0 (Android 12; Mobile; rv:120.0) Gecko/120.0 Firefox/120.0",
                    "Mozilla/5.0 (Android 11; Mobile; rv:119.0) Gecko/119.0 Firefox/119.0",
                ],
                "platforms": ["Linux armv7l"],
                "os": "android"
            }
        }

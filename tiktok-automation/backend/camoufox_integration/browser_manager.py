"""
Browser Manager for Camoufox integration with antidetect features
"""

import asyncio
import json
import os
import tempfile
import time
from typing import Dict, Any, Optional, List
from pathlib import Path
import psutil
from loguru import logger

# Browser types from Camoufox (compatible with Playwright API)
from typing import Any as Browser, Any as BrowserContext, Any as Page

from core.config import settings
from models.browser_profile import BrowserProfile
from models.proxy import Proxy
from .fingerprint_generator import FingerprintGenerator
from .proxy_manager import ProxyManager
from .antidetect_config import AntidetectConfig
from .camoufox_wrapper import local_camoufox
from core.fingerprint_cache import fingerprint_cache


class BrowserManager:
    """Manages Camoufox browser instances with antidetect capabilities"""

    def __init__(self):
        self.active_browsers: Dict[str, Browser] = {}
        self.active_contexts: Dict[str, BrowserContext] = {}
        self.login_contexts: Dict[int, BrowserContext] = {}  # profile_id -> login context
        self.fingerprint_generator = FingerprintGenerator()
        self.proxy_manager = ProxyManager()
        self.antidetect_config = AntidetectConfig()

        # Performance tracking
        self.browser_count = 0
        self.max_browsers = settings.MAX_CONCURRENT_BROWSERS

        # Initialization flag
        self._initialized = False

    async def initialize(self) -> bool:
        """Initialize browser manager and ensure Camoufox is ready"""

        if self._initialized:
            return True

        try:
            # Initialize local Camoufox
            success = await local_camoufox.initialize()
            if success:
                self._initialized = True
                logger.info("BrowserManager initialized successfully")
            else:
                logger.error("Failed to initialize Camoufox")

            return success

        except Exception as e:
            logger.error(f"BrowserManager initialization failed: {e}")
            return False
        
    async def create_browser_instance(
        self,
        profile: BrowserProfile,
        proxy: Optional[Proxy] = None,
        headless: bool = None,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Browser:
        """Create a new Camoufox browser instance with antidetect configuration"""

        # Ensure initialized
        if not self._initialized:
            if not await self.initialize():
                raise Exception("Failed to initialize BrowserManager")

        # Check browser limits
        if self.browser_count >= self.max_browsers:
            raise Exception(f"Maximum browser limit reached ({self.max_browsers})")

        try:
            # Setup proxy configuration first to get IP for antidetect
            proxy_config = None
            proxy_ip = None
            if proxy:
                proxy_config = await self.proxy_manager.get_proxy_config(proxy)
                proxy_ip = proxy.host  # Use proxy host IP for antidetect matching

            # Generate fingerprint configuration with proxy IP
            fingerprint_config = await self._generate_fingerprint_config(profile, custom_config, proxy_ip)

            # Create user data directory
            user_data_dir = await self._create_user_data_dir(profile.id)

            logger.info(f"Creating browser instance for profile {profile.name}")

            # Create browser using local Camoufox
            browser = await local_camoufox.create_browser(
                config=fingerprint_config,
                proxy=proxy_config,
                headless=headless if headless is not None else settings.CAMOUFOX_HEADLESS,
                user_data_dir=user_data_dir,
                timeout=settings.CAMOUFOX_TIMEOUT
            )
            
            # Store browser reference
            browser_id = f"profile_{profile.id}_{id(browser)}"
            self.active_browsers[browser_id] = browser
            self.browser_count += 1
            
            # Update profile usage
            profile.update_usage()
            
            logger.info(f"Browser instance created successfully: {browser_id}")
            return browser
            
        except Exception as e:
            logger.error(f"Failed to create browser instance: {e}")
            raise



    async def create_browser_context(
        self,
        browser,  # Browser object
        profile: BrowserProfile,
        proxy: Optional[Proxy] = None
    ) -> BrowserContext:
        """Create a new browser context with antidetect settings"""

        try:
            # Check if this is a fallback Firefox browser
            if hasattr(browser, '_fingerprint_config'):
                # This is fallback Firefox - create context with fingerprint injection
                fingerprint_config = browser._fingerprint_config

                # Determine if mobile view should be used
                is_mobile = fingerprint_config.get('is_mobile', False)

                # Set viewport based on mobile/desktop
                if is_mobile:
                    viewport = {
                        'width': fingerprint_config.get('screen.width', 375),
                        'height': fingerprint_config.get('screen.height', 667)
                    }
                    # Use mobile user agent if available
                    user_agent = fingerprint_config.get('navigator.userAgent', 'Mozilla/5.0 (Mobile; rv:121.0) Gecko/121.0 Firefox/121.0')
                else:
                    viewport = {
                        'width': fingerprint_config.get('window.innerWidth', 1366),
                        'height': fingerprint_config.get('window.innerHeight', 768)
                    }
                    user_agent = fingerprint_config.get('navigator.userAgent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/109.0')

                context = await browser.new_context(
                    user_agent=user_agent,
                    viewport=viewport,
                    locale=fingerprint_config.get('navigator.language', 'en-US'),
                    timezone_id=fingerprint_config.get('timezone', 'America/New_York'),
                    geolocation={
                        'latitude': fingerprint_config.get('geolocation:latitude', 40.7128),
                        'longitude': fingerprint_config.get('geolocation:longitude', -74.0060)
                    } if fingerprint_config.get('geolocation:latitude') else None,
                    permissions=['geolocation'] if fingerprint_config.get('geolocation:latitude') else [],
                    device_scale_factor=fingerprint_config.get('window.devicePixelRatio', 1),
                    is_mobile=is_mobile,
                    has_touch=is_mobile
                )

                # Inject fingerprint script
                await context.add_init_script(f"""
                    // Override navigator properties
                    Object.defineProperty(navigator, 'platform', {{
                        get: () => '{fingerprint_config.get('navigator.platform', 'MacIntel')}'
                    }});
                    Object.defineProperty(navigator, 'hardwareConcurrency', {{
                        get: () => {fingerprint_config.get('navigator.hardwareConcurrency', 8)}
                    }});
                    Object.defineProperty(navigator, 'language', {{
                        get: () => '{fingerprint_config.get('navigator.language', 'en-US')}'
                    }});
                    Object.defineProperty(navigator, 'languages', {{
                        get: () => {fingerprint_config.get('navigator.languages', ['en-US', 'en'])}
                    }});
                    Object.defineProperty(navigator, 'cookieEnabled', {{
                        get: () => {str(fingerprint_config.get('navigator.cookieEnabled', True)).lower()}
                    }});
                    Object.defineProperty(navigator, 'doNotTrack', {{
                        get: () => '{fingerprint_config.get('navigator.doNotTrack', '0')}'
                    }});
                    Object.defineProperty(navigator, 'maxTouchPoints', {{
                        get: () => {fingerprint_config.get('navigator.maxTouchPoints', 0)}
                    }});

                    // Override screen properties
                    Object.defineProperty(screen, 'width', {{
                        get: () => {fingerprint_config.get('screen.width', 1920)}
                    }});
                    Object.defineProperty(screen, 'height', {{
                        get: () => {fingerprint_config.get('screen.height', 1080)}
                    }});
                    Object.defineProperty(screen, 'availWidth', {{
                        get: () => {fingerprint_config.get('screen.availWidth', 1920)}
                    }});
                    Object.defineProperty(screen, 'availHeight', {{
                        get: () => {fingerprint_config.get('screen.availHeight', 1055)}
                    }});
                    Object.defineProperty(screen, 'colorDepth', {{
                        get: () => {fingerprint_config.get('screen.colorDepth', 24)}
                    }});
                    Object.defineProperty(screen, 'pixelDepth', {{
                        get: () => {fingerprint_config.get('screen.pixelDepth', 24)}
                    }});

                    // Override window properties
                    Object.defineProperty(window, 'devicePixelRatio', {{
                        get: () => {fingerprint_config.get('window.devicePixelRatio', 1)}
                    }});
                """)

                logger.info("Created context for fallback Firefox with fingerprint injection")
            else:
                # Generate context configuration for regular Camoufox
                context_config = await self._get_context_config(profile, proxy)

                # Create context from Browser
                context = await browser.new_context(**context_config)
                logger.info("Created context for Camoufox browser")

            # Apply additional antidetect measures
            await self._apply_antidetect_measures(context, profile)

            # Store context reference
            context_id = f"profile_{profile.id}_{id(context)}"
            self.active_contexts[context_id] = context

            logger.info(f"Browser context created: {context_id}")
            return context

        except Exception as e:
            logger.error(f"Failed to create browser context: {e}")
            raise
    
    async def close_browser(self, browser: Browser):
        """Close browser instance and cleanup"""
        try:
            # Find and remove browser from active list
            browser_id = None
            for bid, b in self.active_browsers.items():
                if b == browser:
                    browser_id = bid
                    break
            
            if browser_id:
                del self.active_browsers[browser_id]
                self.browser_count -= 1
            
            # Close browser
            await browser.close()
            
            logger.info(f"Browser closed: {browser_id}")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
    
    async def close_context(self, context: BrowserContext):
        """Close browser context"""
        try:
            # Find and remove context from active list
            context_id = None
            for cid, c in self.active_contexts.items():
                if c == context:
                    context_id = cid
                    break
            
            if context_id:
                del self.active_contexts[context_id]
            
            # Close context
            await context.close()
            
            logger.info(f"Browser context closed: {context_id}")
            
        except Exception as e:
            logger.error(f"Error closing context: {e}")

    async def close_browser_for_profile(self, profile_id: int):
        """Close browser and context for a specific profile"""
        try:
            # Close login context if exists
            if profile_id in self.login_contexts:
                try:
                    login_context = self.login_contexts[profile_id]
                    await login_context.close()
                    logger.info(f"Closed login context for profile {profile_id}")
                except Exception as e:
                    logger.warning(f"Error closing login context for profile {profile_id}: {e}")
                finally:
                    del self.login_contexts[profile_id]

            # Find and close contexts for this profile
            contexts_to_close = []
            for context_id, context in self.active_contexts.items():
                # Check if this context belongs to the profile
                # Context ID format: "profile_{profile_id}_{timestamp}"
                if context_id.startswith(f"profile_{profile_id}_"):
                    contexts_to_close.append((context_id, context))

            for context_id, context in contexts_to_close:
                try:
                    await self.close_context(context)
                    logger.info(f"Closed context {context_id} for profile {profile_id}")
                except Exception as e:
                    logger.error(f"Error closing context {context_id} for profile {profile_id}: {e}")

            # Find and close browsers for this profile if no other contexts are using them
            browsers_to_close = []
            for browser_id, browser in self.active_browsers.items():
                # Check if this browser belongs to the profile and has no active contexts
                if browser_id.startswith(f"profile_{profile_id}_"):
                    # Check if there are any active contexts for this browser
                    has_active_contexts = any(
                        cid.startswith(f"profile_{profile_id}_")
                        for cid in self.active_contexts.keys()
                    )
                    if not has_active_contexts:
                        browsers_to_close.append((browser_id, browser))

            for browser_id, browser in browsers_to_close:
                try:
                    await self.close_browser(browser)
                    logger.info(f"Closed browser {browser_id} for profile {profile_id}")
                except Exception as e:
                    logger.error(f"Error closing browser {browser_id} for profile {profile_id}: {e}")

            logger.info(f"Closed all browser instances for profile {profile_id}")

        except Exception as e:
            logger.error(f"Error closing browser for profile {profile_id}: {e}")
            raise

    async def get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "total_browsers": self.browser_count,
                "max_browsers": self.max_browsers,
                "memory_rss_mb": memory_info.rss / 1024 / 1024,
                "memory_vms_mb": memory_info.vms / 1024 / 1024,
                "memory_percent": process.memory_percent(),
                "cpu_percent": process.cpu_percent()
            }
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {}
    
    async def cleanup_all(self):
        """Cleanup all active browsers and contexts"""
        logger.info("Cleaning up all browser instances...")
        
        # Close all contexts
        for context in list(self.active_contexts.values()):
            try:
                await context.close()
            except Exception as e:
                logger.error(f"Error closing context: {e}")
        
        # Close all browsers
        for browser in list(self.active_browsers.values()):
            try:
                await browser.close()
            except Exception as e:
                logger.error(f"Error closing browser: {e}")
        
        self.active_browsers.clear()
        self.active_contexts.clear()
        self.browser_count = 0
        
        logger.info("Browser cleanup completed")
    
    async def _generate_fingerprint_config(
        self,
        profile: BrowserProfile,
        custom_config: Optional[Dict[str, Any]] = None,
        proxy_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate fingerprint configuration for the profile using cache for performance"""

        # Start with profile's Camoufox config
        config = profile.get_camoufox_config()

        # Generate additional fingerprint data if needed
        if not config or len(config) < 5:  # Minimal config
            # Determine template based on profile
            template_name = self._determine_fingerprint_template(profile)

            # Get cached fingerprint for performance
            try:
                cached_config = await fingerprint_cache.get_fingerprint(template_name)
                config.update(cached_config)
                logger.debug(f"Using cached fingerprint template: {template_name}")
            except Exception as e:
                logger.warning(f"Failed to get cached fingerprint, falling back to generator: {e}")
                # Fallback to original method
                generated_config = self.fingerprint_generator.generate_fingerprint(
                    os_preference=self._extract_os_from_user_agent(profile.user_agent),
                    browser_preference="firefox",
                    mobile=True  # Enable mobile view for TikTok
                )
                config.update(generated_config)

        # Apply custom config overrides
        if custom_config:
            config.update(custom_config)
        
        # Debug: Log config before antidetect
        logger.info(f"Config before antidetect: navigator.doNotTrack = {config.get('navigator.doNotTrack')}")

        # Add antidetect enhancements with proxy IP for location matching
        antidetect_config = self.antidetect_config.get_enhanced_config(config, proxy_ip)
        logger.info(f"Antidetect config keys: {list(antidetect_config.keys())}")
        if "navigator.doNotTrack" in antidetect_config:
            logger.warning(f"Antidetect config has navigator.doNotTrack = {antidetect_config['navigator.doNotTrack']}")

        config.update(antidetect_config)

        # Debug: Log config after antidetect
        logger.info(f"Config after antidetect: navigator.doNotTrack = {config.get('navigator.doNotTrack')}")

        return config

    def _determine_fingerprint_template(self, profile: BrowserProfile) -> str:
        """Determine the best fingerprint template for a profile"""

        user_agent = profile.user_agent.lower() if profile.user_agent else ""

        # Determine OS
        if "mac" in user_agent or "darwin" in user_agent:
            return "mac_desktop"
        elif "android" in user_agent:
            return "mobile_android"
        elif "windows" in user_agent:
            # Determine screen size preference
            if profile.screen_width and profile.screen_width < 1500:
                return "windows_laptop"
            else:
                return "windows_desktop"
        else:
            # Default to Windows desktop
            return "windows_desktop"
    

    
    async def _get_context_config(
        self,
        profile: BrowserProfile,
        proxy: Optional[Proxy]
    ) -> Dict[str, Any]:
        """Get browser context configuration"""
        
        config = {
            "viewport": None,  # Let Camoufox handle viewport
            "ignore_https_errors": True,
            "java_script_enabled": True,
            "accept_downloads": True,
        }
        
        # Add user agent if specified
        if profile.user_agent:
            config["user_agent"] = profile.user_agent
        
        # Add geolocation if configured
        if profile.geolocation_config:
            geo_config = profile.geolocation_config
            if "latitude" in geo_config and "longitude" in geo_config:
                config["geolocation"] = {
                    "latitude": geo_config["latitude"],
                    "longitude": geo_config["longitude"]
                }
                config["permissions"] = ["geolocation"]
        
        # Add locale if configured
        if profile.locale:
            config["locale"] = profile.locale
        
        # Add timezone if configured
        if profile.timezone:
            config["timezone_id"] = profile.timezone
        
        return config
    
    async def _apply_antidetect_measures(
        self,
        context: BrowserContext,
        profile: BrowserProfile
    ):
        """Apply additional antidetect measures to the context"""
        
        try:
            # Add stealth scripts
            await context.add_init_script("""
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // Override permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // Override plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // Override languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            # Set additional headers
            await context.set_extra_http_headers({
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            })
            
        except Exception as e:
            logger.warning(f"Failed to apply some antidetect measures: {e}")
    
    async def _create_user_data_dir(self, profile_id: int) -> str:
        """Create user data directory for the profile"""
        
        user_data_dir = settings.PROFILES_DIR / f"profile_{profile_id}"
        user_data_dir.mkdir(parents=True, exist_ok=True)
        
        return str(user_data_dir)
    
    def _extract_os_from_user_agent(self, user_agent: Optional[str]) -> str:
        """Extract OS preference from user agent"""
        
        if not user_agent:
            return "windows"
        
        user_agent_lower = user_agent.lower()
        
        if "windows" in user_agent_lower:
            return "windows"
        elif "mac" in user_agent_lower or "darwin" in user_agent_lower:
            return "macos"
        elif "linux" in user_agent_lower:
            return "linux"
        else:
            return "windows"  # Default

    def get_contexts_for_profile(self, profile_id: int) -> List:
        """Get all browser contexts for a profile"""
        contexts = []

        # Check login context first (priority for login sessions)
        if profile_id in self.login_contexts:
            contexts.append(self.login_contexts[profile_id])

        # Check active contexts
        for context_id, context in self.active_contexts.items():
            if f"profile_{profile_id}_" in context_id:
                contexts.append(context)
        return contexts

    def save_login_context(self, profile_id: int, context):
        """Save browser context specifically for login session"""
        self.login_contexts[profile_id] = context
        logger.info(f"Saved login context for profile {profile_id}")

        # Also save context reference with timestamp for recovery
        import time
        context._profile_id = profile_id
        context._saved_time = time.time()

    def get_login_context(self, profile_id: int):
        """Get login context for a profile with recovery mechanism"""
        # First try direct lookup
        context = self.login_contexts.get(profile_id)
        if context:
            try:
                # Test if context is still valid
                pages = context.pages
                return context
            except Exception as e:
                logger.warning(f"Login context for profile {profile_id} is invalid: {e}")
                # Remove invalid context
                del self.login_contexts[profile_id]

        # Recovery: Search through all active contexts
        for context_id, context in self.active_contexts.items():
            if f"profile_{profile_id}_" in context_id:
                try:
                    # Test if context is valid
                    pages = context.pages
                    # Save as login context for future use
                    self.login_contexts[profile_id] = context
                    logger.info(f"Recovered login context for profile {profile_id} from active contexts")
                    return context
                except Exception:
                    continue

        return None

    def remove_login_context(self, profile_id: int):
        """Remove login context after completing login"""
        if profile_id in self.login_contexts:
            del self.login_contexts[profile_id]
            logger.info(f"Removed login context for profile {profile_id}")

    def get_cookies_path(self, profile_id: int) -> Path:
        """Get cookies file path for a profile"""
        from core.config import settings
        cookies_dir = settings.COOKIES_DIR
        return cookies_dir / f"profile_{profile_id}_cookies.json"

    async def load_cookies_for_context(self, context, profile_id: int):
        """Load saved cookies into browser context with validation"""
        try:
            cookies_path = self.get_cookies_path(profile_id)
            logger.info(f"Looking for cookies at: {cookies_path}")

            if cookies_path.exists():
                try:
                    # Load and validate storage state
                    import json
                    with open(cookies_path, 'r') as f:
                        storage_state = json.load(f)

                    # Validate storage state structure
                    if not isinstance(storage_state, dict):
                        logger.error(f"Invalid storage state format for profile {profile_id}")
                        return False

                    cookies_loaded = 0

                    # Load cookies with validation
                    if 'cookies' in storage_state and isinstance(storage_state['cookies'], list):
                        valid_cookies = []
                        for cookie in storage_state['cookies']:
                            # Validate required cookie fields
                            if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                                # Ensure domain is set for TikTok
                                if 'domain' not in cookie:
                                    cookie['domain'] = '.tiktok.com'
                                valid_cookies.append(cookie)

                        if valid_cookies:
                            await context.add_cookies(valid_cookies)
                            cookies_loaded = len(valid_cookies)
                            logger.info(f"Loaded {cookies_loaded} valid cookies for profile {profile_id}")

                    # Load localStorage data with validation
                    if 'origins' in storage_state and isinstance(storage_state['origins'], list):
                        for origin in storage_state['origins']:
                            if isinstance(origin, dict) and 'localStorage' in origin:
                                if isinstance(origin['localStorage'], list):
                                    for item in origin['localStorage']:
                                        if isinstance(item, dict) and 'name' in item and 'value' in item:
                                            # Escape values to prevent injection
                                            name = item['name'].replace("'", "\\'")
                                            value = item['value'].replace("'", "\\'")
                                            await context.add_init_script(f"""
                                                try {{
                                                    localStorage.setItem('{name}', '{value}');
                                                }} catch(e) {{
                                                    console.log('Failed to set localStorage item:', e);
                                                }}
                                            """)

                    if cookies_loaded > 0:
                        logger.info(f"Successfully loaded storage state for profile {profile_id}")
                        return True
                    else:
                        logger.warning(f"No valid cookies found for profile {profile_id}")
                        return False

                except json.JSONDecodeError as json_error:
                    logger.error(f"Invalid JSON in cookies file for profile {profile_id}: {json_error}")
                    # Try to backup and recreate
                    backup_path = cookies_path.with_suffix('.json.backup')
                    cookies_path.rename(backup_path)
                    logger.info(f"Corrupted cookies file backed up to {backup_path}")
                    return False
                except Exception as load_error:
                    logger.error(f"Failed to load storage state for profile {profile_id}: {load_error}")
                    return False
            else:
                logger.info(f"No cookies file found for profile {profile_id} at {cookies_path}")
                return False
        except Exception as e:
            logger.error(f"Failed to load cookies for profile {profile_id}: {e}")
            return False

#!/usr/bin/env python3
"""
HTTP to SOCKS5 proxy tunnel for Camoufox compatibility
"""

import asyncio
import socket
import struct
import logging
from typing import Optional, Tuple
import threading
import time

logger = logging.getLogger(__name__)

class SOCKS5Tunnel:
    """HTTP to SOCKS5 proxy tunnel"""
    
    def __init__(self, socks_host: str, socks_port: int, socks_user: str, socks_pass: str):
        self.socks_host = socks_host
        self.socks_port = socks_port
        self.socks_user = socks_user
        self.socks_pass = socks_pass
        self.server_socket = None
        self.running = False
        
    def start_tunnel(self, local_port: int = 0) -> int:
        """Start HTTP proxy tunnel, returns the local port"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind(('127.0.0.1', local_port))
        self.server_socket.listen(5)
        
        actual_port = self.server_socket.getsockname()[1]
        logger.info(f"HTTP proxy tunnel started on port {actual_port}")
        
        self.running = True
        thread = threading.Thread(target=self._run_server, daemon=True)
        thread.start()
        
        return actual_port
    
    def stop_tunnel(self):
        """Stop the tunnel"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
    
    def _run_server(self):
        """Run the proxy server"""
        while self.running:
            try:
                client_socket, addr = self.server_socket.accept()
                thread = threading.Thread(
                    target=self._handle_client, 
                    args=(client_socket,), 
                    daemon=True
                )
                thread.start()
            except Exception as e:
                if self.running:
                    logger.error(f"Server error: {e}")
                break
    
    def _handle_client(self, client_socket: socket.socket):
        """Handle client connection"""
        try:
            # Read HTTP request
            request = client_socket.recv(4096).decode('utf-8')
            if not request:
                return
                
            lines = request.split('\n')
            first_line = lines[0]
            
            if first_line.startswith('CONNECT'):
                # HTTPS CONNECT request
                parts = first_line.split()
                if len(parts) >= 2:
                    host_port = parts[1]
                    if ':' in host_port:
                        host, port = host_port.split(':', 1)
                        port = int(port)
                    else:
                        host = host_port
                        port = 443
                    
                    # Connect through SOCKS5
                    socks_socket = self._connect_socks5(host, port)
                    if socks_socket:
                        # Send 200 Connection established
                        client_socket.send(b'HTTP/1.1 200 Connection established\r\n\r\n')
                        
                        # Start tunneling
                        self._tunnel_data(client_socket, socks_socket)
                    else:
                        client_socket.send(b'HTTP/1.1 502 Bad Gateway\r\n\r\n')
            else:
                # Regular HTTP request
                # Parse URL
                parts = first_line.split()
                if len(parts) >= 2:
                    url = parts[1]
                    if url.startswith('http://'):
                        url = url[7:]
                    
                    if '/' in url:
                        host_port, path = url.split('/', 1)
                        path = '/' + path
                    else:
                        host_port = url
                        path = '/'
                    
                    if ':' in host_port:
                        host, port = host_port.split(':', 1)
                        port = int(port)
                    else:
                        host = host_port
                        port = 80
                    
                    # Connect through SOCKS5
                    socks_socket = self._connect_socks5(host, port)
                    if socks_socket:
                        # Modify request to remove proxy format
                        modified_request = request.replace(f'http://{host_port}', '')
                        socks_socket.send(modified_request.encode('utf-8'))
                        
                        # Start tunneling
                        self._tunnel_data(client_socket, socks_socket)
                    else:
                        client_socket.send(b'HTTP/1.1 502 Bad Gateway\r\n\r\n')
                        
        except Exception as e:
            logger.error(f"Client handling error: {e}")
        finally:
            client_socket.close()
    
    def _connect_socks5(self, target_host: str, target_port: int) -> Optional[socket.socket]:
        """Connect to target through SOCKS5"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((self.socks_host, self.socks_port))
            
            # SOCKS5 authentication
            # Send auth methods
            sock.send(b'\x05\x02\x00\x02')  # SOCKS5, 2 methods, no auth, user/pass
            
            response = sock.recv(2)
            if len(response) != 2 or response[0] != 5:
                sock.close()
                return None
            
            auth_method = response[1]
            
            if auth_method == 2:  # Username/password auth
                # Send credentials
                user_bytes = self.socks_user.encode('utf-8')
                pass_bytes = self.socks_pass.encode('utf-8')
                auth_request = bytes([1, len(user_bytes)]) + user_bytes + bytes([len(pass_bytes)]) + pass_bytes
                sock.send(auth_request)
                
                auth_response = sock.recv(2)
                if len(auth_response) != 2 or auth_response[1] != 0:
                    sock.close()
                    return None
            elif auth_method != 0:  # Not no-auth or user/pass
                sock.close()
                return None
            
            # Send connect request
            host_bytes = target_host.encode('utf-8')
            connect_request = (
                b'\x05\x01\x00\x03' +  # SOCKS5, CONNECT, reserved, domain name
                bytes([len(host_bytes)]) + host_bytes +
                struct.pack('>H', target_port)
            )
            sock.send(connect_request)
            
            # Read response
            response = sock.recv(10)
            if len(response) < 10 or response[0] != 5 or response[1] != 0:
                sock.close()
                return None
            
            return sock
            
        except Exception as e:
            logger.error(f"SOCKS5 connection error: {e}")
            return None
    
    def _tunnel_data(self, client_socket: socket.socket, server_socket: socket.socket):
        """Tunnel data between client and server"""
        def forward(source: socket.socket, destination: socket.socket):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
            except:
                pass
            finally:
                source.close()
                destination.close()
        
        # Start forwarding in both directions
        thread1 = threading.Thread(target=forward, args=(client_socket, server_socket), daemon=True)
        thread2 = threading.Thread(target=forward, args=(server_socket, client_socket), daemon=True)
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()


# Global tunnel instance
_tunnel_instance: Optional[SOCKS5Tunnel] = None
_tunnel_port: Optional[int] = None

def start_proxy_tunnel(socks_host: str, socks_port: int, socks_user: str, socks_pass: str) -> int:
    """Start proxy tunnel and return local HTTP proxy port"""
    global _tunnel_instance, _tunnel_port
    
    if _tunnel_instance and _tunnel_instance.running:
        return _tunnel_port
    
    _tunnel_instance = SOCKS5Tunnel(socks_host, socks_port, socks_user, socks_pass)
    _tunnel_port = _tunnel_instance.start_tunnel()
    
    # Give it a moment to start
    time.sleep(0.1)
    
    return _tunnel_port

def stop_proxy_tunnel():
    """Stop proxy tunnel"""
    global _tunnel_instance
    
    if _tunnel_instance:
        _tunnel_instance.stop_tunnel()
        _tunnel_instance = None

"""
Antidetect Configuration for enhanced stealth capabilities
"""

import random
import json
import pytz
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional
from loguru import logger


class AntidetectConfig:
    """Advanced antidetect configuration generator"""

    def __init__(self):
        self.stealth_patterns = self._load_stealth_patterns()
        self.human_behavior_config = self._load_human_behavior_config()
        self.proxy_ip = None
        self.proxy_location = None
    
    def get_enhanced_config(self, base_config: Dict[str, Any], proxy_ip: str = None) -> Dict[str, Any]:
        """Enhance base configuration with antidetect features"""

        enhanced_config = {}

        # Store proxy info for timezone/location matching
        if proxy_ip:
            self.proxy_ip = proxy_ip
            self.proxy_location = self._get_ip_location(proxy_ip)

        # Add human-like cursor movement
        enhanced_config.update(self._get_cursor_config())

        # Add timing randomization (commented out - contains unsupported properties)
        # enhanced_config.update(self._get_timing_config())

        # Add behavioral patterns (commented out - contains unsupported properties)
        # enhanced_config.update(self._get_behavioral_config())

        # Add stealth enhancements (commented out - contains unsupported properties)
        # enhanced_config.update(self._get_stealth_config(base_config))

        # Add font randomization (commented out - may contain unsupported properties)
        # enhanced_config.update(self._get_font_config())

        # Add WebRTC configuration
        enhanced_config.update(self._get_webrtc_config())

        # Add timezone matching
        enhanced_config.update(self._get_timezone_config())

        # Add language matching
        enhanced_config.update(self._get_language_config())

        # Add DNS leak protection (now uses Firefox prefs)
        enhanced_config.update(self._get_dns_config())

        # Add browser version consistency
        enhanced_config.update(self._get_browser_version_config(base_config))

        logger.debug("Enhanced antidetect configuration generated")
        return enhanced_config
    
    def _get_cursor_config(self) -> Dict[str, Any]:
        """Configure human-like cursor movement"""
        
        return {
            "humanize": True,
            "humanize:maxTime": random.uniform(1.0, 2.5),
            "showcursor": False,  # Don't show cursor highlighter in production
        }
    
    def _get_timing_config(self) -> Dict[str, Any]:
        """Configure timing randomization"""
        
        # Add slight randomization to timing-sensitive properties
        config = {}
        
        # Randomize performance timing
        if random.choice([True, False]):
            config["performance.timing.randomize"] = True
        
        return config
    
    def _get_behavioral_config(self) -> Dict[str, Any]:
        """Configure human behavioral patterns"""
        
        config = {}
        
        # Mouse movement patterns
        config.update({
            "mouse.movement.natural": True,
            "mouse.movement.variance": random.uniform(0.1, 0.3),
            "mouse.click.delay": random.randint(50, 200),  # ms
        })
        
        # Keyboard patterns
        config.update({
            "keyboard.typing.natural": True,
            "keyboard.typing.speed": random.randint(80, 120),  # WPM
            "keyboard.typing.variance": random.uniform(0.1, 0.25),
        })
        
        # Scroll patterns
        config.update({
            "scroll.behavior.smooth": True,
            "scroll.speed.variance": random.uniform(0.8, 1.2),
        })
        
        return config
    
    def _get_stealth_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure stealth enhancements"""
        
        config = {}
        
        # WebDriver detection evasion
        config.update({
            "webdriver.detection.evasion": True,
            "automation.detection.evasion": True,
        })
        
        # Canvas fingerprinting protection
        config.update({
            "canvas.fingerprinting.protection": True,
            "canvas.noise.enabled": True,
            "canvas.noise.amount": random.uniform(0.001, 0.01),
        })
        
        # Audio fingerprinting protection
        config.update({
            "audio.fingerprinting.protection": True,
            "audio.context.noise": True,
        })
        
        # Font fingerprinting protection
        config.update({
            "font.fingerprinting.protection": True,
            "font.metrics.randomization": True,
        })
        
        # WebGL fingerprinting protection
        if not any(key.startswith("webGl:") for key in base_config.keys()):
            config.update({
                "webgl.fingerprinting.protection": True,
                "webgl.noise.enabled": True,
            })
        
        return config
    
    def _get_font_config(self) -> Dict[str, Any]:
        """Configure font randomization"""
        
        # Select random fonts from common system fonts
        common_fonts = [
            "Arial", "Helvetica", "Times New Roman", "Courier New",
            "Verdana", "Georgia", "Palatino", "Garamond",
            "Bookman", "Comic Sans MS", "Trebuchet MS", "Arial Black",
            "Impact", "Lucida Sans Unicode", "Tahoma", "Lucida Console"
        ]
        
        # Randomize font list
        selected_fonts = random.sample(common_fonts, random.randint(8, 12))
        
        return {
            "fonts": selected_fonts,
            # "font.randomization.enabled": True,  # Commented out - not supported by Camoufox
        }
    
    def _get_webrtc_config(self) -> Dict[str, Any]:
        """Configure WebRTC for IP protection"""

        # Generate random local IP that matches proxy location if available
        if self.proxy_location and self.proxy_location.get('country_code'):
            # Use IP range that matches proxy country
            local_ip = self._generate_local_ip_for_country(self.proxy_location['country_code'])
        else:
            # Generate random local IP
            local_ip = f"192.168.{random.randint(1, 254)}.{random.randint(1, 254)}"

        return {
            "webrtc:ipv4": local_ip,
            # "webrtc.leak.protection": True,  # Commented out - may not be supported
        }
    
    def get_human_delay(self, min_delay: float = 1.0, max_delay: float = 3.0) -> float:
        """Generate human-like delay with natural distribution"""
        
        # Use normal distribution for more natural timing
        mean = (min_delay + max_delay) / 2
        std_dev = (max_delay - min_delay) / 6  # 99.7% within range
        
        delay = random.normalvariate(mean, std_dev)
        
        # Ensure within bounds
        delay = max(min_delay, min(max_delay, delay))
        
        return delay
    
    def get_typing_pattern(self, text: str) -> List[Dict[str, Any]]:
        """Generate human-like typing pattern for text input"""
        
        pattern = []
        base_delay = random.uniform(80, 120)  # Base typing speed (ms per char)
        
        for i, char in enumerate(text):
            # Calculate delay for this character
            char_delay = base_delay
            
            # Add variance based on character type
            if char.isupper():
                char_delay *= random.uniform(1.2, 1.5)  # Slower for capitals
            elif char in ".,!?;:":
                char_delay *= random.uniform(1.1, 1.3)  # Slower for punctuation
            elif char == " ":
                char_delay *= random.uniform(1.5, 2.0)  # Slower for spaces
            
            # Add random variance
            char_delay *= random.uniform(0.7, 1.3)
            
            # Occasional longer pauses (thinking)
            if random.random() < 0.05:  # 5% chance
                char_delay *= random.uniform(2.0, 4.0)
            
            pattern.append({
                "char": char,
                "delay": char_delay,
                "timestamp": sum(p["delay"] for p in pattern) + char_delay
            })
        
        return pattern
    
    def get_scroll_pattern(self, distance: int) -> List[Dict[str, Any]]:
        """Generate human-like scroll pattern"""
        
        pattern = []
        remaining = abs(distance)
        direction = 1 if distance > 0 else -1
        
        while remaining > 0:
            # Random scroll amount
            scroll_amount = min(remaining, random.randint(50, 200))
            
            # Random delay between scrolls
            delay = random.uniform(100, 300)
            
            pattern.append({
                "amount": scroll_amount * direction,
                "delay": delay,
                "easing": random.choice(["linear", "ease-out", "ease-in-out"])
            })
            
            remaining -= scroll_amount
        
        return pattern
    
    def _load_stealth_patterns(self) -> Dict[str, Any]:
        """Load stealth behavior patterns"""
        
        return {
            "mouse_movements": {
                "natural_curve": True,
                "speed_variance": 0.2,
                "pause_probability": 0.1,
                "overshoot_probability": 0.05,
            },
            "click_patterns": {
                "double_click_threshold": random.randint(300, 500),
                "click_duration": random.randint(50, 150),
                "release_variance": random.randint(10, 50),
            },
            "page_interaction": {
                "scroll_before_click": 0.3,  # 30% chance
                "hover_before_click": 0.4,   # 40% chance
                "random_movements": 0.2,     # 20% chance
            }
        }
    
    def _load_human_behavior_config(self) -> Dict[str, Any]:
        """Load human behavior configuration"""
        
        return {
            "reading_speed": {
                "words_per_minute": random.randint(200, 300),
                "variance": 0.3,
            },
            "attention_span": {
                "focus_duration": random.randint(30, 120),  # seconds
                "break_probability": 0.1,
            },
            "interaction_patterns": {
                "exploration_probability": 0.15,
                "back_button_usage": 0.1,
                "new_tab_probability": 0.05,
            }
        }
    
    def should_take_break(self, session_duration: int) -> bool:
        """Determine if a human-like break should be taken"""
        
        # Probability increases with session duration
        base_probability = 0.01  # 1% per minute
        duration_factor = session_duration / 60  # Convert to minutes
        
        probability = min(0.3, base_probability * duration_factor)
        
        return random.random() < probability
    
    def get_break_duration(self) -> float:
        """Get human-like break duration"""

        # Most breaks are short, some are longer
        if random.random() < 0.8:  # 80% short breaks
            return random.uniform(5, 30)  # 5-30 seconds
        else:  # 20% longer breaks
            return random.uniform(60, 300)  # 1-5 minutes

    def _get_ip_location(self, ip: str) -> Optional[Dict[str, Any]]:
        """Get location information for IP address"""

        try:
            # Use multiple IP geolocation services for reliability
            services = [
                f"http://ip-api.com/json/{ip}",
                f"https://ipapi.co/{ip}/json/",
                f"https://freegeoip.app/json/{ip}"
            ]

            for service_url in services:
                try:
                    response = requests.get(service_url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()

                        # Normalize response format
                        if 'country' in data or 'country_name' in data:
                            return {
                                'country': data.get('country') or data.get('country_name'),
                                'country_code': data.get('countryCode') or data.get('country_code'),
                                'region': data.get('regionName') or data.get('region'),
                                'city': data.get('city'),
                                'timezone': data.get('timezone'),
                                'lat': data.get('lat') or data.get('latitude'),
                                'lon': data.get('lon') or data.get('longitude')
                            }
                except Exception as e:
                    logger.debug(f"Failed to get location from {service_url}: {e}")
                    continue

        except Exception as e:
            logger.warning(f"Failed to get IP location for {ip}: {e}")

        return None

    def _get_timezone_config(self) -> Dict[str, Any]:
        """Configure timezone to match proxy location"""

        config = {}

        if self.proxy_location and self.proxy_location.get('timezone'):
            # Use proxy timezone
            proxy_timezone = self.proxy_location['timezone']
            config['timezone'] = proxy_timezone

            # Set geolocation to match proxy location
            if self.proxy_location.get('lat') and self.proxy_location.get('lon'):
                config.update({
                    'geolocation:latitude': float(self.proxy_location['lat']),
                    'geolocation:longitude': float(self.proxy_location['lon']),
                    'geolocation:accuracy': random.randint(10, 100)
                })

            logger.info(f"Set timezone to match proxy: {proxy_timezone}")
        else:
            # Use common timezones
            common_timezones = [
                'America/New_York', 'America/Los_Angeles', 'Europe/London',
                'Europe/Berlin', 'Asia/Tokyo', 'Asia/Shanghai', 'Australia/Sydney'
            ]
            config['timezone'] = random.choice(common_timezones)

        return config

    def _get_language_config(self) -> Dict[str, Any]:
        """Configure language to match proxy location"""

        config = {}

        if self.proxy_location and self.proxy_location.get('country_code'):
            country_code = self.proxy_location['country_code'].upper()

            # Map country codes to languages
            country_languages = {
                'US': ['en-US', 'en'],
                'GB': ['en-GB', 'en'],
                'CA': ['en-CA', 'fr-CA', 'en'],
                'AU': ['en-AU', 'en'],
                'DE': ['de-DE', 'de', 'en'],
                'FR': ['fr-FR', 'fr', 'en'],
                'ES': ['es-ES', 'es', 'en'],
                'IT': ['it-IT', 'it', 'en'],
                'JP': ['ja-JP', 'ja', 'en'],
                'CN': ['zh-CN', 'zh', 'en'],
                'KR': ['ko-KR', 'ko', 'en'],
                'RU': ['ru-RU', 'ru', 'en'],
                'BR': ['pt-BR', 'pt', 'en'],
                'IN': ['hi-IN', 'en-IN', 'en'],
                'NL': ['nl-NL', 'nl', 'en'],
                'SE': ['sv-SE', 'sv', 'en'],
                'NO': ['nb-NO', 'no', 'en'],
                'DK': ['da-DK', 'da', 'en'],
                'FI': ['fi-FI', 'fi', 'en'],
            }

            languages = country_languages.get(country_code, ['en-US', 'en'])

            config.update({
                'navigator.language': languages[0],
                'navigator.languages': languages
            })

            logger.info(f"Set language to match proxy country {country_code}: {languages[0]}")

        return config

    def _get_dns_config(self) -> Dict[str, Any]:
        """Configure DNS to prevent leaks using Firefox preferences"""

        config = {}

        # Use secure DNS servers that match proxy location
        if self.proxy_location and self.proxy_location.get('country_code'):
            country_code = self.proxy_location['country_code'].upper()

            # Country-specific DNS servers
            country_dns = {
                'US': ['*******', '*******'],
                'GB': ['*******', '*******'],
                'DE': ['9.9.9.9', '*******'],
                'FR': ['*******', '9.9.9.9'],
                'JP': ['*******', '*******'],
                'CN': ['114.114.114.114', '*********'],
                'RU': ['*********', '*******'],
            }

            dns_servers = country_dns.get(country_code, ['*******', '*******'])
        else:
            # Default secure DNS
            dns_servers = ['*******', '*******']

        # DNS configuration is handled by Camoufox internally
        # We don't need to configure DNS manually as Camoufox handles it
        # Just log the DNS servers we would use for reference
        logger.debug(f"DNS servers for proxy location: {dns_servers}")

        # Return empty config as DNS is handled internally by Camoufox
        config = {}

        return config

    def get_dns_prefs(self) -> Dict[str, Any]:
        """Get DNS preferences for Firefox (to be used with firefox_user_prefs parameter)"""

        # Use secure DNS servers that match proxy location
        if self.proxy_location and self.proxy_location.get('country_code'):
            country_code = self.proxy_location['country_code'].upper()

            # Country-specific DNS servers
            country_dns = {
                'US': ['*******', '*******'],
                'GB': ['*******', '*******'],
                'DE': ['9.9.9.9', '*******'],
                'FR': ['*******', '9.9.9.9'],
                'JP': ['*******', '*******'],
                'CN': ['114.114.114.114', '*********'],
                'RU': ['*********', '*******'],
            }

            dns_servers = country_dns.get(country_code, ['*******', '*******'])
        else:
            # Default secure DNS
            dns_servers = ['*******', '*******']

        # Return Firefox preferences for DNS configuration
        return {
            'network.trr.mode': 2,  # Enable DoH with fallback
            'network.trr.uri': 'https://cloudflare-dns.com/dns-query',
            'network.trr.bootstrapAddress': dns_servers[0],
            'network.dns.disableIPv6': False,  # Allow IPv6 DNS
            'network.proxy.socks_remote_dns': True,  # Use proxy for DNS when using SOCKS
        }

    def _get_browser_version_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure browser version consistency"""

        config = {}

        # Ensure user agent matches actual browser version
        if 'navigator.userAgent' in base_config:
            user_agent = base_config['navigator.userAgent']

            # Extract Firefox version from user agent
            import re
            firefox_match = re.search(r'Firefox/(\d+\.\d+)', user_agent)
            if firefox_match:
                firefox_version = firefox_match.group(1)

                # Set consistent browser version across all properties
                config.update({
                    'navigator.appVersion': f"5.0 (compatible; Firefox {firefox_version})",
                    'navigator.buildID': self._generate_build_id(firefox_version),
                    'navigator.productSub': "20100101",
                    # 'browser.version.consistency': True,  # Removed - not supported by Camoufox
                })

        return config

    def _generate_local_ip_for_country(self, country_code: str) -> str:
        """Generate local IP that matches country patterns"""

        # Country-specific private IP ranges (for realism)
        country_ip_patterns = {
            'US': ['192.168', '10.0', '172.16'],
            'GB': ['192.168', '10.1', '172.17'],
            'DE': ['192.168', '10.2', '172.18'],
            'FR': ['192.168', '10.3', '172.19'],
            'JP': ['192.168', '10.4', '172.20'],
            'CN': ['192.168', '10.5', '172.21'],
            'RU': ['192.168', '10.6', '172.22'],
        }

        patterns = country_ip_patterns.get(country_code.upper(), ['192.168'])
        pattern = random.choice(patterns)

        if pattern.startswith('192.168'):
            return f"{pattern}.{random.randint(1, 254)}.{random.randint(1, 254)}"
        elif pattern.startswith('10.'):
            return f"{pattern}.{random.randint(1, 254)}.{random.randint(1, 254)}"
        else:  # 172.x
            return f"{pattern}.{random.randint(1, 254)}.{random.randint(1, 254)}"

    def _generate_build_id(self, firefox_version: str) -> str:
        """Generate realistic Firefox build ID"""

        # Build ID format: YYYYMMDDHHMMSS
        # Use dates that make sense for the Firefox version
        version_dates = {
            '119': '20231101000000',
            '118': '20231001000000',
            '117': '20230901000000',
            '116': '20230801000000',
            '115': '20230701000000',
        }

        major_version = firefox_version.split('.')[0]
        base_date = version_dates.get(major_version, '20231101000000')

        # Add some randomization to the build ID
        import datetime
        base_dt = datetime.datetime.strptime(base_date, '%Y%m%d%H%M%S')
        random_days = random.randint(0, 30)
        random_hours = random.randint(0, 23)
        random_minutes = random.randint(0, 59)

        final_dt = base_dt.replace(
            day=min(28, base_dt.day + random_days),
            hour=random_hours,
            minute=random_minutes
        )

        return final_dt.strftime('%Y%m%d%H%M%S')

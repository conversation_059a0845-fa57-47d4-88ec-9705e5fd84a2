# =============================================================================
# TikTok Automation Backend Environment Configuration
# =============================================================================
# Copy this file to .env and update the values according to your setup
# DO NOT commit .env file to version control

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
# Environment mode: development, production, testing
ENVIRONMENT=development

# Debug mode (true/false)
DEBUG=true

# Application secret key for JWT tokens and encryption
# Generate a secure random string: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-super-secret-key-change-this-in-production

# Application host and port
HOST=127.0.0.1
PORT=8000

# CORS allowed origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# SQLite database URL (default for development)
DATABASE_URL=sqlite:///./tiktok_automation.db

# Alternative PostgreSQL configuration (uncomment if using PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/tiktok_automation

# Database connection pool settings
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# =============================================================================
# TIKTOK AUTOMATION SETTINGS
# =============================================================================
# Rate limiting settings to avoid detection
TIKTOK_FOLLOW_LIMIT_PER_HOUR=50
TIKTOK_FOLLOW_LIMIT_PER_DAY=200
TIKTOK_UNFOLLOW_LIMIT_PER_HOUR=30
TIKTOK_UNFOLLOW_LIMIT_PER_DAY=150

# Delay settings (in seconds)
TIKTOK_MIN_DELAY_BETWEEN_ACTIONS=3
TIKTOK_MAX_DELAY_BETWEEN_ACTIONS=8
TIKTOK_PAGE_LOAD_TIMEOUT=30

# TikTok API settings (if using official API)
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret

# =============================================================================
# BROWSER & CAMOUFOX SETTINGS
# =============================================================================
# Maximum number of concurrent browser instances
MAX_CONCURRENT_BROWSERS=3

# Browser timeout settings (in seconds)
BROWSER_TIMEOUT=60
BROWSER_PAGE_TIMEOUT=30

# Camoufox settings
CAMOUFOX_HEADLESS=false
CAMOUFOX_DOWNLOAD_PATH=./downloads/camoufox

# Browser profile settings
BROWSER_PROFILES_DIR=./data/browser_profiles
BROWSER_CACHE_DIR=./data/browser_cache

# =============================================================================
# PROXY CONFIGURATION
# =============================================================================
# Default proxy settings (leave empty to disable)
DEFAULT_PROXY_TYPE=
DEFAULT_PROXY_HOST=
DEFAULT_PROXY_PORT=
DEFAULT_PROXY_USERNAME=
DEFAULT_PROXY_PASSWORD=

# Proxy rotation settings
PROXY_ROTATION_ENABLED=false
PROXY_HEALTH_CHECK_INTERVAL=300

# =============================================================================
# REDIS CONFIGURATION (for Celery and caching)
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Redis connection pool settings
REDIS_MAX_CONNECTIONS=20
REDIS_SOCKET_TIMEOUT=5

# =============================================================================
# CELERY CONFIGURATION (Background Tasks)
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Celery worker settings
CELERY_WORKER_CONCURRENCY=4
CELERY_TASK_SOFT_TIME_LIMIT=300
CELERY_TASK_TIME_LIMIT=600

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# JWT token settings
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Password hashing settings
PASSWORD_HASH_ROUNDS=12

# API rate limiting
API_RATE_LIMIT_PER_MINUTE=60
API_RATE_LIMIT_PER_HOUR=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Log file settings
LOG_FILE_PATH=./logs/app.log
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5

# Enable/disable specific loggers
LOG_SQL_QUERIES=false
LOG_HTTP_REQUESTS=true
LOG_BROWSER_ACTIONS=true

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
# Memory limits (in MB)
MEMORY_LIMIT_MB=1024
BROWSER_MEMORY_LIMIT_MB=512

# Cache settings
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# File upload limits
MAX_UPLOAD_SIZE_MB=50
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Enable performance monitoring
MONITORING_ENABLED=true

# Metrics collection interval (in seconds)
METRICS_COLLECTION_INTERVAL=60

# Health check settings
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email settings (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Webhook settings
WEBHOOK_URL=
WEBHOOK_SECRET=

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Auto-reload on code changes
AUTO_RELOAD=true

# Enable API documentation
ENABLE_DOCS=true
DOCS_URL=/docs
REDOC_URL=/redoc

# Test database URL (for running tests)
TEST_DATABASE_URL=sqlite:///./test_tiktok_automation.db

# =============================================================================
# BACKUP SETTINGS
# =============================================================================
# Database backup settings
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_DIRECTORY=./backups

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable specific features
FEATURE_COMPETITOR_ANALYSIS=true
FEATURE_AUTO_FOLLOW=true
FEATURE_AUTO_UNFOLLOW=true
FEATURE_CONTENT_SCRAPING=true
FEATURE_ANALYTICS_DASHBOARD=true

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Sentry error tracking (optional)
SENTRY_DSN=

# Google Analytics (optional)
GOOGLE_ANALYTICS_ID=

# =============================================================================
# MOBILE DEVICE SIMULATION
# =============================================================================
# Default mobile device settings
DEFAULT_MOBILE_DEVICE=iPhone 13 Pro
DEFAULT_USER_AGENT=Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15

# =============================================================================
# ANTI-DETECTION SETTINGS
# =============================================================================
# Fingerprint randomization
RANDOMIZE_FINGERPRINTS=true
FINGERPRINT_UPDATE_INTERVAL=3600

# Human-like behavior simulation
SIMULATE_HUMAN_BEHAVIOR=true
RANDOM_MOUSE_MOVEMENTS=true
RANDOM_SCROLL_PATTERNS=true

# =============================================================================
# DATA STORAGE
# =============================================================================
# Data directories
DATA_DIR=./data
PROFILES_DIR=./data/profiles
COOKIES_DIR=./data/cookies
SCREENSHOTS_DIR=./data/screenshots
LOGS_DIR=./logs

# Data retention settings
COOKIE_RETENTION_DAYS=30
LOG_RETENTION_DAYS=7
SCREENSHOT_RETENTION_DAYS=3

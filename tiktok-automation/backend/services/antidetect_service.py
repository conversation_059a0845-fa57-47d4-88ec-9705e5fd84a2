"""
Antidetect Service for testing and optimizing stealth capabilities
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from loguru import logger

from camoufox_integration.browser_manager import BrowserManager
from camoufox_integration.fingerprint_generator import FingerprintGenerator
from camoufox_integration.antidetect_config import AntidetectConfig
from services.profile_service import ProfileService
from core.database import get_async_session
from models.browser_profile import BrowserProfile


class AntidetectService:
    """Service for testing and optimizing antidetect capabilities"""
    
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.fingerprint_generator = FingerprintGenerator()
        self.antidetect_config = AntidetectConfig()
        self.profile_service = ProfileService()
        
        # Test sites for detection validation
        self.test_sites = {
            "bot_sannysoft": {
                "url": "https://bot.sannysoft.com/",
                "name": "Sannysoft Bot Detector",
                "checks": ["webdriver", "chrome", "permissions", "plugins", "languages"]
            },
            "intoli_headless": {
                "url": "https://intoli.com/blog/not-possible-to-block-chrome-headless/chrome-headless-test.html",
                "name": "Intoli Headless Test",
                "checks": ["headless_detection", "user_agent", "plugins"]
            },
            "areyouheadless": {
                "url": "https://arh.antoinevastel.com/bots/areyouheadless",
                "name": "Are You Headless",
                "checks": ["headless_detection", "webdriver", "chrome_runtime"]
            },
            "pixelscan": {
                "url": "https://pixelscan.net/",
                "name": "PixelScan Fingerprint",
                "checks": ["canvas", "webgl", "audio", "fonts", "timezone"]
            },
            "browserleaks_webrtc": {
                "url": "https://browserleaks.com/webrtc",
                "name": "BrowserLeaks WebRTC",
                "checks": ["webrtc_leak", "local_ip", "public_ip"]
            }
        }
    
    async def run_comprehensive_test(
        self,
        profile_id: int,
        test_sites: List[str] = None,
        include_tiktok_test: bool = True,
        headless: bool = False
    ) -> Dict[str, Any]:
        """Run comprehensive antidetect test"""
        
        start_time = time.time()
        
        try:
            # Get profile
            profile = await self.profile_service.get_profile(profile_id)
            if not profile:
                raise ValueError(f"Profile {profile_id} not found")
            
            # Create browser instance
            browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                headless=headless
            )
            
            context = await self.browser_manager.create_browser_context(
                browser, profile, None
            )
            
            test_results = {}
            overall_score = 0.0
            total_tests = 0
            
            # Test default sites if none specified
            if not test_sites:
                test_sites = list(self.test_sites.keys())
            
            # Run tests on each site
            for site_key in test_sites:
                if site_key in self.test_sites:
                    site_info = self.test_sites[site_key]
                    result = await self._test_site(context, site_info)
                    test_results[site_key] = result
                    
                    if result.get("score") is not None:
                        overall_score += result["score"]
                        total_tests += 1
            
            # Test TikTok access if requested
            if include_tiktok_test:
                tiktok_result = await self._test_tiktok_access(context)
                test_results["tiktok_access"] = tiktok_result
                
                if tiktok_result.get("score") is not None:
                    overall_score += tiktok_result["score"]
                    total_tests += 1
            
            # Calculate overall score
            if total_tests > 0:
                overall_score = overall_score / total_tests
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(test_results, profile)
            
            # Cleanup
            await self.browser_manager.close_context(context)
            await self.browser_manager.close_browser(browser)
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "profile_id": profile_id,
                "test_results": test_results,
                "overall_score": round(overall_score, 2),
                "recommendations": recommendations,
                "execution_time": round(execution_time, 2),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Antidetect test failed for profile {profile_id}: {e}")
            return {
                "success": False,
                "profile_id": profile_id,
                "test_results": {},
                "overall_score": 0.0,
                "recommendations": [f"Test failed: {str(e)}"],
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _test_site(self, context, site_info: Dict[str, Any]) -> Dict[str, Any]:
        """Test a specific detection site"""
        
        try:
            page = await context.new_page()
            
            # Navigate to test site
            response = await page.goto(site_info["url"], timeout=30000)
            
            if not response or response.status != 200:
                return {
                    "success": False,
                    "error": f"Failed to load {site_info['name']}",
                    "score": 0.0
                }
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            # Run site-specific checks
            checks_passed = 0
            total_checks = len(site_info["checks"])
            check_results = {}
            
            for check in site_info["checks"]:
                result = await self._run_detection_check(page, check)
                check_results[check] = result
                if result.get("passed", False):
                    checks_passed += 1
            
            score = (checks_passed / total_checks) * 100 if total_checks > 0 else 0
            
            await page.close()
            
            return {
                "success": True,
                "site": site_info["name"],
                "url": site_info["url"],
                "checks_passed": checks_passed,
                "total_checks": total_checks,
                "score": score,
                "check_results": check_results
            }
            
        except Exception as e:
            logger.error(f"Failed to test site {site_info['name']}: {e}")
            return {
                "success": False,
                "site": site_info["name"],
                "error": str(e),
                "score": 0.0
            }
    
    async def _run_detection_check(self, page, check_type: str) -> Dict[str, Any]:
        """Run specific detection check"""
        
        try:
            if check_type == "webdriver":
                # Check for webdriver property
                result = await page.evaluate("() => window.navigator.webdriver")
                return {
                    "passed": result is None or result is False,
                    "value": result,
                    "description": "WebDriver property check"
                }
            
            elif check_type == "chrome":
                # Check for chrome property
                result = await page.evaluate("() => window.chrome")
                return {
                    "passed": result is not None,
                    "value": result is not None,
                    "description": "Chrome object presence"
                }
            
            elif check_type == "headless_detection":
                # Multiple headless detection methods
                checks = await page.evaluate("""() => {
                    const results = {};
                    
                    // Check for headless-specific properties
                    results.outerHeight = window.outerHeight === 0;
                    results.outerWidth = window.outerWidth === 0;
                    results.screenY = window.screenY === 0;
                    results.screenX = window.screenX === 0;
                    
                    // Check for missing plugins
                    results.pluginsLength = navigator.plugins.length === 0;
                    
                    // Check for webdriver
                    results.webdriver = navigator.webdriver === true;
                    
                    return results;
                }""")
                
                # Count failed checks (indicators of headless)
                failed_checks = sum(1 for v in checks.values() if v)
                passed = failed_checks <= 1  # Allow 1 failed check
                
                return {
                    "passed": passed,
                    "value": checks,
                    "description": "Headless browser detection"
                }
            
            elif check_type == "canvas":
                # Canvas fingerprinting test
                result = await page.evaluate("""() => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillText('Canvas fingerprint test', 2, 2);
                    return canvas.toDataURL();
                }""")
                
                return {
                    "passed": len(result) > 100,  # Valid canvas data
                    "value": len(result),
                    "description": "Canvas fingerprinting capability"
                }
            
            elif check_type == "webgl":
                # WebGL support test
                result = await page.evaluate("""() => {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    if (!gl) return null;
                    
                    return {
                        vendor: gl.getParameter(gl.VENDOR),
                        renderer: gl.getParameter(gl.RENDERER),
                        version: gl.getParameter(gl.VERSION)
                    };
                }""")
                
                return {
                    "passed": result is not None,
                    "value": result,
                    "description": "WebGL support and info"
                }
            
            else:
                return {
                    "passed": True,
                    "value": None,
                    "description": f"Unknown check type: {check_type}"
                }
                
        except Exception as e:
            logger.error(f"Detection check {check_type} failed: {e}")
            return {
                "passed": False,
                "error": str(e),
                "description": f"Check {check_type} failed"
            }
    
    async def _test_tiktok_access(self, context) -> Dict[str, Any]:
        """Test TikTok access and detection"""
        
        try:
            page = await context.new_page()
            
            # Navigate to TikTok
            response = await page.goto("https://www.tiktok.com/", timeout=30000)
            
            if not response or response.status != 200:
                return {
                    "success": False,
                    "error": "Failed to load TikTok",
                    "score": 0.0
                }
            
            # Wait for page load
            await page.wait_for_load_state("networkidle", timeout=15000)
            
            # Check for bot detection indicators
            bot_detected = False
            detection_indicators = []
            
            # Check for CAPTCHA
            captcha_present = await page.locator("iframe[src*='captcha']").count() > 0
            if captcha_present:
                bot_detected = True
                detection_indicators.append("CAPTCHA challenge")
            
            # Check for access denied messages
            access_denied = await page.locator("text=Access denied").count() > 0
            if access_denied:
                bot_detected = True
                detection_indicators.append("Access denied message")
            
            # Check for unusual redirects
            current_url = page.url
            if "blocked" in current_url.lower() or "denied" in current_url.lower():
                bot_detected = True
                detection_indicators.append("Blocked/denied redirect")
            
            score = 0.0 if bot_detected else 100.0
            
            await page.close()
            
            return {
                "success": True,
                "site": "TikTok",
                "url": "https://www.tiktok.com/",
                "bot_detected": bot_detected,
                "detection_indicators": detection_indicators,
                "score": score,
                "final_url": current_url
            }
            
        except Exception as e:
            logger.error(f"TikTok access test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "score": 0.0
            }
    
    async def _generate_recommendations(
        self, 
        test_results: Dict[str, Any], 
        profile: BrowserProfile
    ) -> List[str]:
        """Generate recommendations based on test results"""
        
        recommendations = []
        
        # Analyze overall performance
        failed_tests = [k for k, v in test_results.items() 
                       if v.get("score", 100) < 80]
        
        if failed_tests:
            recommendations.append(
                f"Consider optimizing profile configuration. Failed tests: {', '.join(failed_tests)}"
            )
        
        # Check for specific issues
        for test_name, result in test_results.items():
            if result.get("score", 100) < 50:
                if "webdriver" in str(result):
                    recommendations.append("Enable enhanced webdriver detection evasion")
                
                if "headless" in str(result):
                    recommendations.append("Consider running in non-headless mode for better stealth")
                
                if "canvas" in str(result):
                    recommendations.append("Update canvas fingerprinting configuration")
        
        # TikTok-specific recommendations
        if "tiktok_access" in test_results:
            tiktok_result = test_results["tiktok_access"]
            if tiktok_result.get("bot_detected", False):
                recommendations.append("TikTok bot detection triggered - consider profile rotation")
                recommendations.append("Implement longer delays between actions")
                recommendations.append("Use residential proxy for TikTok access")
        
        if not recommendations:
            recommendations.append("Profile configuration looks good! No major issues detected.")
        
        return recommendations
    
    async def get_test_sites(self) -> List[Dict[str, Any]]:
        """Get available test sites"""
        return [
            {
                "key": key,
                "name": info["name"],
                "url": info["url"],
                "checks": info["checks"]
            }
            for key, info in self.test_sites.items()
        ]
    
    async def optimize_profile(self, profile_id: int) -> Dict[str, Any]:
        """Optimize profile for better antidetect performance"""
        
        try:
            # Run test first to identify issues
            test_result = await self.run_comprehensive_test(profile_id)
            
            if not test_result["success"]:
                return {"success": False, "error": "Failed to run initial test"}
            
            # Generate optimized configuration
            optimizations = []
            
            if test_result["overall_score"] < 80:
                # Apply optimizations based on test results
                profile = await self.profile_service.get_profile(profile_id)
                
                # Update fingerprint configuration
                new_fingerprint = self.fingerprint_generator.generate_fingerprint(
                    profile.os_preference,
                    profile.browser_preference
                )
                
                await self.profile_service.update_profile(
                    profile_id,
                    fingerprint_config=new_fingerprint
                )
                
                optimizations.append("Updated fingerprint configuration")
                optimizations.append("Enhanced stealth settings")
            
            return {
                "success": True,
                "profile_id": profile_id,
                "original_score": test_result["overall_score"],
                "optimizations_applied": optimizations,
                "recommendations": test_result["recommendations"]
            }
            
        except Exception as e:
            logger.error(f"Profile optimization failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def validate_tiktok_access(self, profile_id: int) -> Dict[str, Any]:
        """Validate TikTok access specifically"""
        
        result = await self.run_comprehensive_test(
            profile_id=profile_id,
            test_sites=[],
            include_tiktok_test=True,
            headless=False
        )
        
        if result["success"] and "tiktok_access" in result["test_results"]:
            tiktok_result = result["test_results"]["tiktok_access"]
            return {
                "success": True,
                "profile_id": profile_id,
                "tiktok_accessible": not tiktok_result.get("bot_detected", True),
                "detection_score": tiktok_result.get("score", 0),
                "details": tiktok_result
            }
        
        return {"success": False, "error": "TikTok validation failed"}
    
    async def get_detection_statistics(self) -> Dict[str, Any]:
        """Get detection statistics across profiles"""
        
        # This would typically query a database of test results
        # For now, return mock statistics
        return {
            "total_profiles_tested": 0,
            "average_detection_score": 0.0,
            "tiktok_success_rate": 0.0,
            "most_common_issues": [],
            "last_updated": datetime.now().isoformat()
        }

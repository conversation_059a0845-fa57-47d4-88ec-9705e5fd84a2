"""
Profile Service for managing browser profiles
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from loguru import logger

from models.browser_profile import Browser<PERSON>rofile
from models.proxy import Proxy
from camoufox_integration.fingerprint_generator import FingerprintGenerator
from camoufox_integration.browser_manager import BrowserManager
from core.database import get_async_session_context


class ProfileService:
    """Service for managing browser profiles"""
    
    def __init__(self, session: AsyncSession = None):
        self.session = session
        self.fingerprint_generator = FingerprintGenerator()
        self.browser_manager = BrowserManager()
    
    async def create_profile(
        self,
        name: str,
        description: Optional[str] = None,
        proxy_id: Optional[int] = None,
        fingerprint_config: Optional[Dict[str, Any]] = None,
        auto_generate_fingerprint: bool = True,
        os_preference: str = "windows",
        browser_preference: str = "firefox"
    ) -> BrowserProfile:
        """Create a new browser profile"""
        
        async with get_async_session_context() as session:
            try:
                # Check if name already exists
                existing = await session.execute(
                    select(<PERSON>rowserProfile).where(BrowserProfile.name == name)
                )
                if existing.scalar_one_or_none():
                    raise ValueError(f"Profile with name '{name}' already exists")
                
                # Generate fingerprint if needed
                if auto_generate_fingerprint and not fingerprint_config:
                    fingerprint_config = self.fingerprint_generator.generate_fingerprint(
                        os_preference=os_preference,
                        browser_preference=browser_preference
                    )
                
                # Extract specific configurations from fingerprint
                navigator_config = {}
                screen_config = {}
                window_config = {}
                webgl_config = {}
                audio_config = {}
                geolocation_config = {}
                fonts_config = {}
                
                if fingerprint_config:
                    for key, value in fingerprint_config.items():
                        if key.startswith("navigator."):
                            navigator_config[key] = value
                        elif key.startswith("screen."):
                            screen_config[key.replace("screen.", "")] = value
                        elif key.startswith("window."):
                            window_config[key.replace("window.", "")] = value
                        elif key.startswith("webGl"):
                            webgl_config[key] = value
                        elif key.startswith("AudioContext:"):
                            audio_config[key.replace("AudioContext:", "")] = value
                        elif key.startswith("geolocation:"):
                            geolocation_config[key.replace("geolocation:", "")] = value
                        elif key == "fonts":
                            fonts_config["fonts"] = value
                
                # Extract user agent
                user_agent = navigator_config.get("navigator.userAgent")
                
                # Extract timezone and locale
                timezone = fingerprint_config.get("timezone") if fingerprint_config else None
                locale = None
                if fingerprint_config:
                    lang = fingerprint_config.get("locale:language")
                    region = fingerprint_config.get("locale:region")
                    if lang and region:
                        locale = f"{lang}-{region}"
                
                # Create profile
                profile = BrowserProfile(
                    name=name,
                    description=description,
                    proxy_id=proxy_id,
                    fingerprint_config=fingerprint_config or {},
                    user_agent=user_agent,
                    navigator_config=navigator_config,
                    screen_config=screen_config,
                    window_config=window_config,
                    webgl_config=webgl_config,
                    audio_config=audio_config,
                    geolocation_config=geolocation_config,
                    timezone=timezone,
                    locale=locale,
                    fonts_config=fonts_config
                )
                
                session.add(profile)
                await session.commit()
                await session.refresh(profile)
                
                logger.info(f"Created browser profile: {profile.name}")
                return profile
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error creating profile: {e}")
                raise
    
    async def get_profile(self, profile_id: int) -> Optional[BrowserProfile]:
        """Get profile by ID"""

        async with get_async_session_context() as session:
            try:
                result = await session.execute(
                    select(BrowserProfile)
                    .options(
                        selectinload(BrowserProfile.tiktok_accounts),
                        selectinload(BrowserProfile.proxy)
                    )
                    .where(BrowserProfile.id == profile_id)
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(f"Error getting profile {profile_id}: {e}")
                return None
    
    async def get_profiles(
        self,
        active_only: bool = False,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[BrowserProfile]:
        """Get list of profiles"""
        
        async with get_async_session_context() as session:
            try:
                query = select(BrowserProfile).options(
                    selectinload(BrowserProfile.tiktok_accounts),
                    selectinload(BrowserProfile.proxy)
                )
                
                if active_only:
                    query = query.where(BrowserProfile.is_active == True)
                
                query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(f"Error getting profiles: {e}")
                return []
    
    async def update_profile(
        self,
        profile_id: int,
        **updates
    ) -> Optional[BrowserProfile]:
        """Update profile"""

        async with get_async_session_context() as session:
            try:
                # Get existing profile
                profile = await session.get(BrowserProfile, profile_id)
                if not profile:
                    return None
                
                # Update fields
                for key, value in updates.items():
                    if hasattr(profile, key):
                        setattr(profile, key, value)
                
                await session.commit()
                await session.refresh(profile)
                
                logger.info(f"Updated profile: {profile.name}")
                return profile
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error updating profile {profile_id}: {e}")
                raise
    
    async def delete_profile(self, profile_id: int) -> bool:
        """Delete profile"""

        session = self.session
        if not session:
            async with get_async_session_context() as session:
                return await self._delete_profile_impl(session, profile_id)
        else:
            return await self._delete_profile_impl(session, profile_id)

    async def _delete_profile_impl(self, session: AsyncSession, profile_id: int) -> bool:
        """Implementation of delete profile"""
        try:
            # Load profile with relationships
            result = await session.execute(
                select(BrowserProfile)
                .options(selectinload(BrowserProfile.tiktok_accounts))
                .where(BrowserProfile.id == profile_id)
            )
            profile = result.scalar_one_or_none()
            if not profile:
                return False

            # Check if profile is in use
            if profile.tiktok_accounts:
                raise ValueError("Cannot delete profile that has associated TikTok accounts")

            await session.delete(profile)
            await session.commit()

            logger.info(f"Deleted profile: {profile.name}")
            return True

        except Exception as e:
            await session.rollback()
            logger.error(f"Error deleting profile {profile_id}: {e}")
            raise
    
    async def duplicate_profile(
        self,
        profile_id: int,
        new_name: str,
        regenerate_fingerprint: bool = False
    ) -> Optional[BrowserProfile]:
        """Duplicate an existing profile"""

        async with get_async_session_context() as session:
            try:
                # Get original profile
                original = await session.get(BrowserProfile, profile_id)
                if not original:
                    return None
                
                # Check if new name exists
                existing = await session.execute(
                    select(BrowserProfile).where(BrowserProfile.name == new_name)
                )
                if existing.scalar_one_or_none():
                    raise ValueError(f"Profile with name '{new_name}' already exists")
                
                # Create duplicate
                duplicate = BrowserProfile(
                    name=new_name,
                    description=f"Copy of {original.name}",
                    proxy_id=original.proxy_id,
                    fingerprint_config=original.fingerprint_config.copy() if original.fingerprint_config else {},
                    user_agent=original.user_agent,
                    navigator_config=original.navigator_config.copy() if original.navigator_config else {},
                    screen_config=original.screen_config.copy() if original.screen_config else {},
                    window_config=original.window_config.copy() if original.window_config else {},
                    webgl_config=original.webgl_config.copy() if original.webgl_config else {},
                    audio_config=original.audio_config.copy() if original.audio_config else {},
                    geolocation_config=original.geolocation_config.copy() if original.geolocation_config else {},
                    timezone=original.timezone,
                    locale=original.locale,
                    fonts_config=original.fonts_config.copy() if original.fonts_config else {}
                )
                
                # Regenerate fingerprint if requested
                if regenerate_fingerprint:
                    os_pref = self.browser_manager._extract_os_from_user_agent(original.user_agent)
                    new_fingerprint = self.fingerprint_generator.generate_fingerprint(
                        os_preference=os_pref,
                        browser_preference="firefox"
                    )
                    
                    # Update duplicate with new fingerprint
                    duplicate.fingerprint_config = new_fingerprint
                    duplicate.user_agent = new_fingerprint.get("navigator.userAgent", original.user_agent)
                
                session.add(duplicate)
                await session.commit()
                await session.refresh(duplicate)
                
                logger.info(f"Duplicated profile: {original.name} -> {duplicate.name}")
                return duplicate
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error duplicating profile {profile_id}: {e}")
                raise
    
    async def test_profile(self, profile_id: int) -> Dict[str, Any]:
        """Test profile by launching browser"""
        
        try:
            profile = await self.get_profile(profile_id)
            if not profile:
                return {"success": False, "error": "Profile not found"}
            
            # Get proxy if assigned
            proxy = None
            if profile.proxy_id:
                async with get_async_session_context() as session:
                    proxy = await session.get(Proxy, profile.proxy_id)
            
            # Launch browser
            browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                proxy=proxy,
                headless=True
            )
            
            # Create context and test page
            context = await self.browser_manager.create_browser_context(browser, profile, proxy)
            page = await context.new_page()
            
            # Test navigation
            await page.goto("https://httpbin.org/headers", timeout=30000)
            content = await page.content()
            
            # Cleanup
            await page.close()
            await self.browser_manager.close_context(context)
            await self.browser_manager.close_browser(browser)
            
            # Update profile usage
            profile.update_usage()
            async with get_async_session_context() as session:
                session.add(profile)
                await session.commit()
            
            return {
                "success": True,
                "message": "Profile test successful",
                "user_agent": profile.user_agent,
                "proxy_used": proxy.name if proxy else None
            }

        except Exception as e:
            logger.error(f"Error testing profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}

    async def start_login(self, profile_id: int) -> Dict[str, Any]:
        """Start login process for profile"""

        try:
            profile = await self.get_profile(profile_id)
            if not profile:
                return {"success": False, "error": "Profile not found"}

            # Get proxy if assigned
            proxy = None
            if profile.proxy_id:
                async with get_async_session_context() as session:
                    proxy = await session.get(Proxy, profile.proxy_id)

            # Launch browser for login
            logger.info(f"Creating browser instance for profile {profile.name} (ID: {profile_id})")
            try:
                browser = await self.browser_manager.create_browser_instance(
                    profile=profile,
                    proxy=proxy,
                    headless=False  # Visible for manual login
                )
                logger.info(f"Browser instance created successfully for profile {profile.name}")
            except Exception as browser_error:
                logger.error(f"Failed to create browser instance for profile {profile.name}: {browser_error}")
                return {
                    "success": False,
                    "error": f"Camoufox browser creation failed: {str(browser_error)}"
                }

            # Create context and navigate to TikTok login
            context = await self.browser_manager.create_browser_context(browser, profile, proxy)
            logger.info(f"Browser context created for profile {profile.name}")

            # Save login context for later use in complete_login
            self.browser_manager.save_login_context(profile_id, context)

            page = await context.new_page()
            logger.info(f"New page created for profile {profile.name}")

            # Navigate to TikTok login page
            logger.info(f"Navigating to TikTok login page for profile {profile.name}")
            await page.goto("https://www.tiktok.com/login", timeout=10000)
            logger.info(f"Successfully navigated to TikTok login page for profile {profile.name}")

            # Save initial storage state immediately after navigation
            try:
                cookies_path = self.browser_manager.get_cookies_path(profile_id)
                cookies_path.parent.mkdir(parents=True, exist_ok=True)
                await context.storage_state(path=str(cookies_path))
                logger.info(f"Initial storage state saved for profile {profile.name}")
            except Exception as e:
                logger.warning(f"Could not save initial storage state: {e}")

            # Update profile status
            async with get_async_session_context() as session:
                profile.status = "logging_in"
                profile.current_action = "Đang đăng nhập TikTok..."
                session.add(profile)
                await session.commit()

            logger.info(f"Login process started for profile {profile.name}")

            return {
                "success": True,
                "message": "Login process started. Please complete login manually in the browser.",
                "profile_id": profile_id,
                "proxy_used": proxy.name if proxy else "Local Network"
            }

        except Exception as e:
            logger.error(f"Error starting login for profile {profile_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def complete_login(self, profile_id: int) -> Dict[str, Any]:
        """Complete login process for profile - save cookies and close browser"""
        try:
            profile = await self.get_profile(profile_id)
            if not profile:
                return {"success": False, "error": "Profile not found"}

            # Save cookies before closing browser
            cookies_saved = False
            try:
                # Multiple strategies to get context and save cookies
                login_context = None

                # Strategy 1: Get login context specifically saved during login
                login_context = self.browser_manager.get_login_context(profile_id)
                logger.info(f"Login context found: {login_context is not None}")

                # Strategy 2: If no login context, try to get from active contexts
                if not login_context:
                    browser_contexts = self.browser_manager.get_contexts_for_profile(profile_id)
                    logger.info(f"Fallback: Found {len(browser_contexts)} active contexts")
                    if browser_contexts:
                        login_context = browser_contexts[0]
                        logger.info(f"Using active context as fallback")

                # Strategy 3: Check if cookies file already exists from periodic saving
                cookies_path = self.browser_manager.get_cookies_path(profile_id)
                if cookies_path.exists():
                    cookies_saved = True
                    logger.info(f"Cookies already saved during login session: {cookies_path}")

                # Strategy 4: Try to save from context if available
                if login_context and not cookies_saved:
                    # Ensure directory exists
                    cookies_path.parent.mkdir(parents=True, exist_ok=True)

                    try:
                        # Save storage state regardless of page state
                        await login_context.storage_state(path=str(cookies_path))
                        cookies_saved = True
                        logger.info(f"Storage state saved for profile {profile.name} to {cookies_path}")
                    except Exception as save_error:
                        logger.error(f"Error saving storage state: {save_error}")

                        # Strategy 5: Try to extract cookies manually
                        try:
                            cookies = await login_context.cookies()
                            if cookies:
                                import json
                                storage_state = {
                                    "cookies": cookies,
                                    "origins": []
                                }
                                with open(cookies_path, 'w') as f:
                                    json.dump(storage_state, f, indent=2)
                                cookies_saved = True
                                logger.info(f"Manual cookies saved for profile {profile.name}")
                        except Exception as manual_error:
                            logger.error(f"Manual cookie extraction failed: {manual_error}")

                    # Remove login context after saving cookies
                    self.browser_manager.remove_login_context(profile_id)

                if not cookies_saved:
                    logger.warning(f"Could not save cookies for profile {profile.name}")

            except Exception as cookie_error:
                logger.error(f"Failed to save storage state for profile {profile.name}: {cookie_error}")

            # Close browser after saving cookies
            try:
                await self.browser_manager.close_browser_for_profile(profile_id)
                logger.info(f"Browser closed for profile {profile.name}")
            except Exception as browser_error:
                logger.warning(f"Failed to close browser for profile {profile.name}: {browser_error}")

            # Update profile status to ready
            async with get_async_session_context() as session:
                profile.status = "ready"
                profile.current_action = "Sẵn sàng hoạt động"
                profile.is_logged_in = True
                profile.last_used = datetime.utcnow()
                session.add(profile)
                await session.commit()

            logger.info(f"Login completed for profile {profile.name}, cookies saved: {cookies_saved}")

            return {
                "success": True,
                "message": "Login completed successfully. Profile is now ready and browser has been closed.",
                "profile_id": profile_id,
                "cookies_saved": cookies_saved
            }

        except Exception as e:
            logger.error(f"Error completing login for profile {profile_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_profile_templates(self) -> List[Dict[str, Any]]:
        """Get predefined profile templates"""
        
        templates = [
            {
                "name": "Windows Desktop",
                "description": "Standard Windows desktop profile",
                "os_preference": "windows",
                "config": {
                    "screen.width": 1920,
                    "screen.height": 1080,
                    "window.outerWidth": 1366,
                    "window.outerHeight": 768
                }
            },
            {
                "name": "macOS Desktop", 
                "description": "Standard macOS desktop profile",
                "os_preference": "macos",
                "config": {
                    "screen.width": 1440,
                    "screen.height": 900,
                    "window.outerWidth": 1200,
                    "window.outerHeight": 800
                }
            },
            {
                "name": "Linux Desktop",
                "description": "Standard Linux desktop profile", 
                "os_preference": "linux",
                "config": {
                    "screen.width": 1366,
                    "screen.height": 768,
                    "window.outerWidth": 1024,
                    "window.outerHeight": 600
                }
            },
            {
                "name": "High Resolution",
                "description": "4K display profile",
                "os_preference": "windows",
                "config": {
                    "screen.width": 3840,
                    "screen.height": 2160,
                    "window.outerWidth": 1920,
                    "window.outerHeight": 1080,
                    "window.devicePixelRatio": 2
                }
            }
        ]
        
        return templates
    
    async def create_from_template(
        self,
        template_name: str,
        profile_name: str,
        proxy_id: Optional[int] = None
    ) -> Optional[BrowserProfile]:
        """Create profile from template"""
        
        templates = await self.get_profile_templates()
        template = next((t for t in templates if t["name"] == template_name), None)
        
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        return await self.create_profile(
            name=profile_name,
            description=template["description"],
            proxy_id=proxy_id,
            auto_generate_fingerprint=True,
            os_preference=template["os_preference"]
        )

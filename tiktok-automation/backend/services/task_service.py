"""
Task Service for managing automation tasks
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from loguru import logger

from models.follow_task import FollowTask, TaskStatus, TaskType
from models.tiktok_account import TikTokAccount
from models.competitor import Competitor
from models.browser_profile import BrowserProfile
from automation.tiktok_bot import T<PERSON><PERSON><PERSON><PERSON>ot
from automation.competitor_analyzer import Competitor<PERSON><PERSON>yzer
from automation.rate_limiter import RateLimiter
from core.database import get_async_session


class TaskService:
    """Service for managing automation tasks"""
    
    def __init__(self):
        self.tiktok_bot = TikTokBot()
        self.competitor_analyzer = CompetitorAnalyzer()
        self.rate_limiter = RateLimiter()
        self.active_tasks = {}  # task_id -> task_runner
    
    async def create_task(
        self,
        name: str,
        task_type: TaskType,
        tiktok_account_id: int,
        target_count: int,
        competitor_id: Optional[int] = None,
        browser_profile_id: Optional[int] = None,
        delay_min: int = 2,
        delay_max: int = 5,
        filter_criteria: Optional[Dict[str, Any]] = None,
        target_usernames: Optional[List[str]] = None,
        scheduled_start: Optional[datetime] = None,
        description: Optional[str] = None
    ) -> FollowTask:
        """Create a new automation task"""
        
        async with get_async_session() as session:
            try:
                # Validate account exists
                account = await session.get(TikTokAccount, tiktok_account_id)
                if not account:
                    raise ValueError(f"TikTok account {tiktok_account_id} not found")
                
                # Validate competitor if specified
                if competitor_id:
                    competitor = await session.get(Competitor, competitor_id)
                    if not competitor:
                        raise ValueError(f"Competitor {competitor_id} not found")
                
                # Validate browser profile if specified
                if browser_profile_id:
                    profile = await session.get(BrowserProfile, browser_profile_id)
                    if not profile:
                        raise ValueError(f"Browser profile {browser_profile_id} not found")
                else:
                    # Use account's default profile
                    browser_profile_id = account.browser_profile_id
                
                # Create task
                task = FollowTask(
                    name=name,
                    description=description,
                    task_type=task_type,
                    tiktok_account_id=tiktok_account_id,
                    competitor_id=competitor_id,
                    browser_profile_id=browser_profile_id,
                    target_count=target_count,
                    delay_min=delay_min,
                    delay_max=delay_max,
                    filter_criteria=filter_criteria or {},
                    target_usernames=target_usernames or [],
                    scheduled_start=scheduled_start
                )
                
                session.add(task)
                await session.commit()
                await session.refresh(task)
                
                logger.info(f"Created task: {task.name} (ID: {task.id})")
                return task
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error creating task: {e}")
                raise
    
    async def get_task(self, task_id: int) -> Optional[FollowTask]:
        """Get task by ID"""
        
        async with get_async_session() as session:
            try:
                result = await session.execute(
                    select(FollowTask)
                    .options(
                        selectinload(FollowTask.tiktok_account),
                        selectinload(FollowTask.competitor),
                        selectinload(FollowTask.browser_profile)
                    )
                    .where(FollowTask.id == task_id)
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(f"Error getting task {task_id}: {e}")
                return None
    
    async def get_tasks(
        self,
        account_id: Optional[int] = None,
        status: Optional[TaskStatus] = None,
        task_type: Optional[TaskType] = None,
        active_only: bool = False,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[FollowTask]:
        """Get list of tasks"""
        
        async with get_async_session() as session:
            try:
                query = select(FollowTask).options(
                    selectinload(FollowTask.tiktok_account),
                    selectinload(FollowTask.competitor),
                    selectinload(FollowTask.browser_profile)
                )
                
                if account_id:
                    query = query.where(FollowTask.tiktok_account_id == account_id)
                
                if status:
                    query = query.where(FollowTask.status == status)
                
                if task_type:
                    query = query.where(FollowTask.task_type == task_type)
                
                if active_only:
                    query = query.where(FollowTask.status.in_([
                        TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED
                    ]))
                
                query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(f"Error getting tasks: {e}")
                return []
    
    async def start_task(self, task_id: int) -> Dict[str, Any]:
        """Start a task"""
        
        try:
            task = await self.get_task(task_id)
            if not task:
                return {"success": False, "error": "Task not found"}
            
            if task.status != TaskStatus.PENDING:
                return {"success": False, "error": f"Task is {task.status.value}, cannot start"}
            
            # Check if account is available
            if task.tiktok_account_id in self.active_tasks:
                return {"success": False, "error": "Account is already running a task"}
            
            # Start task in background
            task_runner = asyncio.create_task(self._run_task(task))
            self.active_tasks[task.id] = task_runner
            
            # Update task status
            await self._update_task_status(task.id, TaskStatus.RUNNING)
            
            logger.info(f"Started task: {task.name} (ID: {task.id})")
            return {"success": True, "message": "Task started successfully"}
            
        except Exception as e:
            logger.error(f"Error starting task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def pause_task(self, task_id: int) -> Dict[str, Any]:
        """Pause a running task"""
        
        try:
            task = await self.get_task(task_id)
            if not task:
                return {"success": False, "error": "Task not found"}
            
            if task.status != TaskStatus.RUNNING:
                return {"success": False, "error": f"Task is {task.status.value}, cannot pause"}
            
            # Update task status
            await self._update_task_status(task.id, TaskStatus.PAUSED)
            
            logger.info(f"Paused task: {task.name} (ID: {task.id})")
            return {"success": True, "message": "Task paused successfully"}
            
        except Exception as e:
            logger.error(f"Error pausing task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def resume_task(self, task_id: int) -> Dict[str, Any]:
        """Resume a paused task"""
        
        try:
            task = await self.get_task(task_id)
            if not task:
                return {"success": False, "error": "Task not found"}
            
            if task.status != TaskStatus.PAUSED:
                return {"success": False, "error": f"Task is {task.status.value}, cannot resume"}
            
            # Update task status
            await self._update_task_status(task.id, TaskStatus.RUNNING)
            
            logger.info(f"Resumed task: {task.name} (ID: {task.id})")
            return {"success": True, "message": "Task resumed successfully"}
            
        except Exception as e:
            logger.error(f"Error resuming task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def stop_task(self, task_id: int) -> Dict[str, Any]:
        """Stop a task"""
        
        try:
            task = await self.get_task(task_id)
            if not task:
                return {"success": False, "error": "Task not found"}
            
            if task.status not in [TaskStatus.RUNNING, TaskStatus.PAUSED]:
                return {"success": False, "error": f"Task is {task.status.value}, cannot stop"}
            
            # Cancel task runner if active
            if task.id in self.active_tasks:
                task_runner = self.active_tasks[task.id]
                task_runner.cancel()
                del self.active_tasks[task.id]
            
            # Update task status
            await self._update_task_status(task.id, TaskStatus.CANCELLED)
            
            logger.info(f"Stopped task: {task.name} (ID: {task.id})")
            return {"success": True, "message": "Task stopped successfully"}
            
        except Exception as e:
            logger.error(f"Error stopping task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def delete_task(self, task_id: int) -> bool:
        """Delete a task"""
        
        async with get_async_session() as session:
            try:
                task = await session.get(FollowTask, task_id)
                if not task:
                    return False
                
                # Stop task if running
                if task.status in [TaskStatus.RUNNING, TaskStatus.PAUSED]:
                    await self.stop_task(task_id)
                
                # Delete task
                await session.delete(task)
                await session.commit()
                
                logger.info(f"Deleted task: {task.name} (ID: {task.id})")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error deleting task {task_id}: {e}")
                raise
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """Get task statistics"""
        
        async with get_async_session() as session:
            try:
                tasks = await session.execute(select(FollowTask))
                all_tasks = tasks.scalars().all()
                
                stats = {
                    "total": len(all_tasks),
                    "by_status": {},
                    "by_type": {},
                    "active": len(self.active_tasks),
                    "total_actions": 0,
                    "success_rate": 0
                }
                
                total_processed = 0
                total_successful = 0
                
                for task in all_tasks:
                    # Count by status
                    status = task.status.value
                    stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
                    
                    # Count by type
                    task_type = task.task_type.value
                    stats["by_type"][task_type] = stats["by_type"].get(task_type, 0) + 1
                    
                    # Calculate totals
                    total_processed += task.total_processed
                    total_successful += task.successful_actions
                
                stats["total_actions"] = total_processed
                if total_processed > 0:
                    stats["success_rate"] = (total_successful / total_processed) * 100
                
                return stats
                
            except Exception as e:
                logger.error(f"Error getting task statistics: {e}")
                return {}
    
    async def _run_task(self, task: FollowTask):
        """Run a task (background coroutine)"""
        
        try:
            logger.info(f"Starting task execution: {task.name}")
            
            # Mark task as started
            task.start_task()
            await self._save_task(task)
            
            # Get required objects
            account = task.tiktok_account
            profile = task.browser_profile or account.browser_profile
            competitor = task.competitor
            
            if not profile:
                raise ValueError("No browser profile available")
            
            # Initialize bot session
            proxy = None  # TODO: Get proxy from profile
            
            session_success = await self.tiktok_bot.initialize_session(
                account, profile, proxy, headless=True
            )
            
            if not session_success:
                raise Exception("Failed to initialize bot session")
            
            try:
                # Execute task based on type
                if task.task_type == TaskType.FOLLOW_FOLLOWERS:
                    await self._execute_follow_followers_task(task, competitor)
                elif task.task_type == TaskType.FOLLOW_FOLLOWING:
                    await self._execute_follow_following_task(task, competitor)
                elif task.task_type == TaskType.UNFOLLOW_USERS:
                    await self._execute_unfollow_task(task)
                else:
                    raise ValueError(f"Unsupported task type: {task.task_type}")
                
                # Mark task as completed
                task.complete_task(success=True)
                
            finally:
                await self.tiktok_bot.cleanup_session()
                
        except asyncio.CancelledError:
            logger.info(f"Task cancelled: {task.name}")
            task.cancel_task()
        except Exception as e:
            logger.error(f"Task execution failed: {task.name} - {e}")
            task.complete_task(success=False)
            task.error_message = str(e)
        finally:
            # Save final task state
            await self._save_task(task)
            
            # Remove from active tasks
            if task.id in self.active_tasks:
                del self.active_tasks[task.id]
    
    async def _execute_follow_followers_task(
        self, 
        task: FollowTask, 
        competitor: Competitor
    ):
        """Execute follow followers task"""
        
        # Get targeted followers
        followers = await self.competitor_analyzer.get_targeted_followers(
            competitor, 
            task.tiktok_account, 
            target_count=task.target_count,
            filters=task.filter_criteria
        )
        
        if not followers:
            raise Exception("No followers found to target")
        
        # Follow each user
        for follower in followers:
            # Check if task is paused
            if task.status == TaskStatus.PAUSED:
                while task.status == TaskStatus.PAUSED:
                    await asyncio.sleep(5)
                
                if task.status == TaskStatus.CANCELLED:
                    break
            
            # Check rate limits
            rate_check = await self.rate_limiter.can_perform_action(
                task.tiktok_account_id, "follow"
            )
            
            if not rate_check["can_proceed"]:
                await asyncio.sleep(rate_check["wait_time"])
            
            # Follow user
            result = await self.tiktok_bot.follow_user(follower["username"])
            
            # Update task progress
            task.update_progress(
                success=result["success"],
                error_message=result.get("error")
            )
            
            # Log action
            task.add_log_entry(
                action="follow",
                target=follower["username"],
                result="success" if result["success"] else "failed",
                details=result
            )
            
            # Save progress periodically
            if task.total_processed % 10 == 0:
                await self._save_task(task)
            
            # Human-like delay
            delay = random.uniform(task.delay_min, task.delay_max)
            await asyncio.sleep(delay)
            
            # Check if task is complete
            if task.total_processed >= task.target_count:
                break
    
    async def _execute_follow_following_task(
        self, 
        task: FollowTask, 
        competitor: Competitor
    ):
        """Execute follow following task (users that competitor follows)"""
        
        # Similar to follow_followers but targets competitor's following list
        # Implementation would be similar to _execute_follow_followers_task
        pass
    
    async def _execute_unfollow_task(self, task: FollowTask):
        """Execute unfollow task"""
        
        # Get list of users to unfollow
        target_usernames = task.target_usernames
        
        if not target_usernames:
            raise Exception("No usernames specified for unfollow task")
        
        # Unfollow each user
        for username in target_usernames:
            # Check if task is paused/cancelled
            if task.status == TaskStatus.PAUSED:
                while task.status == TaskStatus.PAUSED:
                    await asyncio.sleep(5)
                
                if task.status == TaskStatus.CANCELLED:
                    break
            
            # Check rate limits
            rate_check = await self.rate_limiter.can_perform_action(
                task.tiktok_account_id, "unfollow"
            )
            
            if not rate_check["can_proceed"]:
                await asyncio.sleep(rate_check["wait_time"])
            
            # Unfollow user
            result = await self.tiktok_bot.unfollow_user(username)
            
            # Update task progress
            task.update_progress(
                success=result["success"],
                error_message=result.get("error")
            )
            
            # Log action
            task.add_log_entry(
                action="unfollow",
                target=username,
                result="success" if result["success"] else "failed",
                details=result
            )
            
            # Save progress periodically
            if task.total_processed % 10 == 0:
                await self._save_task(task)
            
            # Human-like delay
            delay = random.uniform(task.delay_min, task.delay_max)
            await asyncio.sleep(delay)
            
            # Check if task is complete
            if task.total_processed >= task.target_count:
                break
    
    async def _update_task_status(self, task_id: int, status: TaskStatus):
        """Update task status in database"""
        
        async with get_async_session() as session:
            try:
                await session.execute(
                    update(FollowTask)
                    .where(FollowTask.id == task_id)
                    .values(status=status, updated_at=datetime.utcnow())
                )
                await session.commit()
            except Exception as e:
                logger.error(f"Error updating task status: {e}")
                await session.rollback()
    
    async def _save_task(self, task: FollowTask):
        """Save task to database"""
        
        async with get_async_session() as session:
            try:
                session.add(task)
                await session.commit()
            except Exception as e:
                logger.error(f"Error saving task: {e}")
                await session.rollback()

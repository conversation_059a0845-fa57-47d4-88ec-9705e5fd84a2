"""
Automation Service
Manages TikTok automation tasks and engines
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger

from automation.tiktok_engine import TikTokAutomationEngine, AutomationConfig
from models.automation_task import AutomationTask
from models.browser_profile import BrowserProfile
from core.database import get_async_session_context
from sqlalchemy import select
from sqlalchemy.orm import selectinload


class AutomationService:
    """Service for managing automation tasks"""
    
    def __init__(self):
        self.engines = {}  # profile_id -> engine
        self.default_config = AutomationConfig()
    
    async def create_automation_task(
        self,
        profile_id: int,
        task_type: str,
        target_data: Dict[str, Any],
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a new automation task"""
        
        try:
            async with get_async_session_context() as session:
                # Verify profile exists
                profile = await session.get(BrowserProfile, profile_id)
                if not profile:
                    return {"success": False, "error": "Profile not found"}
                
                # Create task
                task = AutomationTask(
                    profile_id=profile_id,
                    task_type=task_type,
                    target_data=target_data,
                    config=config or {},
                    status="created",
                    created_at=datetime.now()
                )
                
                session.add(task)
                await session.commit()
                await session.refresh(task)
                
                logger.info(f"Created automation task {task.id} for profile {profile_id}")
                
                return {
                    "success": True,
                    "task_id": task.id,
                    "message": "Automation task created successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to create automation task: {e}")
            return {"success": False, "error": str(e)}
    
    async def start_automation_task(self, task_id: int) -> Dict[str, Any]:
        """Start an automation task"""
        
        try:
            async with get_async_session_context() as session:
                # Get task with profile
                result = await session.execute(
                    select(AutomationTask)
                    .options(selectinload(AutomationTask.profile))
                    .where(AutomationTask.id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    return {"success": False, "error": "Task not found"}
                
                if task.status == "running":
                    return {"success": False, "error": "Task is already running"}
                
                # Get or create engine for profile
                engine = self._get_or_create_engine(task.profile_id)
                
                # Extract target usernames from target_data
                target_usernames = []
                if task.task_type in ["follow_users", "unfollow_users"]:
                    target_usernames = task.target_data.get("usernames", [])
                elif task.task_type == "follow_competitors_followers":
                    # This would require scraping competitor followers first
                    competitor_username = task.target_data.get("competitor_username")
                    if competitor_username:
                        # For now, we'll use a placeholder
                        target_usernames = [f"follower_{i}" for i in range(10)]
                
                if not target_usernames:
                    return {"success": False, "error": "No target usernames found"}
                
                # Determine action type
                action_type = "follow" if "follow" in task.task_type else "unfollow"
                
                # Start automation
                result = await engine.start_automation(
                    profile_id=task.profile_id,
                    target_usernames=target_usernames,
                    action_type=action_type,
                    custom_config=task.config
                )
                
                if result["success"]:
                    # Update task status
                    task.status = "running"
                    task.session_id = result["session_id"]
                    task.started_at = datetime.now()
                    await session.commit()
                    
                    logger.info(f"Started automation task {task_id}")
                    
                    return {
                        "success": True,
                        "session_id": result["session_id"],
                        "message": "Automation task started successfully"
                    }
                else:
                    return {"success": False, "error": result["error"]}
                
        except Exception as e:
            logger.error(f"Failed to start automation task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def stop_automation_task(self, task_id: int) -> Dict[str, Any]:
        """Stop an automation task"""
        
        try:
            async with get_async_session_context() as session:
                task = await session.get(AutomationTask, task_id)
                if not task:
                    return {"success": False, "error": "Task not found"}
                
                if task.status != "running":
                    return {"success": False, "error": "Task is not running"}
                
                # Get engine and stop session
                engine = self.engines.get(task.profile_id)
                if engine and task.session_id:
                    result = await engine.stop_automation(task.session_id)
                    
                    # Update task status
                    task.status = "stopped"
                    task.completed_at = datetime.now()
                    task.results = result.get("stats", {})
                    await session.commit()
                    
                    logger.info(f"Stopped automation task {task_id}")
                    
                    return {
                        "success": True,
                        "message": "Automation task stopped successfully",
                        "stats": result.get("stats", {})
                    }
                else:
                    return {"success": False, "error": "Engine or session not found"}
                
        except Exception as e:
            logger.error(f"Failed to stop automation task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def pause_automation_task(self, task_id: int) -> Dict[str, Any]:
        """Pause an automation task"""
        
        try:
            async with get_async_session_context() as session:
                task = await session.get(AutomationTask, task_id)
                if not task:
                    return {"success": False, "error": "Task not found"}
                
                if task.status != "running":
                    return {"success": False, "error": "Task is not running"}
                
                # Update task status
                task.status = "paused"
                await session.commit()
                
                logger.info(f"Paused automation task {task_id}")
                
                return {
                    "success": True,
                    "message": "Automation task paused successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to pause automation task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def resume_automation_task(self, task_id: int) -> Dict[str, Any]:
        """Resume a paused automation task"""
        
        try:
            async with get_async_session_context() as session:
                task = await session.get(AutomationTask, task_id)
                if not task:
                    return {"success": False, "error": "Task not found"}
                
                if task.status != "paused":
                    return {"success": False, "error": "Task is not paused"}
                
                # Update task status
                task.status = "running"
                await session.commit()
                
                logger.info(f"Resumed automation task {task_id}")
                
                return {
                    "success": True,
                    "message": "Automation task resumed successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to resume automation task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_automation_tasks(
        self,
        profile_id: Optional[int] = None,
        status: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[AutomationTask]:
        """Get automation tasks with optional filters"""
        
        try:
            async with get_async_session_context() as session:
                query = select(AutomationTask).options(
                    selectinload(AutomationTask.profile)
                )
                
                if profile_id:
                    query = query.where(AutomationTask.profile_id == profile_id)
                
                if status:
                    query = query.where(AutomationTask.status == status)
                
                query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                
                query = query.order_by(AutomationTask.created_at.desc())
                
                result = await session.execute(query)
                tasks = result.scalars().all()
                
                return list(tasks)
                
        except Exception as e:
            logger.error(f"Failed to get automation tasks: {e}")
            return []
    
    async def get_automation_task(self, task_id: int) -> Optional[AutomationTask]:
        """Get a specific automation task"""
        
        try:
            async with get_async_session_context() as session:
                result = await session.execute(
                    select(AutomationTask)
                    .options(selectinload(AutomationTask.profile))
                    .where(AutomationTask.id == task_id)
                )
                return result.scalar_one_or_none()
                
        except Exception as e:
            logger.error(f"Failed to get automation task {task_id}: {e}")
            return None
    
    async def get_task_statistics(self, task_id: int) -> Dict[str, Any]:
        """Get real-time statistics for a running task"""
        
        try:
            task = await self.get_automation_task(task_id)
            if not task:
                return {"error": "Task not found"}
            
            # Get engine stats if task is running
            if task.status == "running" and task.session_id:
                engine = self.engines.get(task.profile_id)
                if engine:
                    return engine.get_session_stats(task.session_id)
            
            # Return stored results for completed tasks
            if task.results:
                return task.results
            
            return {
                "task_id": task_id,
                "status": task.status,
                "follows_completed": 0,
                "unfollows_completed": 0,
                "errors_encountered": 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get task statistics {task_id}: {e}")
            return {"error": str(e)}
    
    async def delete_automation_task(self, task_id: int) -> Dict[str, Any]:
        """Delete an automation task"""
        
        try:
            async with get_async_session_context() as session:
                task = await session.get(AutomationTask, task_id)
                if not task:
                    return {"success": False, "error": "Task not found"}
                
                if task.status == "running":
                    return {"success": False, "error": "Cannot delete running task"}
                
                await session.delete(task)
                await session.commit()
                
                logger.info(f"Deleted automation task {task_id}")
                
                return {
                    "success": True,
                    "message": "Automation task deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to delete automation task {task_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def _get_or_create_engine(self, profile_id: int) -> TikTokAutomationEngine:
        """Get existing engine or create new one for profile"""
        
        if profile_id not in self.engines:
            self.engines[profile_id] = TikTokAutomationEngine(self.default_config)
            logger.info(f"Created new automation engine for profile {profile_id}")
        
        return self.engines[profile_id]
    
    async def get_active_sessions(self) -> List[Dict[str, Any]]:
        """Get all active automation sessions"""
        
        active_sessions = []
        
        for profile_id, engine in self.engines.items():
            sessions = engine.get_all_sessions()
            for session in sessions:
                if session.get("status") == "running":
                    active_sessions.append({
                        "profile_id": profile_id,
                        **session
                    })
        
        return active_sessions
    
    async def stop_all_automation(self) -> Dict[str, Any]:
        """Stop all running automation tasks"""
        
        try:
            stopped_count = 0
            
            for profile_id, engine in self.engines.items():
                sessions = engine.get_all_sessions()
                for session in sessions:
                    if session.get("status") == "running":
                        await engine.stop_automation(session["session_id"])
                        stopped_count += 1
            
            logger.info(f"Stopped {stopped_count} automation sessions")
            
            return {
                "success": True,
                "message": f"Stopped {stopped_count} automation sessions"
            }
            
        except Exception as e:
            logger.error(f"Failed to stop all automation: {e}")
            return {"success": False, "error": str(e)}
    
    async def update_automation_config(
        self,
        profile_id: int,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update automation configuration for a profile"""
        
        try:
            engine = self._get_or_create_engine(profile_id)
            
            # Update engine configuration
            for key, value in config.items():
                if hasattr(engine.config, key):
                    setattr(engine.config, key, value)
            
            logger.info(f"Updated automation config for profile {profile_id}")
            
            return {
                "success": True,
                "message": "Automation configuration updated successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to update automation config: {e}")
            return {"success": False, "error": str(e)}

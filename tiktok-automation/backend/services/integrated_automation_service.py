"""
Integrated Automation Service
Combines all components for optimized TikTok automation with persistent sessions
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from models.browser_profile import BrowserProfile
from models.tiktok_account import TikTokAccount
from models.proxy import Proxy
from core.database import get_async_session
from core.browser_pool import BrowserPool
from services.persistent_session_service import persistent_session_service
from services.cookie_service import CookieService
from services.monitoring_service import monitoring_service
from automation.optimized_follow_workflow import OptimizedFollowWorkflow
from automation.human_behavior import HumanBehavior
from camoufox_integration.browser_manager import BrowserManager


class IntegratedAutomationService:
    """Integrated service for TikTok automation with all optimizations"""
    
    def __init__(self):
        self.browser_pool = BrowserPool(max_instances=10)
        self.cookie_service = CookieService()
        self.follow_workflow = OptimizedFollowWorkflow()
        self.human_behavior = HumanBehavior()
        self.browser_manager = BrowserManager()
        
        # Active sessions
        self.active_sessions: Dict[int, Dict[str, Any]] = {}  # profile_id -> session_info
        
        # Service state
        self.service_running = False
        self.initialization_complete = False
    
    async def initialize(self) -> bool:
        """Initialize the integrated automation service"""
        
        try:
            logger.info("Initializing Integrated Automation Service")
            
            # Start browser pool
            await self.browser_pool.start()
            
            # Start monitoring service
            await monitoring_service.start_monitoring()
            
            # Initialize browser manager
            await self.browser_manager.initialize()
            
            self.initialization_complete = True
            self.service_running = True
            
            logger.info("Integrated Automation Service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Integrated Automation Service: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the service gracefully"""
        
        try:
            logger.info("Shutting down Integrated Automation Service")
            
            self.service_running = False
            
            # Close all active sessions
            await self._close_all_sessions()
            
            # Stop monitoring
            await monitoring_service.stop_monitoring()
            
            # Stop browser pool
            await self.browser_pool.stop()
            
            logger.info("Integrated Automation Service shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during service shutdown: {e}")
    
    async def start_automation_session(
        self,
        profile_id: int,
        account_id: Optional[int] = None,
        automation_type: str = "follow",
        settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Start automation session for a profile"""
        
        try:
            if not self.initialization_complete:
                return {"success": False, "error": "Service not initialized"}
            
            logger.info(f"Starting automation session for profile {profile_id}")
            
            # Get profile and account
            async with get_async_session() as session:
                profile = await session.get(BrowserProfile, profile_id)
                if not profile:
                    return {"success": False, "error": "Profile not found"}
                
                account = None
                if account_id:
                    account = await session.get(TikTokAccount, account_id)
                    if not account:
                        return {"success": False, "error": "Account not found"}
            
            # Check if session already active
            if profile_id in self.active_sessions:
                return {"success": False, "error": "Session already active for this profile"}
            
            # Create persistent browser session
            browser, context, instance_id = await self.browser_pool.get_persistent_browser(
                profile=profile,
                account=account,
                reuse=True
            )
            
            # Load or restore session state
            if account:
                await self.cookie_service.restore_to_persistent_session(profile_id, context)
            
            # Create session info
            session_info = {
                "profile_id": profile_id,
                "account_id": account_id,
                "instance_id": instance_id,
                "browser": browser,
                "context": context,
                "automation_type": automation_type,
                "settings": settings or {},
                "start_time": datetime.utcnow(),
                "status": "active"
            }
            
            self.active_sessions[profile_id] = session_info
            
            # Update profile status
            async with get_async_session() as session:
                profile = await session.get(BrowserProfile, profile_id)
                if profile:
                    profile.status = "running"
                    profile.current_action = f"Đang chạy {automation_type}"
                    await session.commit()
            
            logger.info(f"Automation session started for profile {profile_id}")
            
            return {
                "success": True,
                "profile_id": profile_id,
                "instance_id": instance_id,
                "session_info": {
                    "automation_type": automation_type,
                    "start_time": session_info["start_time"].isoformat(),
                    "status": session_info["status"]
                }
            }
            
        except Exception as e:
            logger.error(f"Error starting automation session for profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_follow_automation(
        self,
        profile_id: int,
        target_usernames: List[str],
        pattern: str = "moderate",
        mobile_mode: bool = False
    ) -> Dict[str, Any]:
        """Execute follow automation for a list of users"""
        
        try:
            if profile_id not in self.active_sessions:
                return {"success": False, "error": "No active session for this profile"}
            
            session_info = self.active_sessions[profile_id]
            context = session_info["context"]
            
            logger.info(f"Starting follow automation for {len(target_usernames)} users")
            
            # Create new page for automation
            page = await context.new_page()
            
            # Apply mobile mode if requested
            if mobile_mode:
                await self._configure_mobile_mode(page)
            
            results = []
            
            for username in target_usernames:
                try:
                    # Check for detection before each follow
                    detection_event = await monitoring_service.check_for_detection(
                        page, profile_id, session_info.get("account_id")
                    )
                    
                    if detection_event:
                        logger.warning(f"Detection event found, stopping automation: {detection_event.detection_type}")
                        break
                    
                    # Record start time for performance monitoring
                    start_time = datetime.utcnow()
                    
                    # Execute follow sequence
                    follow_result = await self.follow_workflow.execute_follow_sequence(
                        page=page,
                        target_username=username,
                        pattern=pattern,
                        mobile_mode=mobile_mode
                    )
                    
                    # Record performance metric
                    duration = (datetime.utcnow() - start_time).total_seconds()
                    await monitoring_service.record_performance_metric(
                        profile_id=profile_id,
                        action_type="follow",
                        duration=duration,
                        success=follow_result["success"],
                        error_message=follow_result.get("error")
                    )
                    
                    results.append({
                        "username": username,
                        "result": follow_result
                    })
                    
                    # Sync cookies after successful follow
                    if follow_result["success"] and session_info.get("account_id"):
                        await self.browser_pool.sync_persistent_session_cookies(
                            session_info["instance_id"],
                            session_info["account_id"]
                        )
                    
                    # Wait for next interval if specified
                    if "next_interval" in follow_result:
                        await asyncio.sleep(follow_result["next_interval"])
                    
                except Exception as e:
                    logger.error(f"Error following user {username}: {e}")
                    results.append({
                        "username": username,
                        "result": {"success": False, "error": str(e)}
                    })
            
            # Close the page
            await page.close()
            
            # Calculate summary
            successful_follows = sum(1 for r in results if r["result"]["success"])
            
            logger.info(f"Follow automation completed: {successful_follows}/{len(target_usernames)} successful")
            
            return {
                "success": True,
                "total_users": len(target_usernames),
                "successful_follows": successful_follows,
                "results": results,
                "pattern": pattern,
                "mobile_mode": mobile_mode
            }
            
        except Exception as e:
            logger.error(f"Error in follow automation for profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def stop_automation_session(self, profile_id: int, save_state: bool = True) -> Dict[str, Any]:
        """Stop automation session for a profile"""
        
        try:
            if profile_id not in self.active_sessions:
                return {"success": False, "error": "No active session for this profile"}
            
            session_info = self.active_sessions[profile_id]
            
            logger.info(f"Stopping automation session for profile {profile_id}")
            
            # Save session state if requested
            if save_state:
                await self.browser_pool.save_persistent_session_state(session_info["instance_id"])
                
                # Sync cookies one final time
                if session_info.get("account_id"):
                    await self.browser_pool.sync_persistent_session_cookies(
                        session_info["instance_id"],
                        session_info["account_id"]
                    )
            
            # Release browser instance
            await self.browser_pool.release_browser(session_info["instance_id"])
            
            # Remove from active sessions
            del self.active_sessions[profile_id]
            
            # Update profile status
            async with get_async_session() as session:
                profile = await session.get(BrowserProfile, profile_id)
                if profile:
                    profile.status = "inactive"
                    profile.current_action = "Đã dừng"
                    await session.commit()
            
            logger.info(f"Automation session stopped for profile {profile_id}")
            
            return {"success": True, "profile_id": profile_id}
            
        except Exception as e:
            logger.error(f"Error stopping automation session for profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_session_status(self, profile_id: int) -> Dict[str, Any]:
        """Get status of automation session"""
        
        try:
            if profile_id not in self.active_sessions:
                return {"active": False, "profile_id": profile_id}
            
            session_info = self.active_sessions[profile_id]
            
            # Get health status from monitoring
            health_status = await monitoring_service.get_profile_health_status(profile_id)
            
            # Calculate session duration
            duration = (datetime.utcnow() - session_info["start_time"]).total_seconds()
            
            return {
                "active": True,
                "profile_id": profile_id,
                "automation_type": session_info["automation_type"],
                "status": session_info["status"],
                "duration": duration,
                "start_time": session_info["start_time"].isoformat(),
                "health_status": health_status,
                "instance_id": session_info["instance_id"]
            }
            
        except Exception as e:
            logger.error(f"Error getting session status for profile {profile_id}: {e}")
            return {"active": False, "error": str(e)}
    
    async def get_service_overview(self) -> Dict[str, Any]:
        """Get overview of the automation service"""
        
        try:
            # Get system health from monitoring
            system_health = await monitoring_service.get_system_health_overview()
            
            # Get browser pool statistics
            pool_stats = self.browser_pool.get_statistics()
            
            # Get active sessions info
            active_sessions_info = []
            for profile_id, session_info in self.active_sessions.items():
                duration = (datetime.utcnow() - session_info["start_time"]).total_seconds()
                active_sessions_info.append({
                    "profile_id": profile_id,
                    "automation_type": session_info["automation_type"],
                    "duration": duration,
                    "status": session_info["status"]
                })
            
            return {
                "service_running": self.service_running,
                "initialization_complete": self.initialization_complete,
                "active_sessions": len(self.active_sessions),
                "active_sessions_info": active_sessions_info,
                "system_health": system_health,
                "browser_pool_stats": pool_stats
            }
            
        except Exception as e:
            logger.error(f"Error getting service overview: {e}")
            return {"error": str(e)}
    
    async def _configure_mobile_mode(self, page):
        """Configure page for mobile mode"""
        
        try:
            # Set mobile viewport
            await page.set_viewport_size({"width": 390, "height": 844})
            
            # Add mobile user agent
            await page.set_extra_http_headers({
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
            })
            
            # Enable touch events
            await page.add_init_script("""
                Object.defineProperty(navigator, 'maxTouchPoints', {
                    get: () => 5
                });
            """)
            
        except Exception as e:
            logger.error(f"Error configuring mobile mode: {e}")
    
    async def _close_all_sessions(self):
        """Close all active sessions"""
        
        try:
            logger.info(f"Closing {len(self.active_sessions)} active sessions")
            
            for profile_id in list(self.active_sessions.keys()):
                await self.stop_automation_session(profile_id, save_state=True)
            
            logger.info("All sessions closed")
            
        except Exception as e:
            logger.error(f"Error closing all sessions: {e}")


# Global service instance
integrated_automation_service = IntegratedAutomationService()

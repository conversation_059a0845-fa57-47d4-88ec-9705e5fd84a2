"""
Enhanced Cookie Service for managing TikTok cookies with persistent session integration
"""

import json
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from loguru import logger

from models.tiktok_account import Tik<PERSON>ok<PERSON>ccount
from models.browser_profile import BrowserProfile
from core.database import get_async_session
from core.encryption import encrypt_cookies, decrypt_cookies, encrypt_data, decrypt_data
from core.config import settings


class CookieService:
    """Enhanced service for managing TikTok cookies with persistent session integration"""

    def __init__(self):
        self.cookie_storage_path = settings.COOKIES_DIR
        self.cookie_storage_path.mkdir(parents=True, exist_ok=True)

        # Storage state directory for persistent sessions
        self.storage_state_path = settings.DATA_DIR / "storage_states"
        self.storage_state_path.mkdir(parents=True, exist_ok=True)
    
    async def save_cookies(
        self,
        account_id: int,
        cookies: List[Dict[str, Any]],
        session_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Save encrypted cookies for TikTok account"""
        
        async with get_async_session() as session:
            try:
                # Get account
                account = await session.get(TikTokAccount, account_id)
                if not account:
                    logger.error(f"Account {account_id} not found")
                    return False
                
                # Filter and validate cookies
                valid_cookies = self._filter_valid_cookies(cookies)
                
                if not valid_cookies:
                    logger.warning(f"No valid cookies to save for account {account_id}")
                    return False
                
                # Encrypt cookies
                encrypted_cookies = encrypt_cookies(valid_cookies)
                
                # Update account with encrypted cookies
                account.cookies_data = encrypted_cookies
                account.session_data = session_data or {}
                account.is_logged_in = True
                account.last_login = datetime.utcnow()
                account.login_count += 1
                
                # Save to database
                await session.commit()
                
                # Also save to file as backup
                await self._save_cookies_to_file(account_id, valid_cookies)
                
                logger.info(f"Saved {len(valid_cookies)} cookies for account {account.username}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error saving cookies for account {account_id}: {e}")
                return False
    
    async def load_cookies(self, account_id: int) -> Optional[List[Dict[str, Any]]]:
        """Load and decrypt cookies for TikTok account"""
        
        async with get_async_session() as session:
            try:
                # Get account
                account = await session.get(TikTokAccount, account_id)
                if not account or not account.cookies_data:
                    logger.warning(f"No cookies found for account {account_id}")
                    return None
                
                # Decrypt cookies
                cookies = decrypt_cookies(account.cookies_data)
                
                # Validate cookies are not expired
                valid_cookies = self._filter_expired_cookies(cookies)
                
                if len(valid_cookies) != len(cookies):
                    logger.info(f"Filtered {len(cookies) - len(valid_cookies)} expired cookies")
                    
                    # Update account with filtered cookies
                    if valid_cookies:
                        account.cookies_data = encrypt_cookies(valid_cookies)
                        await session.commit()
                    else:
                        # No valid cookies left
                        account.cookies_data = None
                        account.is_logged_in = False
                        await session.commit()
                        return None
                
                logger.info(f"Loaded {len(valid_cookies)} cookies for account {account.username}")
                return valid_cookies
                
            except Exception as e:
                logger.error(f"Error loading cookies for account {account_id}: {e}")
                return None
    
    async def delete_cookies(self, account_id: int) -> bool:
        """Delete cookies for TikTok account"""
        
        async with get_async_session() as session:
            try:
                # Get account
                account = await session.get(TikTokAccount, account_id)
                if not account:
                    return False
                
                # Clear cookies from database
                account.cookies_data = None
                account.session_data = None
                account.is_logged_in = False
                
                await session.commit()
                
                # Delete cookie file
                await self._delete_cookies_file(account_id)
                
                logger.info(f"Deleted cookies for account {account.username}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error deleting cookies for account {account_id}: {e}")
                return False
    
    async def validate_cookies(self, account_id: int) -> Dict[str, Any]:
        """Validate cookies by testing with browser"""
        
        try:
            cookies = await self.load_cookies(account_id)
            if not cookies:
                return {"valid": False, "error": "No cookies found"}
            
            # Get account and profile
            async with get_async_session() as session:
                account = await session.get(TikTokAccount, account_id)
                if not account:
                    return {"valid": False, "error": "Account not found"}
                
                profile = None
                if account.browser_profile_id:
                    profile = await session.get(BrowserProfile, account.browser_profile_id)
            
            # Test cookies with browser
            from camoufox_integration.browser_manager import BrowserManager
            browser_manager = BrowserManager()
            
            # Create test profile if none exists
            if not profile:
                profile = BrowserProfile(
                    name=f"temp_profile_{account_id}",
                    fingerprint_config={}
                )
            
            # Launch browser
            browser = await browser_manager.create_browser_instance(
                profile=profile,
                headless=True
            )
            
            context = await browser_manager.create_browser_context(browser, profile)
            
            # Add cookies to context
            await context.add_cookies(cookies)
            
            # Test by navigating to TikTok
            page = await context.new_page()
            response = await page.goto("https://www.tiktok.com/", timeout=30000)
            
            # Check if logged in by looking for user indicators
            is_logged_in = await self._check_login_status(page)
            
            # Cleanup
            await page.close()
            await browser_manager.close_context(context)
            await browser_manager.close_browser(browser)
            
            # Update account login status
            async with get_async_session() as session:
                account = await session.get(TikTokAccount, account_id)
                if account:
                    account.is_logged_in = is_logged_in
                    await session.commit()
            
            return {
                "valid": is_logged_in,
                "status_code": response.status,
                "cookie_count": len(cookies),
                "message": "Cookies are valid" if is_logged_in else "Cookies are invalid or expired"
            }
            
        except Exception as e:
            logger.error(f"Error validating cookies for account {account_id}: {e}")
            return {"valid": False, "error": str(e)}
    
    async def refresh_cookies(self, account_id: int) -> Dict[str, Any]:
        """Refresh cookies by re-logging into TikTok"""
        
        try:
            # This would involve launching browser and going through login flow
            # For now, return placeholder
            return {
                "success": False,
                "message": "Cookie refresh not implemented yet"
            }
            
        except Exception as e:
            logger.error(f"Error refreshing cookies for account {account_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def export_cookies(self, account_id: int, format: str = "json") -> Optional[str]:
        """Export cookies in specified format"""
        
        try:
            cookies = await self.load_cookies(account_id)
            if not cookies:
                return None
            
            if format.lower() == "json":
                return json.dumps(cookies, indent=2)
            elif format.lower() == "netscape":
                return self._convert_to_netscape_format(cookies)
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting cookies for account {account_id}: {e}")
            return None
    
    async def import_cookies(
        self,
        account_id: int,
        cookies_data: str,
        format: str = "json"
    ) -> bool:
        """Import cookies from string data"""
        
        try:
            if format.lower() == "json":
                cookies = json.loads(cookies_data)
            elif format.lower() == "netscape":
                cookies = self._parse_netscape_format(cookies_data)
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            return await self.save_cookies(account_id, cookies)
            
        except Exception as e:
            logger.error(f"Error importing cookies for account {account_id}: {e}")
            return False
    
    async def cleanup_expired_cookies(self) -> Dict[str, Any]:
        """Cleanup expired cookies from all accounts"""
        
        results = {
            "accounts_processed": 0,
            "cookies_removed": 0,
            "accounts_logged_out": 0
        }
        
        try:
            async with get_async_session() as session:
                # Get all accounts with cookies
                result = await session.execute(
                    select(TikTokAccount).where(TikTokAccount.cookies_data.isnot(None))
                )
                accounts = result.scalars().all()
                
                for account in accounts:
                    try:
                        results["accounts_processed"] += 1
                        
                        # Decrypt and filter cookies
                        cookies = decrypt_cookies(account.cookies_data)
                        original_count = len(cookies)
                        valid_cookies = self._filter_expired_cookies(cookies)
                        
                        removed_count = original_count - len(valid_cookies)
                        results["cookies_removed"] += removed_count
                        
                        if valid_cookies:
                            # Update with valid cookies
                            account.cookies_data = encrypt_cookies(valid_cookies)
                        else:
                            # No valid cookies left
                            account.cookies_data = None
                            account.is_logged_in = False
                            results["accounts_logged_out"] += 1
                        
                    except Exception as e:
                        logger.error(f"Error processing account {account.id}: {e}")
                
                await session.commit()
                
            logger.info(f"Cookie cleanup completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error during cookie cleanup: {e}")
            return results
    
    def _filter_valid_cookies(self, cookies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out invalid cookies"""
        
        valid_cookies = []
        required_fields = ["name", "value", "domain"]
        
        for cookie in cookies:
            # Check required fields
            if not all(field in cookie for field in required_fields):
                continue
            
            # Check if cookie is for TikTok domain
            domain = cookie.get("domain", "")
            if not any(tiktok_domain in domain for tiktok_domain in [".tiktok.com", "tiktok.com"]):
                continue
            
            # Check if cookie has value
            if not cookie.get("value"):
                continue
            
            valid_cookies.append(cookie)
        
        return valid_cookies
    
    def _filter_expired_cookies(self, cookies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out expired cookies"""
        
        valid_cookies = []
        current_time = datetime.utcnow().timestamp()
        
        for cookie in cookies:
            # Check expiration
            expires = cookie.get("expires")
            if expires and expires < current_time:
                continue
            
            valid_cookies.append(cookie)
        
        return valid_cookies
    
    async def _save_cookies_to_file(self, account_id: int, cookies: List[Dict[str, Any]]):
        """Save cookies to file as backup"""
        
        try:
            file_path = self.cookie_storage_path / f"account_{account_id}_cookies.json"
            
            # Encrypt cookies for file storage
            encrypted_data = encrypt_cookies(cookies)
            
            with open(file_path, "w") as f:
                json.dump({
                    "account_id": account_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "encrypted_cookies": encrypted_data
                }, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving cookies to file: {e}")
    
    async def _delete_cookies_file(self, account_id: int):
        """Delete cookies file"""
        
        try:
            file_path = self.cookie_storage_path / f"account_{account_id}_cookies.json"
            if file_path.exists():
                file_path.unlink()
        except Exception as e:
            logger.error(f"Error deleting cookies file: {e}")
    
    async def _check_login_status(self, page) -> bool:
        """Check if user is logged in on TikTok page"""
        
        try:
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            # Check for login indicators
            login_indicators = [
                '[data-e2e="profile-icon"]',  # Profile icon
                '[data-e2e="nav-profile"]',   # Profile nav
                '.avatar',                    # Avatar element
                '[href*="/profile"]'          # Profile link
            ]
            
            for selector in login_indicators:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False
    
    def _convert_to_netscape_format(self, cookies: List[Dict[str, Any]]) -> str:
        """Convert cookies to Netscape format"""
        
        lines = ["# Netscape HTTP Cookie File"]
        
        for cookie in cookies:
            domain = cookie.get("domain", "")
            flag = "TRUE" if domain.startswith(".") else "FALSE"
            path = cookie.get("path", "/")
            secure = "TRUE" if cookie.get("secure", False) else "FALSE"
            expires = str(int(cookie.get("expires", 0)))
            name = cookie.get("name", "")
            value = cookie.get("value", "")
            
            line = f"{domain}\t{flag}\t{path}\t{secure}\t{expires}\t{name}\t{value}"
            lines.append(line)
        
        return "\n".join(lines)
    
    def _parse_netscape_format(self, data: str) -> List[Dict[str, Any]]:
        """Parse Netscape format cookies"""
        
        cookies = []
        lines = data.strip().split("\n")
        
        for line in lines:
            if line.startswith("#") or not line.strip():
                continue
            
            parts = line.split("\t")
            if len(parts) != 7:
                continue
            
            domain, flag, path, secure, expires, name, value = parts
            
            cookie = {
                "name": name,
                "value": value,
                "domain": domain,
                "path": path,
                "secure": secure.upper() == "TRUE",
                "expires": int(expires) if expires.isdigit() else None
            }
            
            cookies.append(cookie)
        
        return cookies

    async def save_storage_state(
        self,
        profile_id: int,
        storage_state: Dict[str, Any],
        account_id: Optional[int] = None
    ) -> bool:
        """Save storage state from persistent session"""

        try:
            # Save to file with encryption
            storage_state_file = self.storage_state_path / f"profile_{profile_id}_state.json"

            encrypted_state = encrypt_data(json.dumps(storage_state))

            with open(storage_state_file, 'wb') as f:
                f.write(encrypted_state)

            # Also save cookies to database if account_id provided
            if account_id and 'cookies' in storage_state:
                cookies = storage_state['cookies']
                await self.save_cookies(account_id, cookies)

            logger.info(f"Storage state saved for profile {profile_id}")
            return True

        except Exception as e:
            logger.error(f"Error saving storage state for profile {profile_id}: {e}")
            return False

    async def load_storage_state(self, profile_id: int) -> Optional[Dict[str, Any]]:
        """Load storage state for persistent session"""

        try:
            storage_state_file = self.storage_state_path / f"profile_{profile_id}_state.json"

            if not storage_state_file.exists():
                logger.info(f"No storage state found for profile {profile_id}")
                return None

            # Load and decrypt storage state
            with open(storage_state_file, 'rb') as f:
                encrypted_state = f.read()

            decrypted_state = decrypt_data(encrypted_state)
            storage_state = json.loads(decrypted_state)

            # Validate storage state structure
            if not isinstance(storage_state, dict):
                logger.error(f"Invalid storage state format for profile {profile_id}")
                return None

            # Filter expired cookies
            if 'cookies' in storage_state and isinstance(storage_state['cookies'], list):
                valid_cookies = self._filter_expired_cookies(storage_state['cookies'])
                storage_state['cookies'] = valid_cookies

                if len(valid_cookies) != len(storage_state['cookies']):
                    logger.info(f"Filtered expired cookies for profile {profile_id}")
                    # Save updated state
                    await self.save_storage_state(profile_id, storage_state)

            logger.info(f"Storage state loaded for profile {profile_id}")
            return storage_state

        except Exception as e:
            logger.error(f"Error loading storage state for profile {profile_id}: {e}")
            return None

    async def sync_persistent_session_cookies(
        self,
        profile_id: int,
        account_id: int,
        persistent_context
    ) -> bool:
        """Sync cookies from persistent session to database"""

        try:
            # Get storage state from persistent context
            storage_state = await persistent_context.storage_state()

            # Save storage state
            await self.save_storage_state(profile_id, storage_state, account_id)

            # Extract and save cookies specifically
            if 'cookies' in storage_state:
                cookies = storage_state['cookies']
                await self.save_cookies(account_id, cookies)

                logger.info(f"Synced {len(cookies)} cookies from persistent session for account {account_id}")

            return True

        except Exception as e:
            logger.error(f"Error syncing persistent session cookies for profile {profile_id}: {e}")
            return False

    async def restore_to_persistent_session(
        self,
        profile_id: int,
        persistent_context
    ) -> bool:
        """Restore storage state to persistent session (if needed)"""

        try:
            # For persistent contexts, storage is automatically handled
            # But we can validate that the data is consistent

            storage_state = await self.load_storage_state(profile_id)
            if not storage_state:
                logger.info(f"No storage state to restore for profile {profile_id}")
                return True

            # Get current storage state from context
            current_state = await persistent_context.storage_state()

            # Compare cookie counts as a basic validation
            stored_cookies = len(storage_state.get('cookies', []))
            current_cookies = len(current_state.get('cookies', []))

            logger.info(f"Storage state validation for profile {profile_id}: "
                       f"stored={stored_cookies}, current={current_cookies}")

            return True

        except Exception as e:
            logger.error(f"Error restoring storage state to persistent session for profile {profile_id}: {e}")
            return False

    async def cleanup_storage_state(self, profile_id: int) -> bool:
        """Clean up storage state files for profile"""

        try:
            storage_state_file = self.storage_state_path / f"profile_{profile_id}_state.json"

            if storage_state_file.exists():
                storage_state_file.unlink()
                logger.info(f"Storage state cleaned up for profile {profile_id}")

            return True

        except Exception as e:
            logger.error(f"Error cleaning up storage state for profile {profile_id}: {e}")
            return False

    async def validate_persistent_session_cookies(
        self,
        profile_id: int,
        persistent_context
    ) -> Dict[str, Any]:
        """Validate cookies in persistent session"""

        try:
            # Get storage state from persistent context
            storage_state = await persistent_context.storage_state()

            if 'cookies' not in storage_state:
                return {"valid": False, "error": "No cookies found in storage state"}

            cookies = storage_state['cookies']

            # Filter expired cookies
            valid_cookies = self._filter_expired_cookies(cookies)

            # Check if we have TikTok-specific cookies
            tiktok_cookies = [c for c in valid_cookies if 'tiktok.com' in c.get('domain', '')]

            # Basic validation - check for essential TikTok cookies
            essential_cookies = ['sessionid', 'sid_tt', 'uid_tt']
            has_essential = any(
                any(cookie.get('name') == essential for essential in essential_cookies)
                for cookie in tiktok_cookies
            )

            return {
                "valid": len(valid_cookies) > 0 and has_essential,
                "total_cookies": len(cookies),
                "valid_cookies": len(valid_cookies),
                "tiktok_cookies": len(tiktok_cookies),
                "has_essential": has_essential,
                "expired_count": len(cookies) - len(valid_cookies)
            }

        except Exception as e:
            logger.error(f"Error validating persistent session cookies for profile {profile_id}: {e}")
            return {"valid": False, "error": str(e)}

"""
Monitoring & Recovery Service for TikTok Automation
Monitors for detection, handles recovery, and tracks performance
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger
from dataclasses import dataclass, asdict

from core.config import settings
from core.database import get_async_session
from models.browser_profile import BrowserProfile
from models.tiktok_account import TikTokAccount


@dataclass
class DetectionEvent:
    """Represents a detection event"""
    timestamp: datetime
    profile_id: int
    account_id: Optional[int]
    detection_type: str  # captcha, rate_limit, account_blocked, etc.
    severity: str  # low, medium, high, critical
    details: Dict[str, Any]
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class PerformanceMetrics:
    """Performance metrics for monitoring"""
    timestamp: datetime
    profile_id: int
    action_type: str  # follow, login, navigation, etc.
    duration: float
    success: bool
    error_message: Optional[str] = None
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None


class MonitoringService:
    """Service for monitoring automation and handling recovery"""
    
    def __init__(self):
        self.detection_events: List[DetectionEvent] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Detection patterns
        self.detection_patterns = {
            "captcha": [
                "captcha",
                "verify you're human",
                "security check",
                "robot verification",
                "please verify",
                "suspicious activity"
            ],
            "rate_limit": [
                "too many requests",
                "rate limit",
                "slow down",
                "try again later",
                "temporarily blocked"
            ],
            "account_blocked": [
                "account suspended",
                "account banned",
                "account restricted",
                "violation of terms",
                "community guidelines"
            ],
            "login_required": [
                "please log in",
                "sign in required",
                "login to continue",
                "authentication required"
            ]
        }
        
        # Recovery strategies
        self.recovery_strategies = {
            "captcha": self._handle_captcha_detection,
            "rate_limit": self._handle_rate_limit,
            "account_blocked": self._handle_account_blocked,
            "login_required": self._handle_login_required,
            "session_expired": self._handle_session_expired
        }
        
        # Performance thresholds
        self.performance_thresholds = {
            "follow_duration": 30.0,  # seconds
            "login_duration": 60.0,
            "navigation_duration": 15.0,
            "memory_usage": 500.0,  # MB
            "cpu_usage": 80.0  # percentage
        }
        
        # Ensure monitoring directory exists
        self.monitoring_dir = settings.DATA_DIR / "monitoring"
        self.monitoring_dir.mkdir(parents=True, exist_ok=True)
    
    async def start_monitoring(self):
        """Start monitoring service"""
        
        if self.monitoring_active:
            logger.warning("Monitoring service already active")
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Monitoring service started")
    
    async def stop_monitoring(self):
        """Stop monitoring service"""
        
        self.monitoring_active = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        # Save monitoring data
        await self._save_monitoring_data()
        logger.info("Monitoring service stopped")
    
    async def check_for_detection(
        self,
        page,
        profile_id: int,
        account_id: Optional[int] = None
    ) -> Optional[DetectionEvent]:
        """Check page for detection indicators"""
        
        try:
            page_content = await page.content()
            page_url = page.url
            
            # Check for detection patterns
            for detection_type, patterns in self.detection_patterns.items():
                for pattern in patterns:
                    if pattern.lower() in page_content.lower():
                        # Create detection event
                        event = DetectionEvent(
                            timestamp=datetime.utcnow(),
                            profile_id=profile_id,
                            account_id=account_id,
                            detection_type=detection_type,
                            severity=self._get_detection_severity(detection_type),
                            details={
                                "pattern": pattern,
                                "url": page_url,
                                "page_title": await page.title()
                            }
                        )
                        
                        self.detection_events.append(event)
                        logger.warning(f"Detection event: {detection_type} for profile {profile_id}")
                        
                        # Trigger recovery
                        await self._trigger_recovery(event, page)
                        
                        return event
            
            # Check for specific elements that indicate detection
            detection_selectors = [
                "[data-testid='captcha']",
                ".captcha-container",
                "[aria-label*='captcha']",
                ".rate-limit-message",
                ".blocked-message"
            ]
            
            for selector in detection_selectors:
                element = await page.query_selector(selector)
                if element:
                    event = DetectionEvent(
                        timestamp=datetime.utcnow(),
                        profile_id=profile_id,
                        account_id=account_id,
                        detection_type="element_detection",
                        severity="medium",
                        details={
                            "selector": selector,
                            "url": page_url,
                            "element_text": await element.inner_text() if element else ""
                        }
                    )
                    
                    self.detection_events.append(event)
                    logger.warning(f"Element detection for profile {profile_id}: {selector}")
                    
                    return event
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking for detection: {e}")
            return None
    
    async def record_performance_metric(
        self,
        profile_id: int,
        action_type: str,
        duration: float,
        success: bool,
        error_message: Optional[str] = None,
        memory_usage: Optional[float] = None,
        cpu_usage: Optional[float] = None
    ):
        """Record performance metric"""
        
        metric = PerformanceMetrics(
            timestamp=datetime.utcnow(),
            profile_id=profile_id,
            action_type=action_type,
            duration=duration,
            success=success,
            error_message=error_message,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage
        )
        
        self.performance_metrics.append(metric)
        
        # Check for performance issues
        await self._check_performance_thresholds(metric)
    
    async def get_profile_health_status(self, profile_id: int) -> Dict[str, Any]:
        """Get health status for a profile"""
        
        try:
            # Get recent detection events
            recent_detections = [
                event for event in self.detection_events
                if event.profile_id == profile_id and 
                event.timestamp > datetime.utcnow() - timedelta(hours=24)
            ]
            
            # Get recent performance metrics
            recent_metrics = [
                metric for metric in self.performance_metrics
                if metric.profile_id == profile_id and
                metric.timestamp > datetime.utcnow() - timedelta(hours=24)
            ]
            
            # Calculate success rate
            if recent_metrics:
                success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics)
            else:
                success_rate = 1.0
            
            # Calculate average performance
            avg_duration = 0.0
            if recent_metrics:
                avg_duration = sum(m.duration for m in recent_metrics) / len(recent_metrics)
            
            # Determine health status
            health_status = "healthy"
            if len(recent_detections) > 5:
                health_status = "critical"
            elif len(recent_detections) > 2:
                health_status = "warning"
            elif success_rate < 0.8:
                health_status = "warning"
            
            return {
                "profile_id": profile_id,
                "health_status": health_status,
                "success_rate": success_rate,
                "avg_duration": avg_duration,
                "recent_detections": len(recent_detections),
                "recent_metrics": len(recent_metrics),
                "last_detection": recent_detections[-1].timestamp.isoformat() if recent_detections else None,
                "detection_types": list(set(event.detection_type for event in recent_detections))
            }
            
        except Exception as e:
            logger.error(f"Error getting health status for profile {profile_id}: {e}")
            return {
                "profile_id": profile_id,
                "health_status": "unknown",
                "error": str(e)
            }
    
    async def get_system_health_overview(self) -> Dict[str, Any]:
        """Get overall system health overview"""
        
        try:
            # Get all unique profile IDs
            profile_ids = set()
            profile_ids.update(event.profile_id for event in self.detection_events)
            profile_ids.update(metric.profile_id for metric in self.performance_metrics)
            
            # Get health status for each profile
            profile_health = {}
            for profile_id in profile_ids:
                profile_health[profile_id] = await self.get_profile_health_status(profile_id)
            
            # Calculate system-wide metrics
            total_detections = len(self.detection_events)
            recent_detections = len([
                event for event in self.detection_events
                if event.timestamp > datetime.utcnow() - timedelta(hours=24)
            ])
            
            total_metrics = len(self.performance_metrics)
            recent_metrics = [
                metric for metric in self.performance_metrics
                if metric.timestamp > datetime.utcnow() - timedelta(hours=24)
            ]
            
            system_success_rate = 1.0
            if recent_metrics:
                system_success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics)
            
            # Determine system health
            critical_profiles = sum(1 for h in profile_health.values() if h["health_status"] == "critical")
            warning_profiles = sum(1 for h in profile_health.values() if h["health_status"] == "warning")
            
            system_health = "healthy"
            if critical_profiles > 0:
                system_health = "critical"
            elif warning_profiles > len(profile_ids) * 0.3:  # More than 30% warning
                system_health = "warning"
            
            return {
                "system_health": system_health,
                "total_profiles": len(profile_ids),
                "critical_profiles": critical_profiles,
                "warning_profiles": warning_profiles,
                "system_success_rate": system_success_rate,
                "total_detections": total_detections,
                "recent_detections": recent_detections,
                "total_metrics": total_metrics,
                "recent_metrics": len(recent_metrics),
                "monitoring_active": self.monitoring_active,
                "profile_health": profile_health
            }
            
        except Exception as e:
            logger.error(f"Error getting system health overview: {e}")
            return {
                "system_health": "unknown",
                "error": str(e)
            }
    
    def _get_detection_severity(self, detection_type: str) -> str:
        """Get severity level for detection type"""
        
        severity_map = {
            "captcha": "high",
            "rate_limit": "medium",
            "account_blocked": "critical",
            "login_required": "medium",
            "session_expired": "low"
        }
        
        return severity_map.get(detection_type, "medium")
    
    async def _trigger_recovery(self, event: DetectionEvent, page):
        """Trigger recovery strategy for detection event"""
        
        try:
            recovery_func = self.recovery_strategies.get(event.detection_type)
            if recovery_func:
                logger.info(f"Triggering recovery for {event.detection_type}")
                success = await recovery_func(event, page)
                
                if success:
                    event.resolved = True
                    event.resolution_time = datetime.utcnow()
                    logger.info(f"Recovery successful for {event.detection_type}")
                else:
                    logger.warning(f"Recovery failed for {event.detection_type}")
            else:
                logger.warning(f"No recovery strategy for {event.detection_type}")
                
        except Exception as e:
            logger.error(f"Error in recovery for {event.detection_type}: {e}")
    
    async def _handle_captcha_detection(self, event: DetectionEvent, page) -> bool:
        """Handle CAPTCHA detection"""
        
        try:
            logger.warning(f"CAPTCHA detected for profile {event.profile_id}")
            
            # Pause automation for this profile
            await self._pause_profile_automation(event.profile_id)
            
            # Take screenshot for manual review
            screenshot_path = self.monitoring_dir / f"captcha_{event.profile_id}_{int(time.time())}.png"
            await page.screenshot(path=str(screenshot_path))
            
            # Log details for manual intervention
            logger.critical(f"CAPTCHA requires manual intervention. Screenshot: {screenshot_path}")
            
            return False  # Manual intervention required
            
        except Exception as e:
            logger.error(f"Error handling CAPTCHA: {e}")
            return False
    
    async def _handle_rate_limit(self, event: DetectionEvent, page) -> bool:
        """Handle rate limit detection"""
        
        try:
            logger.warning(f"Rate limit detected for profile {event.profile_id}")
            
            # Implement exponential backoff
            backoff_time = 300  # 5 minutes base
            await asyncio.sleep(backoff_time)
            
            # Refresh page
            await page.reload()
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling rate limit: {e}")
            return False
    
    async def _handle_account_blocked(self, event: DetectionEvent, page) -> bool:
        """Handle account blocked detection"""
        
        try:
            logger.critical(f"Account blocked for profile {event.profile_id}")
            
            # Disable profile automation
            await self._disable_profile_automation(event.profile_id)
            
            return False  # Manual intervention required
            
        except Exception as e:
            logger.error(f"Error handling account blocked: {e}")
            return False
    
    async def _handle_login_required(self, event: DetectionEvent, page) -> bool:
        """Handle login required detection"""
        
        try:
            logger.warning(f"Login required for profile {event.profile_id}")
            
            # Trigger re-login process
            # This would integrate with the login service
            
            return False  # Manual intervention required for now
            
        except Exception as e:
            logger.error(f"Error handling login required: {e}")
            return False
    
    async def _handle_session_expired(self, event: DetectionEvent, page) -> bool:
        """Handle session expired detection"""
        
        try:
            logger.info(f"Session expired for profile {event.profile_id}")
            
            # Refresh session
            await page.reload()
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling session expired: {e}")
            return False
    
    async def _check_performance_thresholds(self, metric: PerformanceMetrics):
        """Check if performance metric exceeds thresholds"""
        
        try:
            threshold_key = f"{metric.action_type}_duration"
            threshold = self.performance_thresholds.get(threshold_key)
            
            if threshold and metric.duration > threshold:
                logger.warning(f"Performance threshold exceeded for {metric.action_type}: "
                             f"{metric.duration:.2f}s > {threshold}s")
            
            if metric.memory_usage and metric.memory_usage > self.performance_thresholds["memory_usage"]:
                logger.warning(f"Memory usage threshold exceeded: "
                             f"{metric.memory_usage:.2f}MB > {self.performance_thresholds['memory_usage']}MB")
            
            if metric.cpu_usage and metric.cpu_usage > self.performance_thresholds["cpu_usage"]:
                logger.warning(f"CPU usage threshold exceeded: "
                             f"{metric.cpu_usage:.2f}% > {self.performance_thresholds['cpu_usage']}%")
                
        except Exception as e:
            logger.error(f"Error checking performance thresholds: {e}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        
        try:
            while self.monitoring_active:
                # Periodic cleanup of old data
                await self._cleanup_old_data()
                
                # Save monitoring data periodically
                await self._save_monitoring_data()
                
                # Sleep for monitoring interval
                await asyncio.sleep(60)  # 1 minute
                
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
    
    async def _cleanup_old_data(self):
        """Clean up old monitoring data"""
        
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=7)  # Keep 7 days
            
            # Clean up old detection events
            self.detection_events = [
                event for event in self.detection_events
                if event.timestamp > cutoff_time
            ]
            
            # Clean up old performance metrics
            self.performance_metrics = [
                metric for metric in self.performance_metrics
                if metric.timestamp > cutoff_time
            ]
            
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
    
    async def _save_monitoring_data(self):
        """Save monitoring data to files"""
        
        try:
            # Save detection events
            events_file = self.monitoring_dir / "detection_events.json"
            events_data = [asdict(event) for event in self.detection_events]
            
            # Convert datetime objects to strings
            for event_data in events_data:
                event_data["timestamp"] = event_data["timestamp"].isoformat()
                if event_data["resolution_time"]:
                    event_data["resolution_time"] = event_data["resolution_time"].isoformat()
            
            with open(events_file, 'w') as f:
                json.dump(events_data, f, indent=2)
            
            # Save performance metrics
            metrics_file = self.monitoring_dir / "performance_metrics.json"
            metrics_data = [asdict(metric) for metric in self.performance_metrics]
            
            # Convert datetime objects to strings
            for metric_data in metrics_data:
                metric_data["timestamp"] = metric_data["timestamp"].isoformat()
            
            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving monitoring data: {e}")
    
    async def _pause_profile_automation(self, profile_id: int):
        """Pause automation for a specific profile"""
        
        try:
            async with get_async_session() as session:
                profile = await session.get(BrowserProfile, profile_id)
                if profile:
                    profile.status = "paused"
                    profile.current_action = "Tạm dừng do phát hiện vấn đề"
                    await session.commit()
                    
        except Exception as e:
            logger.error(f"Error pausing profile automation: {e}")
    
    async def _disable_profile_automation(self, profile_id: int):
        """Disable automation for a specific profile"""
        
        try:
            async with get_async_session() as session:
                profile = await session.get(BrowserProfile, profile_id)
                if profile:
                    profile.status = "error"
                    profile.current_action = "Vô hiệu hóa do tài khoản bị chặn"
                    profile.is_active = False
                    await session.commit()
                    
        except Exception as e:
            logger.error(f"Error disabling profile automation: {e}")


# Global monitoring service instance
monitoring_service = MonitoringService()

"""
Performance optimization utilities and memory management
"""

import gc
import asyncio
import psutil
import weakref
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import threading
from concurrent.futures import Thread<PERSON>oolExecutor
from loguru import logger

from core.config import settings


@dataclass
class MemoryStats:
    """Memory usage statistics"""
    total_mb: float
    used_mb: float
    available_mb: float
    percent: float
    process_mb: float
    process_percent: float


@dataclass
class PerformanceMetrics:
    """Performance metrics"""
    cpu_percent: float
    memory_stats: MemoryStats
    active_tasks: int
    active_browsers: int
    response_time_ms: float
    throughput_per_second: float


class MemoryManager:
    """Advanced memory management system"""
    
    def __init__(self):
        self.memory_limit_mb = getattr(settings, 'MEMORY_LIMIT_MB', 1024)
        self.cleanup_threshold = 0.85  # Cleanup when 85% of limit is reached
        self.weak_references: Dict[str, weakref.ref] = {}
        self.cleanup_callbacks: List[Callable] = []
        self.last_cleanup = datetime.utcnow()
        self.cleanup_interval = timedelta(minutes=5)
        
    def register_cleanup_callback(self, callback: Callable):
        """Register callback for memory cleanup"""
        self.cleanup_callbacks.append(callback)
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics"""
        # System memory
        memory = psutil.virtual_memory()
        
        # Process memory
        process = psutil.Process()
        process_memory = process.memory_info()
        
        return MemoryStats(
            total_mb=memory.total / 1024 / 1024,
            used_mb=memory.used / 1024 / 1024,
            available_mb=memory.available / 1024 / 1024,
            percent=memory.percent,
            process_mb=process_memory.rss / 1024 / 1024,
            process_percent=(process_memory.rss / memory.total) * 100
        )
    
    def should_cleanup(self) -> bool:
        """Check if memory cleanup is needed"""
        stats = self.get_memory_stats()
        
        # Check process memory limit
        if stats.process_mb > self.memory_limit_mb * self.cleanup_threshold:
            return True
        
        # Check system memory
        if stats.percent > 90:
            return True
        
        # Check time-based cleanup
        if datetime.utcnow() - self.last_cleanup > self.cleanup_interval:
            return True
        
        return False
    
    async def cleanup_memory(self, force: bool = False):
        """Perform memory cleanup"""
        if not force and not self.should_cleanup():
            return
        
        logger.info("Starting memory cleanup...")
        
        # Run cleanup callbacks
        for callback in self.cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.error(f"Error in cleanup callback: {e}")
        
        # Clean up weak references
        dead_refs = []
        for key, ref in self.weak_references.items():
            if ref() is None:
                dead_refs.append(key)
        
        for key in dead_refs:
            del self.weak_references[key]
        
        # Force garbage collection
        collected = gc.collect()
        
        self.last_cleanup = datetime.utcnow()
        
        stats = self.get_memory_stats()
        logger.info(f"Memory cleanup completed. Collected {collected} objects. "
                   f"Process memory: {stats.process_mb:.1f}MB")
    
    def register_object(self, key: str, obj: Any):
        """Register object for weak reference tracking"""
        self.weak_references[key] = weakref.ref(obj)
    
    def get_object(self, key: str) -> Optional[Any]:
        """Get object from weak reference"""
        ref = self.weak_references.get(key)
        return ref() if ref else None


class ProcessPool:
    """Managed process pool for CPU-intensive tasks"""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or min(4, psutil.cpu_count())
        self.executor: Optional[ThreadPoolExecutor] = None
        self.active_tasks: Dict[str, asyncio.Future] = {}
        
    async def start(self):
        """Start the process pool"""
        if self.executor is None:
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
            logger.info(f"Process pool started with {self.max_workers} workers")
    
    async def stop(self):
        """Stop the process pool"""
        if self.executor:
            # Cancel active tasks
            for task_id, future in self.active_tasks.items():
                future.cancel()
            
            self.active_tasks.clear()
            
            # Shutdown executor
            self.executor.shutdown(wait=True)
            self.executor = None
            logger.info("Process pool stopped")
    
    async def submit_task(self, task_id: str, func: Callable, *args, **kwargs) -> Any:
        """Submit task to process pool"""
        if not self.executor:
            await self.start()
        
        loop = asyncio.get_event_loop()
        future = loop.run_in_executor(self.executor, func, *args, **kwargs)
        
        self.active_tasks[task_id] = future
        
        try:
            result = await future
            return result
        finally:
            self.active_tasks.pop(task_id, None)
    
    def get_active_task_count(self) -> int:
        """Get number of active tasks"""
        return len(self.active_tasks)


class LazyLoader:
    """Lazy loading utility for heavy resources"""
    
    def __init__(self):
        self.loaded_modules: Dict[str, Any] = {}
        self.loading_locks: Dict[str, asyncio.Lock] = {}
    
    async def load_module(self, module_name: str, loader_func: Callable) -> Any:
        """Lazy load a module"""
        if module_name in self.loaded_modules:
            return self.loaded_modules[module_name]
        
        # Get or create lock for this module
        if module_name not in self.loading_locks:
            self.loading_locks[module_name] = asyncio.Lock()
        
        async with self.loading_locks[module_name]:
            # Double-check after acquiring lock
            if module_name in self.loaded_modules:
                return self.loaded_modules[module_name]
            
            logger.info(f"Lazy loading module: {module_name}")
            
            try:
                if asyncio.iscoroutinefunction(loader_func):
                    module = await loader_func()
                else:
                    module = loader_func()
                
                self.loaded_modules[module_name] = module
                logger.info(f"Module loaded successfully: {module_name}")
                return module
                
            except Exception as e:
                logger.error(f"Error loading module {module_name}: {e}")
                raise
    
    def unload_module(self, module_name: str):
        """Unload a module to free memory"""
        if module_name in self.loaded_modules:
            del self.loaded_modules[module_name]
            logger.info(f"Module unloaded: {module_name}")
    
    def get_loaded_modules(self) -> List[str]:
        """Get list of loaded modules"""
        return list(self.loaded_modules.keys())


class ResourcePool:
    """Generic resource pool with automatic cleanup"""
    
    def __init__(self, max_size: int = 10, cleanup_interval: int = 300):
        self.max_size = max_size
        self.cleanup_interval = cleanup_interval
        self.resources: Dict[str, Any] = {}
        self.last_used: Dict[str, datetime] = {}
        self.cleanup_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Start the resource pool"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Resource pool started")
    
    async def stop(self):
        """Stop the resource pool"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
        
        # Cleanup all resources
        for resource_id in list(self.resources.keys()):
            await self.release_resource(resource_id)
        
        logger.info("Resource pool stopped")
    
    async def get_resource(self, resource_id: str, factory_func: Callable) -> Any:
        """Get or create resource"""
        if resource_id in self.resources:
            self.last_used[resource_id] = datetime.utcnow()
            return self.resources[resource_id]
        
        # Check pool size limit
        if len(self.resources) >= self.max_size:
            await self._cleanup_oldest()
        
        # Create new resource
        logger.info(f"Creating new resource: {resource_id}")
        
        if asyncio.iscoroutinefunction(factory_func):
            resource = await factory_func()
        else:
            resource = factory_func()
        
        self.resources[resource_id] = resource
        self.last_used[resource_id] = datetime.utcnow()
        
        return resource
    
    async def release_resource(self, resource_id: str):
        """Release a resource"""
        if resource_id in self.resources:
            resource = self.resources.pop(resource_id)
            self.last_used.pop(resource_id, None)
            
            # Call cleanup method if available
            if hasattr(resource, 'cleanup'):
                try:
                    if asyncio.iscoroutinefunction(resource.cleanup):
                        await resource.cleanup()
                    else:
                        resource.cleanup()
                except Exception as e:
                    logger.error(f"Error cleaning up resource {resource_id}: {e}")
            
            logger.info(f"Resource released: {resource_id}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in resource cleanup loop: {e}")
    
    async def _cleanup_expired(self):
        """Cleanup expired resources"""
        now = datetime.utcnow()
        expired_threshold = timedelta(minutes=30)
        
        expired_resources = []
        for resource_id, last_used in self.last_used.items():
            if now - last_used > expired_threshold:
                expired_resources.append(resource_id)
        
        for resource_id in expired_resources:
            await self.release_resource(resource_id)
    
    async def _cleanup_oldest(self):
        """Cleanup oldest resource when pool is full"""
        if not self.last_used:
            return
        
        oldest_id = min(self.last_used.keys(), key=lambda x: self.last_used[x])
        await self.release_resource(oldest_id)


class PerformanceMonitor:
    """Performance monitoring and optimization"""
    
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.process_pool = ProcessPool()
        self.lazy_loader = LazyLoader()
        self.resource_pool = ResourcePool()
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history = 100
        
    async def start(self):
        """Start performance monitoring"""
        await self.process_pool.start()
        await self.resource_pool.start()
        logger.info("Performance monitor started")
    
    async def stop(self):
        """Stop performance monitoring"""
        await self.process_pool.stop()
        await self.resource_pool.stop()
        logger.info("Performance monitor stopped")
    
    async def collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics"""
        memory_stats = self.memory_manager.get_memory_stats()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Get application-specific metrics
        active_tasks = self.process_pool.get_active_task_count()
        active_browsers = len(self.resource_pool.resources)
        
        # Calculate response time (placeholder)
        response_time_ms = 50.0  # This would be calculated from actual requests
        
        # Calculate throughput (placeholder)
        throughput_per_second = 10.0  # This would be calculated from actual metrics
        
        metrics = PerformanceMetrics(
            cpu_percent=cpu_percent,
            memory_stats=memory_stats,
            active_tasks=active_tasks,
            active_browsers=active_browsers,
            response_time_ms=response_time_ms,
            throughput_per_second=throughput_per_second
        )
        
        # Add to history
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > self.max_history:
            self.metrics_history.pop(0)
        
        return metrics
    
    async def optimize_performance(self):
        """Perform performance optimizations"""
        metrics = await self.collect_metrics()
        
        # Memory optimization
        if self.memory_manager.should_cleanup():
            await self.memory_manager.cleanup_memory()
        
        # CPU optimization
        if metrics.cpu_percent > 80:
            logger.warning(f"High CPU usage: {metrics.cpu_percent}%")
            # Could implement CPU throttling here
        
        # Resource optimization
        if metrics.active_browsers > 10:
            logger.warning(f"High browser count: {metrics.active_browsers}")
            # Could implement browser cleanup here
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.metrics_history:
            return {}
        
        latest = self.metrics_history[-1]
        
        # Calculate averages over last 10 metrics
        recent_metrics = self.metrics_history[-10:]
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_stats.process_mb for m in recent_metrics) / len(recent_metrics)
        
        return {
            "current": {
                "cpu_percent": latest.cpu_percent,
                "memory_mb": latest.memory_stats.process_mb,
                "memory_percent": latest.memory_stats.process_percent,
                "active_tasks": latest.active_tasks,
                "active_browsers": latest.active_browsers
            },
            "averages": {
                "cpu_percent": avg_cpu,
                "memory_mb": avg_memory
            },
            "optimization": {
                "memory_limit_mb": self.memory_manager.memory_limit_mb,
                "cleanup_threshold": self.memory_manager.cleanup_threshold,
                "loaded_modules": len(self.lazy_loader.loaded_modules),
                "pooled_resources": len(self.resource_pool.resources)
            }
        }


# Global performance monitor instance
performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global performance_monitor
    if performance_monitor is None:
        performance_monitor = PerformanceMonitor()
    return performance_monitor


async def start_performance_monitoring():
    """Start global performance monitoring"""
    monitor = get_performance_monitor()
    await monitor.start()


async def stop_performance_monitoring():
    """Stop global performance monitoring"""
    monitor = get_performance_monitor()
    await monitor.stop()

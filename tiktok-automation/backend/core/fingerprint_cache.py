"""
Fingerprint Cache System
Pre-generates and caches fingerprints for faster browser creation
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger

from camoufox_integration.fingerprint_generator import FingerprintGenerator
from core.config import settings


class FingerprintCache:
    """Manages cached fingerprints for performance optimization"""
    
    def __init__(self, cache_size: int = 50):
        self.cache_size = cache_size
        self.fingerprint_generator = FingerprintGenerator()
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamps: Dict[str, float] = {}
        self.cache_file = Path("data/fingerprint_cache.json")
        self.cache_file.parent.mkdir(exist_ok=True)
        
        # Pre-defined fingerprint templates for common use cases
        self.templates = {
            "windows_desktop": {
                "os": "windows",
                "screen": {"width": 1920, "height": 1080},
                "locale": "en-US",
                "timezone": "America/New_York"
            },
            "windows_laptop": {
                "os": "windows", 
                "screen": {"width": 1366, "height": 768},
                "locale": "en-US",
                "timezone": "America/New_York"
            },
            "mac_desktop": {
                "os": "macos",
                "screen": {"width": 2560, "height": 1440},
                "locale": "en-US", 
                "timezone": "America/New_York"
            },
            "mobile_android": {
                "os": "android",
                "screen": {"width": 393, "height": 851},
                "locale": "en-US",
                "timezone": "America/New_York"
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize cache system and pre-generate fingerprints"""

        try:
            # Force clear cache to regenerate with new fingerprint logic
            logger.info("Force clearing fingerprint cache to regenerate")
            self.cache = {}
            self.cache_timestamps = {}

            # Pre-generate fingerprints for all templates
            await self._pre_generate_fingerprints()

            logger.info(f"Fingerprint cache initialized with {len(self.cache)} entries")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize fingerprint cache: {e}")
            return False
    
    async def get_fingerprint(self, template_name: str = "windows_desktop") -> Dict[str, Any]:
        """Get a cached fingerprint or generate new one"""
        
        try:
            # Check if we have cached fingerprint
            if template_name in self.cache:
                # Check if cache is still fresh (1 hour)
                if time.time() - self.cache_timestamps.get(template_name, 0) < 3600:
                    logger.debug(f"Using cached fingerprint for {template_name}")
                    return self.cache[template_name].copy()
            
            # Generate new fingerprint
            logger.debug(f"Generating new fingerprint for {template_name}")
            fingerprint = await self._generate_fingerprint(template_name)
            
            # Cache it
            self.cache[template_name] = fingerprint
            self.cache_timestamps[template_name] = time.time()
            
            # Save cache
            await self._save_cache()
            
            return fingerprint.copy()
            
        except Exception as e:
            logger.error(f"Failed to get fingerprint for {template_name}: {e}")
            # Return basic fallback
            return self.templates.get(template_name, self.templates["windows_desktop"])
    
    async def _generate_fingerprint(self, template_name: str) -> Dict[str, Any]:
        """Generate fingerprint based on template"""

        base_config = self.templates.get(template_name, self.templates["windows_desktop"])

        # Extract parameters from base config
        os_preference = base_config.get("os", "windows")
        browser_preference = "firefox"  # Default to firefox for Camoufox
        mobile = base_config.get("mobile", False)

        # Use fingerprint generator to enhance the config
        enhanced_config = self.fingerprint_generator.generate_fingerprint(
            os_preference=os_preference,
            browser_preference=browser_preference,
            mobile=mobile
        )

        return enhanced_config
    
    async def _pre_generate_fingerprints(self):
        """Pre-generate fingerprints for all templates"""
        
        logger.info("Pre-generating fingerprints...")
        
        tasks = []
        for template_name in self.templates.keys():
            if template_name not in self.cache:
                task = self._generate_and_cache_fingerprint(template_name)
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            await self._save_cache()
        
        logger.info(f"Pre-generated {len(tasks)} fingerprints")
    
    async def _generate_and_cache_fingerprint(self, template_name: str):
        """Generate and cache a single fingerprint"""
        
        try:
            fingerprint = await self._generate_fingerprint(template_name)
            self.cache[template_name] = fingerprint
            self.cache_timestamps[template_name] = time.time()
            
        except Exception as e:
            logger.error(f"Failed to generate fingerprint for {template_name}: {e}")
    
    async def _load_cache(self):
        """Load cache from disk"""
        
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r') as f:
                    data = json.load(f)
                    self.cache = data.get('cache', {})
                    self.cache_timestamps = data.get('timestamps', {})
                    
                logger.info(f"Loaded {len(self.cache)} cached fingerprints")
                
        except Exception as e:
            logger.warning(f"Failed to load fingerprint cache: {e}")
            self.cache = {}
            self.cache_timestamps = {}
    
    async def _save_cache(self):
        """Save cache to disk"""
        
        try:
            # Limit cache size
            if len(self.cache) > self.cache_size:
                # Remove oldest entries
                sorted_items = sorted(
                    self.cache_timestamps.items(), 
                    key=lambda x: x[1]
                )
                
                to_remove = len(self.cache) - self.cache_size
                for template_name, _ in sorted_items[:to_remove]:
                    del self.cache[template_name]
                    del self.cache_timestamps[template_name]
            
            # Save to disk
            data = {
                'cache': self.cache,
                'timestamps': self.cache_timestamps
            }
            
            with open(self.cache_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save fingerprint cache: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        
        return {
            "cache_size": len(self.cache),
            "max_cache_size": self.cache_size,
            "templates_available": list(self.templates.keys()),
            "cached_templates": list(self.cache.keys()),
            "oldest_entry": min(self.cache_timestamps.values()) if self.cache_timestamps else None,
            "newest_entry": max(self.cache_timestamps.values()) if self.cache_timestamps else None
        }
    
    async def clear_cache(self):
        """Clear all cached fingerprints"""
        
        self.cache.clear()
        self.cache_timestamps.clear()
        
        if self.cache_file.exists():
            self.cache_file.unlink()
        
        logger.info("Fingerprint cache cleared")
    
    async def refresh_cache(self):
        """Refresh all cached fingerprints"""
        
        logger.info("Refreshing fingerprint cache...")
        
        # Clear existing cache
        self.cache.clear()
        self.cache_timestamps.clear()
        
        # Re-generate all fingerprints
        await self._pre_generate_fingerprints()
        
        logger.info("Fingerprint cache refreshed")


# Global instance
fingerprint_cache = FingerprintCache()

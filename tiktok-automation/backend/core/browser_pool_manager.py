"""
Browser Pool Manager
Optimized browser instance pooling for performance
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from loguru import logger

from camoufox_integration.browser_manager import BrowserManager
from models.browser_profile import BrowserProfile
from models.proxy import Proxy
from core.config import settings


@dataclass
class PooledBrowser:
    """Represents a pooled browser instance"""
    browser: Any  # Browser instance
    profile_id: int
    created_at: float
    last_used: float
    in_use: bool = False
    use_count: int = 0


class BrowserPoolManager:
    """Manages a pool of browser instances for optimal performance"""
    
    def __init__(self, max_pool_size: int = 5, max_idle_time: int = 300):
        self.max_pool_size = max_pool_size
        self.max_idle_time = max_idle_time  # 5 minutes
        self.browser_manager = BrowserManager()
        
        # Pool storage
        self.pool: Dict[str, PooledBrowser] = {}
        self.profile_browsers: Dict[int, List[str]] = {}  # profile_id -> browser_ids
        
        # Performance tracking
        self.pool_hits = 0
        self.pool_misses = 0
        self.total_requests = 0
        
        # Cleanup task
        self._cleanup_task = None
        self._running = False
    
    async def initialize(self) -> bool:
        """Initialize the browser pool manager"""
        
        try:
            # Initialize browser manager
            success = await self.browser_manager.initialize()
            if not success:
                return False
            
            # Start cleanup task
            self._running = True
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            logger.info(f"Browser pool manager initialized (max_size: {self.max_pool_size})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize browser pool manager: {e}")
            return False
    
    async def get_browser(
        self,
        profile: BrowserProfile,
        proxy: Optional[Proxy] = None,
        headless: bool = None
    ) -> Any:
        """Get a browser instance from pool or create new one"""
        
        self.total_requests += 1
        browser_key = self._get_browser_key(profile, proxy)
        
        # Check if we have a pooled browser for this profile
        if browser_key in self.pool:
            pooled_browser = self.pool[browser_key]
            
            # Check if browser is still valid and not in use
            if not pooled_browser.in_use and await self._is_browser_valid(pooled_browser.browser):
                # Reuse existing browser
                pooled_browser.in_use = True
                pooled_browser.last_used = time.time()
                pooled_browser.use_count += 1
                
                self.pool_hits += 1
                logger.debug(f"Reusing pooled browser for profile {profile.id}")
                return pooled_browser.browser
        
        # Create new browser instance
        self.pool_misses += 1
        logger.debug(f"Creating new browser for profile {profile.id}")
        
        browser = await self.browser_manager.create_browser_instance(
            profile=profile,
            proxy=proxy,
            headless=headless
        )
        
        # Add to pool if there's space
        if len(self.pool) < self.max_pool_size:
            pooled_browser = PooledBrowser(
                browser=browser,
                profile_id=profile.id,
                created_at=time.time(),
                last_used=time.time(),
                in_use=True,
                use_count=1
            )
            
            self.pool[browser_key] = pooled_browser
            
            # Track by profile
            if profile.id not in self.profile_browsers:
                self.profile_browsers[profile.id] = []
            self.profile_browsers[profile.id].append(browser_key)
            
            logger.debug(f"Added browser to pool (pool size: {len(self.pool)})")
        
        return browser
    
    async def return_browser(self, profile: BrowserProfile, proxy: Optional[Proxy] = None):
        """Return a browser to the pool"""
        
        browser_key = self._get_browser_key(profile, proxy)
        
        if browser_key in self.pool:
            pooled_browser = self.pool[browser_key]
            pooled_browser.in_use = False
            pooled_browser.last_used = time.time()
            
            logger.debug(f"Returned browser to pool for profile {profile.id}")
    
    async def remove_browser(self, profile: BrowserProfile, proxy: Optional[Proxy] = None):
        """Remove a browser from the pool"""
        
        browser_key = self._get_browser_key(profile, proxy)
        
        if browser_key in self.pool:
            pooled_browser = self.pool[browser_key]
            
            try:
                # Close browser
                await pooled_browser.browser.close()
            except Exception as e:
                logger.warning(f"Error closing browser: {e}")
            
            # Remove from pool
            del self.pool[browser_key]
            
            # Remove from profile tracking
            if profile.id in self.profile_browsers:
                if browser_key in self.profile_browsers[profile.id]:
                    self.profile_browsers[profile.id].remove(browser_key)
                
                if not self.profile_browsers[profile.id]:
                    del self.profile_browsers[profile.id]
            
            logger.debug(f"Removed browser from pool for profile {profile.id}")
    
    def _get_browser_key(self, profile: BrowserProfile, proxy: Optional[Proxy]) -> str:
        """Generate unique key for browser instance"""
        
        proxy_id = proxy.id if proxy else "no_proxy"
        return f"profile_{profile.id}_proxy_{proxy_id}"
    
    async def _is_browser_valid(self, browser: Any) -> bool:
        """Check if browser instance is still valid"""
        
        try:
            # Try to get browser contexts to check if browser is alive
            contexts = browser.contexts
            return True
        except Exception:
            return False
    
    async def _cleanup_loop(self):
        """Background task to cleanup idle browsers"""
        
        while self._running:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._cleanup_idle_browsers()
                
            except Exception as e:
                logger.error(f"Error in browser pool cleanup: {e}")
    
    async def _cleanup_idle_browsers(self):
        """Remove idle browsers from pool"""
        
        current_time = time.time()
        to_remove = []
        
        for browser_key, pooled_browser in self.pool.items():
            # Check if browser is idle for too long
            if (not pooled_browser.in_use and 
                current_time - pooled_browser.last_used > self.max_idle_time):
                to_remove.append(browser_key)
        
        # Remove idle browsers
        for browser_key in to_remove:
            pooled_browser = self.pool[browser_key]
            
            try:
                await pooled_browser.browser.close()
                logger.debug(f"Closed idle browser: {browser_key}")
            except Exception as e:
                logger.warning(f"Error closing idle browser {browser_key}: {e}")
            
            del self.pool[browser_key]
            
            # Update profile tracking
            for profile_id, browser_list in self.profile_browsers.items():
                if browser_key in browser_list:
                    browser_list.remove(browser_key)
                    break
        
        if to_remove:
            logger.info(f"Cleaned up {len(to_remove)} idle browsers")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get browser pool statistics"""
        
        hit_rate = (self.pool_hits / self.total_requests * 100) if self.total_requests > 0 else 0
        
        return {
            "pool_size": len(self.pool),
            "max_pool_size": self.max_pool_size,
            "total_requests": self.total_requests,
            "pool_hits": self.pool_hits,
            "pool_misses": self.pool_misses,
            "hit_rate_percent": round(hit_rate, 2),
            "browsers_in_use": sum(1 for b in self.pool.values() if b.in_use),
            "profiles_with_browsers": len(self.profile_browsers)
        }
    
    async def shutdown(self):
        """Shutdown the browser pool manager"""
        
        self._running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Close all browsers
        for pooled_browser in self.pool.values():
            try:
                await pooled_browser.browser.close()
            except Exception as e:
                logger.warning(f"Error closing browser during shutdown: {e}")
        
        self.pool.clear()
        self.profile_browsers.clear()
        
        logger.info("Browser pool manager shutdown complete")


# Global instance
browser_pool_manager = BrowserPoolManager()

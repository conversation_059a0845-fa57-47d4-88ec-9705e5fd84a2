"""
WebSocket Manager for real-time communication
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger


class WebSocketManager:
    """Manages WebSocket connections for real-time communication"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_data: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket):
        """Accept new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_data[websocket] = {
            "connected_at": asyncio.get_event_loop().time(),
            "subscriptions": set()
        }
        
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
        
        # Send welcome message
        await self.send_personal_message(websocket, {
            "type": "connection",
            "status": "connected",
            "message": "Connected to TikTok Automation API"
        })
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        if websocket in self.connection_data:
            del self.connection_data[websocket]
        
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any], subscription: Optional[str] = None):
        """Broadcast message to all connected clients or specific subscription"""
        if not self.active_connections:
            return
        
        disconnected = []
        
        for websocket in self.active_connections:
            try:
                # Check subscription filter
                if subscription:
                    subscriptions = self.connection_data.get(websocket, {}).get("subscriptions", set())
                    if subscription not in subscriptions:
                        continue
                
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.append(websocket)
        
        # Remove disconnected clients
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def handle_message(self, websocket: WebSocket, data: str):
        """Handle incoming WebSocket message"""
        try:
            message = json.loads(data)
            message_type = message.get("type")
            
            if message_type == "subscribe":
                await self._handle_subscribe(websocket, message)
            elif message_type == "unsubscribe":
                await self._handle_unsubscribe(websocket, message)
            elif message_type == "ping":
                await self._handle_ping(websocket, message)
            else:
                await self.send_personal_message(websocket, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                
        except json.JSONDecodeError:
            await self.send_personal_message(websocket, {
                "type": "error",
                "message": "Invalid JSON format"
            })
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send_personal_message(websocket, {
                "type": "error",
                "message": "Internal server error"
            })
    
    async def _handle_subscribe(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle subscription request"""
        subscription = message.get("subscription")
        if not subscription:
            await self.send_personal_message(websocket, {
                "type": "error",
                "message": "Subscription name required"
            })
            return
        
        if websocket in self.connection_data:
            self.connection_data[websocket]["subscriptions"].add(subscription)
        
        await self.send_personal_message(websocket, {
            "type": "subscribed",
            "subscription": subscription,
            "message": f"Subscribed to {subscription}"
        })
        
        logger.info(f"WebSocket subscribed to: {subscription}")
    
    async def _handle_unsubscribe(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle unsubscription request"""
        subscription = message.get("subscription")
        if not subscription:
            await self.send_personal_message(websocket, {
                "type": "error",
                "message": "Subscription name required"
            })
            return
        
        if websocket in self.connection_data:
            self.connection_data[websocket]["subscriptions"].discard(subscription)
        
        await self.send_personal_message(websocket, {
            "type": "unsubscribed",
            "subscription": subscription,
            "message": f"Unsubscribed from {subscription}"
        })
        
        logger.info(f"WebSocket unsubscribed from: {subscription}")
    
    async def _handle_ping(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle ping request"""
        await self.send_personal_message(websocket, {
            "type": "pong",
            "timestamp": message.get("timestamp")
        })
    
    # Specific broadcast methods for different event types
    
    async def broadcast_profile_update(self, profile_id: int, action: str, data: Dict[str, Any]):
        """Broadcast profile update"""
        await self.broadcast({
            "type": "profile_update",
            "profile_id": profile_id,
            "action": action,
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="profiles")
    
    async def broadcast_proxy_update(self, proxy_id: int, action: str, data: Dict[str, Any]):
        """Broadcast proxy update"""
        await self.broadcast({
            "type": "proxy_update",
            "proxy_id": proxy_id,
            "action": action,
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="proxies")
    
    async def broadcast_task_update(self, task_id: int, action: str, data: Dict[str, Any]):
        """Broadcast task update"""
        await self.broadcast({
            "type": "task_update",
            "task_id": task_id,
            "action": action,
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="tasks")
    
    async def broadcast_system_update(self, event: str, data: Dict[str, Any]):
        """Broadcast system update"""
        await self.broadcast({
            "type": "system_update",
            "event": event,
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="system")
    
    async def broadcast_log_message(self, level: str, message: str, context: Dict[str, Any] = None):
        """Broadcast log message"""
        await self.broadcast({
            "type": "log_message",
            "level": level,
            "message": message,
            "context": context or {},
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="logs")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        subscriptions_count = {}
        
        for conn_data in self.connection_data.values():
            for subscription in conn_data.get("subscriptions", set()):
                subscriptions_count[subscription] = subscriptions_count.get(subscription, 0) + 1
        
        return {
            "total_connections": len(self.active_connections),
            "subscriptions": subscriptions_count,
            "uptime": asyncio.get_event_loop().time()
        }


# Global websocket manager instance
websocket_manager = WebSocketManager()

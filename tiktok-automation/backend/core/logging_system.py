"""
Advanced logging system with real-time streaming
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import logging
from loguru import logger

from core.websocket_manager import WebSocketManager


class LogLevel(Enum):
    """Log levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class LogCategory(Enum):
    """Log categories"""
    SYSTEM = "system"
    TASK = "task"
    BROWSER = "browser"
    API = "api"
    DATABASE = "database"
    AUTOMATION = "automation"
    SECURITY = "security"


@dataclass
class LogEntry:
    """Log entry data structure"""
    id: str
    timestamp: datetime
    level: LogLevel
    category: LogCategory
    message: str
    context: Dict[str, Any] = None
    task_id: Optional[int] = None
    account_id: Optional[int] = None
    profile_id: Optional[int] = None
    session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['level'] = self.level.value
        data['category'] = self.category.value
        return data


class LogBuffer:
    """In-memory log buffer for real-time streaming"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.logs: List[LogEntry] = []
        self.subscribers: List[Callable] = []
    
    def add_log(self, log_entry: LogEntry):
        """Add log entry to buffer"""
        self.logs.append(log_entry)
        
        # Maintain buffer size
        if len(self.logs) > self.max_size:
            self.logs = self.logs[-self.max_size:]
        
        # Notify subscribers
        for subscriber in self.subscribers:
            try:
                asyncio.create_task(subscriber(log_entry))
            except Exception as e:
                logger.error(f"Error notifying log subscriber: {e}")
    
    def subscribe(self, callback: Callable):
        """Subscribe to log updates"""
        self.subscribers.append(callback)
    
    def unsubscribe(self, callback: Callable):
        """Unsubscribe from log updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    def get_logs(self, 
                 level: Optional[LogLevel] = None,
                 category: Optional[LogCategory] = None,
                 since: Optional[datetime] = None,
                 limit: Optional[int] = None) -> List[LogEntry]:
        """Get filtered logs"""
        filtered_logs = self.logs
        
        if level:
            filtered_logs = [log for log in filtered_logs if log.level == level]
        
        if category:
            filtered_logs = [log for log in filtered_logs if log.category == category]
        
        if since:
            filtered_logs = [log for log in filtered_logs if log.timestamp >= since]
        
        if limit:
            filtered_logs = filtered_logs[-limit:]
        
        return filtered_logs


class LogFileManager:
    """Manages log files with rotation"""
    
    def __init__(self, log_dir: Path):
        self.log_dir = log_dir
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.current_files = {}
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.max_files = 10
    
    def get_log_file(self, category: LogCategory) -> Path:
        """Get current log file for category"""
        today = datetime.now().strftime("%Y-%m-%d")
        filename = f"{category.value}_{today}.log"
        return self.log_dir / filename
    
    def write_log(self, log_entry: LogEntry):
        """Write log entry to file"""
        try:
            log_file = self.get_log_file(log_entry.category)
            
            # Check file size and rotate if needed
            if log_file.exists() and log_file.stat().st_size > self.max_file_size:
                self._rotate_log_file(log_file)
            
            # Write log entry
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry.to_dict()) + '\n')
                
        except Exception as e:
            logger.error(f"Error writing log to file: {e}")
    
    def _rotate_log_file(self, log_file: Path):
        """Rotate log file"""
        try:
            # Find next rotation number
            base_name = log_file.stem
            extension = log_file.suffix
            rotation_num = 1
            
            while True:
                rotated_name = f"{base_name}.{rotation_num}{extension}"
                rotated_path = log_file.parent / rotated_name
                if not rotated_path.exists():
                    break
                rotation_num += 1
            
            # Rename current file
            log_file.rename(rotated_path)
            
            # Cleanup old rotated files
            self._cleanup_old_files(log_file.parent, base_name, extension)
            
        except Exception as e:
            logger.error(f"Error rotating log file: {e}")
    
    def _cleanup_old_files(self, log_dir: Path, base_name: str, extension: str):
        """Remove old rotated log files"""
        try:
            # Find all rotated files
            pattern = f"{base_name}.*{extension}"
            rotated_files = list(log_dir.glob(pattern))
            
            # Sort by modification time (oldest first)
            rotated_files.sort(key=lambda x: x.stat().st_mtime)
            
            # Remove excess files
            while len(rotated_files) > self.max_files:
                old_file = rotated_files.pop(0)
                old_file.unlink()
                
        except Exception as e:
            logger.error(f"Error cleaning up old log files: {e}")


class RealTimeLogger:
    """Real-time logging system with WebSocket streaming"""
    
    def __init__(self, websocket_manager: WebSocketManager, log_dir: Path):
        self.websocket_manager = websocket_manager
        self.log_buffer = LogBuffer()
        self.file_manager = LogFileManager(log_dir)
        self.log_counter = 0
        
        # Subscribe to buffer updates for WebSocket streaming
        self.log_buffer.subscribe(self._stream_log_to_websocket)
        
        # Setup loguru integration
        self._setup_loguru_integration()
    
    def _setup_loguru_integration(self):
        """Setup loguru to use our logging system"""
        
        def loguru_sink(message):
            """Custom loguru sink"""
            record = message.record
            
            # Map loguru levels to our levels
            level_mapping = {
                "DEBUG": LogLevel.DEBUG,
                "INFO": LogLevel.INFO,
                "WARNING": LogLevel.WARNING,
                "ERROR": LogLevel.ERROR,
                "CRITICAL": LogLevel.CRITICAL
            }
            
            level = level_mapping.get(record["level"].name, LogLevel.INFO)
            
            # Extract context from extra fields
            context = {}
            for key, value in record["extra"].items():
                if key not in ["category", "task_id", "account_id", "profile_id", "session_id"]:
                    context[key] = value
            
            # Create log entry
            log_entry = LogEntry(
                id=f"log_{self.log_counter}",
                timestamp=record["time"].replace(tzinfo=None),
                level=level,
                category=LogCategory(record["extra"].get("category", "system")),
                message=record["message"],
                context=context if context else None,
                task_id=record["extra"].get("task_id"),
                account_id=record["extra"].get("account_id"),
                profile_id=record["extra"].get("profile_id"),
                session_id=record["extra"].get("session_id")
            )
            
            self.log_counter += 1
            self.add_log(log_entry)
        
        # Add our custom sink to loguru
        logger.add(loguru_sink, level="DEBUG")
    
    def add_log(self, log_entry: LogEntry):
        """Add log entry to system"""
        # Add to buffer (for real-time streaming)
        self.log_buffer.add_log(log_entry)
        
        # Write to file (for persistence)
        self.file_manager.write_log(log_entry)
    
    async def _stream_log_to_websocket(self, log_entry: LogEntry):
        """Stream log entry to WebSocket clients"""
        try:
            await self.websocket_manager.broadcast_log_message(
                log_entry.level.value,
                log_entry.message,
                {
                    "id": log_entry.id,
                    "category": log_entry.category.value,
                    "timestamp": log_entry.timestamp.isoformat(),
                    "context": log_entry.context,
                    "task_id": log_entry.task_id,
                    "account_id": log_entry.account_id,
                    "profile_id": log_entry.profile_id,
                    "session_id": log_entry.session_id
                }
            )
        except Exception as e:
            # Use standard logger to avoid recursion
            print(f"Error streaming log to WebSocket: {e}")
    
    def get_logs(self, 
                 level: Optional[LogLevel] = None,
                 category: Optional[LogCategory] = None,
                 since: Optional[datetime] = None,
                 limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get filtered logs"""
        logs = self.log_buffer.get_logs(level, category, since, limit)
        return [log.to_dict() for log in logs]
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """Get logging statistics"""
        all_logs = self.log_buffer.logs
        
        # Count by level
        level_counts = {level.value: 0 for level in LogLevel}
        for log in all_logs:
            level_counts[log.level.value] += 1
        
        # Count by category
        category_counts = {category.value: 0 for category in LogCategory}
        for log in all_logs:
            category_counts[log.category.value] += 1
        
        # Recent activity (last hour)
        one_hour_ago = datetime.utcnow().replace(minute=0, second=0, microsecond=0)
        recent_logs = [log for log in all_logs if log.timestamp >= one_hour_ago]
        
        return {
            "total_logs": len(all_logs),
            "recent_logs": len(recent_logs),
            "level_distribution": level_counts,
            "category_distribution": category_counts,
            "buffer_size": len(all_logs),
            "max_buffer_size": self.log_buffer.max_size
        }


class TaskLogger:
    """Specialized logger for automation tasks"""
    
    def __init__(self, real_time_logger: RealTimeLogger, task_id: int):
        self.real_time_logger = real_time_logger
        self.task_id = task_id
        self.session_id = f"task_{task_id}_{int(time.time())}"
    
    def log(self, level: LogLevel, message: str, context: Dict[str, Any] = None):
        """Log message for this task"""
        log_entry = LogEntry(
            id=f"task_{self.task_id}_{int(time.time() * 1000)}",
            timestamp=datetime.utcnow(),
            level=level,
            category=LogCategory.TASK,
            message=message,
            context=context,
            task_id=self.task_id,
            session_id=self.session_id
        )
        
        self.real_time_logger.add_log(log_entry)
    
    def debug(self, message: str, context: Dict[str, Any] = None):
        """Log debug message"""
        self.log(LogLevel.DEBUG, message, context)
    
    def info(self, message: str, context: Dict[str, Any] = None):
        """Log info message"""
        self.log(LogLevel.INFO, message, context)
    
    def warning(self, message: str, context: Dict[str, Any] = None):
        """Log warning message"""
        self.log(LogLevel.WARNING, message, context)
    
    def error(self, message: str, context: Dict[str, Any] = None):
        """Log error message"""
        self.log(LogLevel.ERROR, message, context)
    
    def critical(self, message: str, context: Dict[str, Any] = None):
        """Log critical message"""
        self.log(LogLevel.CRITICAL, message, context)


# Global logger instance
logger_instance: Optional[RealTimeLogger] = None


def get_logger() -> RealTimeLogger:
    """Get global logger instance"""
    global logger_instance
    if logger_instance is None:
        from core.websocket_manager import websocket_manager
        from pathlib import Path
        log_dir = Path("logs")
        logger_instance = RealTimeLogger(websocket_manager, log_dir)
    return logger_instance


def get_task_logger(task_id: int) -> TaskLogger:
    """Get task-specific logger"""
    real_time_logger = get_logger()
    return TaskLogger(real_time_logger, task_id)


# Convenience functions for logging
def log_system(level: LogLevel, message: str, context: Dict[str, Any] = None):
    """Log system message"""
    logger_instance = get_logger()
    log_entry = LogEntry(
        id=f"sys_{int(time.time() * 1000)}",
        timestamp=datetime.utcnow(),
        level=level,
        category=LogCategory.SYSTEM,
        message=message,
        context=context
    )
    logger_instance.add_log(log_entry)


def log_automation(level: LogLevel, message: str, 
                  task_id: Optional[int] = None,
                  account_id: Optional[int] = None,
                  context: Dict[str, Any] = None):
    """Log automation message"""
    logger_instance = get_logger()
    log_entry = LogEntry(
        id=f"auto_{int(time.time() * 1000)}",
        timestamp=datetime.utcnow(),
        level=level,
        category=LogCategory.AUTOMATION,
        message=message,
        context=context,
        task_id=task_id,
        account_id=account_id
    )
    logger_instance.add_log(log_entry)

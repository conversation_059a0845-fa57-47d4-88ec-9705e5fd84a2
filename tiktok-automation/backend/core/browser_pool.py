"""
Browser instance pool with memory optimization and process isolation
"""

import asyncio
import time
import psutil
from typing import Dict, Optional, List, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

from core.config import settings
from loguru import logger

from camoufox_integration.browser_manager import BrowserManager
from models.browser_profile import BrowserProfile
from core.performance import get_performance_monitor


@dataclass
class BrowserInstance:
    """Browser instance wrapper"""
    id: str
    profile_id: int
    browser: Any
    context: Any
    created_at: datetime
    last_used: datetime
    usage_count: int
    memory_mb: float
    is_busy: bool = False
    
    def update_usage(self):
        """Update usage statistics"""
        self.last_used = datetime.utcnow()
        self.usage_count += 1
        
        # Update memory usage
        try:
            process = psutil.Process()
            self.memory_mb = process.memory_info().rss / 1024 / 1024
        except:
            pass


class BrowserPool:
    """Optimized browser instance pool with automatic cleanup"""
    
    def __init__(self, max_instances: int = 10, max_idle_time: int = 300):
        self.max_instances = max_instances
        self.max_idle_time = max_idle_time  # seconds
        self.instances: Dict[str, BrowserInstance] = {}
        self.browser_manager = BrowserManager()
        self.cleanup_task: Optional[asyncio.Task] = None
        self.performance_monitor = get_performance_monitor()
        
        # Pool statistics
        self.total_created = 0
        self.total_destroyed = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
    async def start(self):
        """Start the browser pool"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info(f"Browser pool started (max_instances: {self.max_instances})")
    
    async def stop(self):
        """Stop the browser pool and cleanup all instances"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
        
        # Close all browser instances
        for instance_id in list(self.instances.keys()):
            await self._destroy_instance(instance_id)
        
        logger.info("Browser pool stopped")
    
    async def get_browser(self, profile: BrowserProfile, reuse: bool = True) -> tuple[Any, Any, str]:
        """Get browser instance for profile"""
        instance_id = f"profile_{profile.id}"
        
        # Try to reuse existing instance
        if reuse and instance_id in self.instances:
            instance = self.instances[instance_id]
            
            # Check if instance is still valid
            if await self._is_instance_valid(instance):
                instance.update_usage()
                instance.is_busy = True
                self.cache_hits += 1
                logger.debug(f"Reusing browser instance: {instance_id}")
                return instance.browser, instance.context, instance_id
            else:
                # Instance is invalid, remove it
                await self._destroy_instance(instance_id)
        
        # Create new instance
        self.cache_misses += 1
        return await self._create_instance(profile)
    
    async def release_browser(self, instance_id: str):
        """Release browser instance back to pool"""
        if instance_id in self.instances:
            instance = self.instances[instance_id]
            instance.is_busy = False
            instance.last_used = datetime.utcnow()
            logger.debug(f"Released browser instance: {instance_id}")
    
    async def _create_instance(self, profile: BrowserProfile) -> tuple[Any, Any, str]:
        """Create new browser instance"""
        instance_id = f"profile_{profile.id}"
        
        # Check pool size limit
        if len(self.instances) >= self.max_instances:
            await self._cleanup_oldest_instance()
        
        logger.info(f"Creating new browser instance: {instance_id}")
        
        try:
            # Create browser with profile
            browser = await self.browser_manager.create_browser_instance(profile)
            context = await self.browser_manager.create_browser_context(browser, profile)
            
            # Create instance wrapper
            instance = BrowserInstance(
                id=instance_id,
                profile_id=profile.id,
                browser=browser,
                context=context,
                created_at=datetime.utcnow(),
                last_used=datetime.utcnow(),
                usage_count=1,
                memory_mb=0.0,
                is_busy=True
            )
            
            instance.update_usage()
            self.instances[instance_id] = instance
            self.total_created += 1
            
            logger.info(f"Browser instance created successfully: {instance_id}")
            return browser, context, instance_id
            
        except Exception as e:
            logger.error(f"Error creating browser instance {instance_id}: {e}")
            raise
    
    async def _destroy_instance(self, instance_id: str):
        """Destroy browser instance"""
        if instance_id not in self.instances:
            return
        
        instance = self.instances[instance_id]
        logger.info(f"Destroying browser instance: {instance_id}")
        
        try:
            # Close browser context and instance
            if instance.context:
                await instance.context.close()
            
            if instance.browser:
                await instance.browser.close()
            
            # Remove from pool
            del self.instances[instance_id]
            self.total_destroyed += 1
            
            logger.info(f"Browser instance destroyed: {instance_id}")
            
        except Exception as e:
            logger.error(f"Error destroying browser instance {instance_id}: {e}")
    
    async def _is_instance_valid(self, instance: BrowserInstance) -> bool:
        """Check if browser instance is still valid"""
        try:
            # Check if browser is still connected
            if not instance.browser or not instance.context:
                return False
            
            # Try to get browser version (simple connectivity test)
            await instance.browser.version()
            return True
            
        except Exception:
            return False
    
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._cleanup_idle_instances()
                await self._optimize_memory()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in browser pool cleanup loop: {e}")
    
    async def _cleanup_idle_instances(self):
        """Cleanup idle browser instances"""
        now = datetime.utcnow()
        idle_threshold = timedelta(seconds=self.max_idle_time)
        
        idle_instances = []
        for instance_id, instance in self.instances.items():
            if (not instance.is_busy and 
                now - instance.last_used > idle_threshold):
                idle_instances.append(instance_id)
        
        for instance_id in idle_instances:
            logger.info(f"Cleaning up idle browser instance: {instance_id}")
            await self._destroy_instance(instance_id)
    
    async def _cleanup_oldest_instance(self):
        """Cleanup oldest instance when pool is full"""
        if not self.instances:
            return
        
        # Find oldest non-busy instance
        available_instances = {
            id: instance for id, instance in self.instances.items() 
            if not instance.is_busy
        }
        
        if not available_instances:
            logger.warning("All browser instances are busy, cannot cleanup")
            return
        
        oldest_id = min(available_instances.keys(), 
                       key=lambda x: available_instances[x].created_at)
        
        logger.info(f"Cleaning up oldest browser instance: {oldest_id}")
        await self._destroy_instance(oldest_id)
    
    async def _optimize_memory(self):
        """Optimize memory usage of browser instances"""
        # Get current memory usage
        total_memory = sum(instance.memory_mb for instance in self.instances.values())
        
        # If memory usage is high, cleanup some instances
        memory_limit = 500  # MB per instance
        if total_memory > memory_limit * len(self.instances):
            logger.warning(f"High memory usage in browser pool: {total_memory:.1f}MB")
            
            # Sort instances by memory usage (highest first)
            memory_sorted = sorted(
                self.instances.items(),
                key=lambda x: x[1].memory_mb,
                reverse=True
            )
            
            # Cleanup high-memory instances that are not busy
            for instance_id, instance in memory_sorted[:2]:  # Cleanup top 2
                if not instance.is_busy and instance.memory_mb > memory_limit:
                    logger.info(f"Cleaning up high-memory instance: {instance_id} ({instance.memory_mb:.1f}MB)")
                    await self._destroy_instance(instance_id)
    
    def get_pool_statistics(self) -> Dict[str, Any]:
        """Get browser pool statistics"""
        active_instances = len(self.instances)
        busy_instances = sum(1 for instance in self.instances.values() if instance.is_busy)
        total_memory = sum(instance.memory_mb for instance in self.instances.values())
        
        cache_hit_rate = 0.0
        if self.cache_hits + self.cache_misses > 0:
            cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) * 100
        
        return {
            "active_instances": active_instances,
            "busy_instances": busy_instances,
            "idle_instances": active_instances - busy_instances,
            "max_instances": self.max_instances,
            "total_memory_mb": total_memory,
            "avg_memory_per_instance": total_memory / active_instances if active_instances > 0 else 0,
            "cache_hit_rate": cache_hit_rate,
            "total_created": self.total_created,
            "total_destroyed": self.total_destroyed,
            "instances": [
                {
                    "id": instance.id,
                    "profile_id": instance.profile_id,
                    "created_at": instance.created_at.isoformat(),
                    "last_used": instance.last_used.isoformat(),
                    "usage_count": instance.usage_count,
                    "memory_mb": instance.memory_mb,
                    "is_busy": instance.is_busy
                }
                for instance in self.instances.values()
            ]
        }
    
    async def force_cleanup(self, profile_id: Optional[int] = None):
        """Force cleanup of specific profile or all instances"""
        if profile_id:
            instance_id = f"profile_{profile_id}"
            if instance_id in self.instances:
                await self._destroy_instance(instance_id)
        else:
            # Cleanup all idle instances
            idle_instances = [
                instance_id for instance_id, instance in self.instances.items()
                if not instance.is_busy
            ]
            
            for instance_id in idle_instances:
                await self._destroy_instance(instance_id)
    
    async def warm_up_pool(self, profiles: List[BrowserProfile]):
        """Pre-create browser instances for frequently used profiles"""
        logger.info(f"Warming up browser pool with {len(profiles)} profiles")
        
        for profile in profiles[:self.max_instances]:
            try:
                browser, context, instance_id = await self._create_instance(profile)
                await self.release_browser(instance_id)
                logger.info(f"Warmed up browser instance for profile {profile.id}")
            except Exception as e:
                logger.error(f"Error warming up browser for profile {profile.id}: {e}")


# Global browser pool instance
browser_pool: Optional[BrowserPool] = None


def get_browser_pool() -> BrowserPool:
    """Get global browser pool instance"""
    global browser_pool
    if browser_pool is None:
        max_instances = getattr(settings, 'MAX_CONCURRENT_BROWSERS', 10)
        browser_pool = BrowserPool(max_instances=max_instances)
    return browser_pool


async def start_browser_pool():
    """Start global browser pool"""
    pool = get_browser_pool()
    await pool.start()


async def stop_browser_pool():
    """Stop global browser pool"""
    pool = get_browser_pool()
    await pool.stop()

"""
Encryption utilities for secure data storage
"""

import base64
import json
import os
from typing import Any, Optional, Union
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from loguru import logger

from .config import settings


class EncryptionManager:
    """Manages encryption and decryption of sensitive data"""
    
    def __init__(self, password: Optional[str] = None):
        self.password = password or settings.SECRET_KEY
        self._fernet = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize Fernet encryption with derived key"""
        try:
            # Generate salt (should be stored securely in production)
            salt = b'tiktok_automation_salt_2024'  # Fixed salt for consistency
            
            # Derive key from password
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.password.encode()))
            
            # Initialize Fernet
            self._fernet = Fernet(key)
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise
    
    def encrypt(self, data: Union[str, dict, list]) -> str:
        """Encrypt data and return base64 encoded string"""
        try:
            if not self._fernet:
                raise ValueError("Encryption not initialized")
            
            # Convert data to JSON string if not already string
            if isinstance(data, (dict, list)):
                data_str = json.dumps(data)
            else:
                data_str = str(data)
            
            # Encrypt data
            encrypted_data = self._fernet.encrypt(data_str.encode())
            
            # Return base64 encoded string
            return base64.urlsafe_b64encode(encrypted_data).decode()
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt base64 encoded string and return original data"""
        try:
            if not self._fernet:
                raise ValueError("Encryption not initialized")
            
            # Decode base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            
            # Decrypt data
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            
            # Return decrypted string
            return decrypted_bytes.decode()
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    def encrypt_json(self, data: Union[dict, list]) -> str:
        """Encrypt JSON data and return encrypted string"""
        return self.encrypt(data)
    
    def decrypt_json(self, encrypted_data: str) -> Union[dict, list]:
        """Decrypt string and return JSON data"""
        try:
            decrypted_str = self.decrypt(encrypted_data)
            return json.loads(decrypted_str)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse decrypted JSON: {e}")
            raise
    
    def encrypt_cookies(self, cookies: list) -> str:
        """Encrypt cookies list"""
        return self.encrypt_json(cookies)
    
    def decrypt_cookies(self, encrypted_cookies: str) -> list:
        """Decrypt cookies and return list"""
        result = self.decrypt_json(encrypted_cookies)
        return result if isinstance(result, list) else []
    
    def is_encrypted(self, data: str) -> bool:
        """Check if data appears to be encrypted"""
        try:
            # Try to decode as base64
            base64.urlsafe_b64decode(data.encode())
            return True
        except Exception:
            return False


# Global encryption manager instance
encryption_manager = EncryptionManager()


def encrypt_data(data: Any) -> str:
    """Convenience function to encrypt data"""
    return encryption_manager.encrypt(data)


def decrypt_data(encrypted_data: str) -> str:
    """Convenience function to decrypt data"""
    return encryption_manager.decrypt(encrypted_data)


def encrypt_cookies(cookies: list) -> str:
    """Convenience function to encrypt cookies"""
    return encryption_manager.encrypt_cookies(cookies)


def decrypt_cookies(encrypted_cookies: str) -> list:
    """Convenience function to decrypt cookies"""
    return encryption_manager.decrypt_cookies(encrypted_cookies)

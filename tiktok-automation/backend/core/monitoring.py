"""
Real-time monitoring system for TikTok Automation
"""

import asyncio
import time
import psutil
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger

from core.websocket_manager import WebSocketManager


class MetricType(Enum):
    """Types of metrics to monitor"""
    SYSTEM = "system"
    TASK = "task"
    BROWSER = "browser"
    API = "api"
    DATABASE = "database"


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Metric:
    """Metric data structure"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    metric_type: MetricType
    tags: Dict[str, str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['metric_type'] = self.metric_type.value
        return data


@dataclass
class Alert:
    """Alert data structure"""
    id: str
    title: str
    message: str
    level: AlertLevel
    metric_name: str
    threshold: float
    current_value: float
    timestamp: datetime
    resolved: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['level'] = self.level.value
        return data


class MetricsCollector:
    """Collects various system and application metrics"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics_history: List[Metric] = []
        self.max_history_size = 1000
    
    async def collect_system_metrics(self) -> List[Metric]:
        """Collect system performance metrics"""
        metrics = []
        now = datetime.utcnow()
        
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            metrics.append(Metric(
                name="cpu_usage_percent",
                value=cpu_percent,
                unit="percent",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "cpu"}
            ))
            
            # Memory metrics
            memory = psutil.virtual_memory()
            metrics.append(Metric(
                name="memory_usage_percent",
                value=memory.percent,
                unit="percent",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "memory"}
            ))
            
            metrics.append(Metric(
                name="memory_used_mb",
                value=memory.used / 1024 / 1024,
                unit="MB",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "memory"}
            ))
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            metrics.append(Metric(
                name="disk_usage_percent",
                value=(disk.used / disk.total) * 100,
                unit="percent",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "disk"}
            ))
            
            # Network metrics
            network = psutil.net_io_counters()
            metrics.append(Metric(
                name="network_bytes_sent",
                value=network.bytes_sent,
                unit="bytes",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "network", "direction": "sent"}
            ))
            
            metrics.append(Metric(
                name="network_bytes_recv",
                value=network.bytes_recv,
                unit="bytes",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "network", "direction": "received"}
            ))
            
            # Process metrics
            process = psutil.Process()
            metrics.append(Metric(
                name="process_memory_mb",
                value=process.memory_info().rss / 1024 / 1024,
                unit="MB",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "process"}
            ))
            
            metrics.append(Metric(
                name="process_cpu_percent",
                value=process.cpu_percent(),
                unit="percent",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "process"}
            ))
            
            # Uptime
            uptime = time.time() - self.start_time
            metrics.append(Metric(
                name="uptime_seconds",
                value=uptime,
                unit="seconds",
                timestamp=now,
                metric_type=MetricType.SYSTEM,
                tags={"component": "uptime"}
            ))
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
        
        return metrics
    
    async def collect_application_metrics(self) -> List[Metric]:
        """Collect application-specific metrics"""
        metrics = []
        now = datetime.utcnow()

        try:
            # Import here to avoid circular imports
            from core.database import AsyncSessionLocal
            from sqlalchemy import select, func
            from models.follow_task import FollowTask
            from models.browser_profile import BrowserProfile
            from models.tiktok_account import TikTokAccount

            async with AsyncSessionLocal() as session:
                # Task metrics - count by status
                task_result = await session.execute(
                    select(FollowTask.status, func.count(FollowTask.id))
                    .group_by(FollowTask.status)
                )
                task_stats = dict(task_result.fetchall())

                for status, count in task_stats.items():
                    metrics.append(Metric(
                        name=f"tasks_{status}",
                        value=float(count),
                        unit="count",
                        timestamp=now,
                        metric_type=MetricType.TASK,
                        tags={"component": "tasks", "status": status}
                    ))

                # Profile metrics - count total and by status
                profile_result = await session.execute(
                    select(func.count(BrowserProfile.id))
                )
                profile_count = profile_result.scalar() or 0

                metrics.append(Metric(
                    name="profiles_total",
                    value=float(profile_count),
                    unit="count",
                    timestamp=now,
                    metric_type=MetricType.BROWSER,
                    tags={"component": "profiles"}
                ))

                # Account metrics - count total and by status
                account_result = await session.execute(
                    select(func.count(TikTokAccount.id))
                )
                account_count = account_result.scalar() or 0

                metrics.append(Metric(
                    name="accounts_total",
                    value=float(account_count),
                    unit="count",
                    timestamp=now,
                    metric_type=MetricType.API,
                    tags={"component": "accounts"}
                ))

        except Exception as e:
            logger.error(f"Error collecting application metrics: {e}")

        return metrics
    
    def add_metric(self, metric: Metric):
        """Add a metric to history"""
        self.metrics_history.append(metric)
        
        # Limit history size
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]
    
    def get_metrics_by_type(self, metric_type: MetricType, 
                           since: Optional[datetime] = None) -> List[Metric]:
        """Get metrics by type and time range"""
        metrics = [m for m in self.metrics_history if m.metric_type == metric_type]
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        return metrics
    
    def get_latest_metric(self, name: str) -> Optional[Metric]:
        """Get latest metric by name"""
        for metric in reversed(self.metrics_history):
            if metric.name == name:
                return metric
        return None


class AlertManager:
    """Manages alerts and thresholds"""
    
    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.alerts: List[Alert] = []
        self.thresholds = {
            "cpu_usage_percent": {"warning": 80, "critical": 95},
            "memory_usage_percent": {"warning": 85, "critical": 95},
            "disk_usage_percent": {"warning": 85, "critical": 95},
            "process_memory_mb": {"warning": 1000, "critical": 2000},
            "tasks_failed": {"warning": 5, "critical": 10},
        }
        self.alert_cooldown = 300  # 5 minutes
        self.last_alerts = {}
    
    async def check_metric(self, metric: Metric):
        """Check metric against thresholds and create alerts"""
        if metric.name not in self.thresholds:
            return
        
        thresholds = self.thresholds[metric.name]
        alert_level = None
        threshold_value = None
        
        # Check critical threshold first
        if "critical" in thresholds and metric.value >= thresholds["critical"]:
            alert_level = AlertLevel.CRITICAL
            threshold_value = thresholds["critical"]
        elif "warning" in thresholds and metric.value >= thresholds["warning"]:
            alert_level = AlertLevel.WARNING
            threshold_value = thresholds["warning"]
        
        if alert_level:
            # Check cooldown
            cooldown_key = f"{metric.name}_{alert_level.value}"
            last_alert_time = self.last_alerts.get(cooldown_key, 0)
            
            if time.time() - last_alert_time > self.alert_cooldown:
                await self._create_alert(metric, alert_level, threshold_value)
                self.last_alerts[cooldown_key] = time.time()
    
    async def _create_alert(self, metric: Metric, level: AlertLevel, threshold: float):
        """Create and broadcast alert"""
        alert_id = f"{metric.name}_{int(time.time())}"
        
        alert = Alert(
            id=alert_id,
            title=f"{metric.name.replace('_', ' ').title()} {level.value.title()}",
            message=f"{metric.name} is {metric.value}{metric.unit}, exceeding {level.value} threshold of {threshold}{metric.unit}",
            level=level,
            metric_name=metric.name,
            threshold=threshold,
            current_value=metric.value,
            timestamp=datetime.utcnow()
        )
        
        self.alerts.append(alert)
        
        # Broadcast alert via WebSocket
        await self.websocket_manager.broadcast_system_update("alert", alert.to_dict())
        
        logger.warning(f"Alert created: {alert.title} - {alert.message}")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active (unresolved) alerts"""
        return [alert for alert in self.alerts if not alert.resolved]
    
    def resolve_alert(self, alert_id: str):
        """Mark alert as resolved"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                break


class RealTimeMonitor:
    """Main real-time monitoring system"""
    
    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager(websocket_manager)
        self.monitoring_task = None
        self.is_running = False
        self.collection_interval = 10  # seconds
    
    async def start(self):
        """Start monitoring"""
        if self.is_running:
            return
        
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Real-time monitoring started")
    
    async def stop(self):
        """Stop monitoring"""
        self.is_running = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Real-time monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                # Collect metrics
                system_metrics = await self.metrics_collector.collect_system_metrics()
                app_metrics = await self.metrics_collector.collect_application_metrics()
                
                all_metrics = system_metrics + app_metrics
                
                # Process metrics
                for metric in all_metrics:
                    self.metrics_collector.add_metric(metric)
                    await self.alert_manager.check_metric(metric)
                
                # Broadcast metrics via WebSocket
                await self._broadcast_metrics(all_metrics)
                
                # Wait for next collection
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _broadcast_metrics(self, metrics: List[Metric]):
        """Broadcast metrics to WebSocket clients"""
        metrics_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "metrics": [metric.to_dict() for metric in metrics]
        }
        
        await self.websocket_manager.broadcast_system_update("metrics", metrics_data)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get current metrics summary"""
        summary = {}
        
        # Get latest metrics for each type
        for metric_type in MetricType:
            type_metrics = self.metrics_collector.get_metrics_by_type(metric_type)
            if type_metrics:
                latest_metrics = {}
                for metric in type_metrics:
                    latest_metrics[metric.name] = {
                        "value": metric.value,
                        "unit": metric.unit,
                        "timestamp": metric.timestamp.isoformat()
                    }
                summary[metric_type.value] = latest_metrics
        
        return summary
    
    def get_alerts_summary(self) -> Dict[str, Any]:
        """Get alerts summary"""
        active_alerts = self.alert_manager.get_active_alerts()
        
        return {
            "total_alerts": len(self.alert_manager.alerts),
            "active_alerts": len(active_alerts),
            "alerts_by_level": {
                level.value: len([a for a in active_alerts if a.level == level])
                for level in AlertLevel
            },
            "recent_alerts": [alert.to_dict() for alert in active_alerts[-10:]]
        }


# Global monitor instance
monitor_instance: Optional[RealTimeMonitor] = None


def get_monitor() -> RealTimeMonitor:
    """Get global monitor instance"""
    global monitor_instance
    if monitor_instance is None:
        from core.websocket_manager import websocket_manager
        monitor_instance = RealTimeMonitor(websocket_manager)
    return monitor_instance


async def start_monitoring():
    """Start global monitoring"""
    monitor = get_monitor()
    await monitor.start()


async def stop_monitoring():
    """Stop global monitoring"""
    monitor = get_monitor()
    await monitor.stop()

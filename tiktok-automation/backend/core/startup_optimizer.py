"""
Startup Optimization Service
Handles pre-initialization and performance optimization for desktop app
"""

import asyncio
import time
from typing import Dict, Any, Optional
from loguru import logger

from camoufox_integration.camoufox_wrapper import local_camoufox
from camoufox_integration.browser_manager import BrowserManager
from core.fingerprint_cache import fingerprint_cache
from core.browser_pool_manager import browser_pool_manager
from core.update_manager import update_manager
from core.config import settings


class StartupOptimizer:
    """Optimizes application startup performance"""
    
    def __init__(self):
        self.initialization_start_time = None
        self.initialization_complete = False
        self.browser_manager = None
        
    async def initialize_all_systems(self) -> Dict[str, Any]:
        """Initialize all systems for optimal performance"""
        
        self.initialization_start_time = time.time()
        logger.info("Starting application initialization...")
        
        results = {
            "camoufox_ready": False,
            "browser_manager_ready": False,
            "fingerprint_cache_ready": False,
            "browser_pool_ready": False,
            "update_manager_ready": False,
            "total_time": 0,
            "errors": []
        }
        
        try:
            # Initialize Camoufox first (most time-consuming)
            logger.info("Initializing Camoufox...")
            camoufox_start = time.time()
            
            camoufox_success = await local_camoufox.initialize()
            camoufox_time = time.time() - camoufox_start
            
            if camoufox_success:
                results["camoufox_ready"] = True
                logger.info(f"Camoufox initialized successfully in {camoufox_time:.2f}s")
            else:
                results["errors"].append("Failed to initialize Camoufox")
                logger.error("Failed to initialize Camoufox")
            
            # Initialize Browser Manager
            logger.info("Initializing Browser Manager...")
            browser_start = time.time()
            
            self.browser_manager = BrowserManager()
            browser_success = await self.browser_manager.initialize()
            browser_time = time.time() - browser_start
            
            if browser_success:
                results["browser_manager_ready"] = True
                logger.info(f"Browser Manager initialized successfully in {browser_time:.2f}s")
            else:
                results["errors"].append("Failed to initialize Browser Manager")
                logger.error("Failed to initialize Browser Manager")

            # Initialize Fingerprint Cache
            logger.info("Initializing Fingerprint Cache...")
            cache_start = time.time()

            cache_success = await fingerprint_cache.initialize()
            cache_time = time.time() - cache_start

            if cache_success:
                results["fingerprint_cache_ready"] = True
                logger.info(f"Fingerprint Cache initialized successfully in {cache_time:.2f}s")
            else:
                results["errors"].append("Failed to initialize Fingerprint Cache")
                logger.error("Failed to initialize Fingerprint Cache")

            # Initialize Browser Pool Manager
            logger.info("Initializing Browser Pool Manager...")
            pool_start = time.time()

            pool_success = await browser_pool_manager.initialize()
            pool_time = time.time() - pool_start

            if pool_success:
                results["browser_pool_ready"] = True
                logger.info(f"Browser Pool Manager initialized successfully in {pool_time:.2f}s")
            else:
                results["errors"].append("Failed to initialize Browser Pool Manager")
                logger.error("Failed to initialize Browser Pool Manager")

            # Initialize Update Manager (non-blocking)
            logger.info("Initializing Update Manager...")
            update_start = time.time()

            update_success = await update_manager.initialize()
            update_time = time.time() - update_start

            if update_success:
                results["update_manager_ready"] = True
                logger.info(f"Update Manager initialized successfully in {update_time:.2f}s")
            else:
                results["errors"].append("Failed to initialize Update Manager")
                logger.error("Failed to initialize Update Manager")

            # Mark as complete
            self.initialization_complete = True
            total_time = time.time() - self.initialization_start_time
            results["total_time"] = total_time
            
            logger.info(f"Application initialization completed in {total_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            results["errors"].append(str(e))
        
        return results
    
    async def warm_up_browser_pool(self, pool_size: int = 1) -> bool:
        """Pre-warm browser instances for faster response"""
        
        if not self.initialization_complete:
            logger.warning("Cannot warm up browser pool - initialization not complete")
            return False
        
        try:
            logger.info(f"Warming up browser pool with {pool_size} instances...")
            
            # This would create and cache browser instances
            # For now, just ensure the system is ready
            
            logger.info("Browser pool warm-up completed")
            return True
            
        except Exception as e:
            logger.error(f"Browser pool warm-up failed: {e}")
            return False
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """Get current initialization status"""
        
        if not self.initialization_start_time:
            return {"status": "not_started"}
        
        if self.initialization_complete:
            total_time = time.time() - self.initialization_start_time
            return {
                "status": "complete",
                "total_time": total_time,
                "camoufox_ready": True,
                "browser_manager_ready": self.browser_manager is not None
            }
        else:
            elapsed_time = time.time() - self.initialization_start_time
            return {
                "status": "in_progress",
                "elapsed_time": elapsed_time
            }
    
    async def optimize_memory_usage(self) -> Dict[str, Any]:
        """Optimize memory usage for desktop app"""
        
        try:
            import gc
            import psutil
            
            # Get initial memory usage
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Force garbage collection
            gc.collect()
            
            # Get memory after cleanup
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_saved = initial_memory - final_memory
            
            logger.info(f"Memory optimization: {memory_saved:.2f}MB saved")
            
            return {
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_saved_mb": memory_saved
            }
            
        except Exception as e:
            logger.error(f"Memory optimization failed: {e}")
            return {"error": str(e)}


# Global instance
startup_optimizer = StartupOptimizer()

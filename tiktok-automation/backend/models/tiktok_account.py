"""
TikTok Account model for managing account information and cookies
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from core.database import Base


class TikTokAccount(Base):
    """TikTok account with cookie management"""
    
    __tablename__ = "tiktok_accounts"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Account info
    username = Column(String(255), nullable=False, unique=True, index=True)
    display_name = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    
    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_logged_in = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_banned = Column(Boolean, default=False, nullable=False)
    
    # Profile information
    profile_picture_url = Column(Text, nullable=True)
    bio = Column(Text, nullable=True)
    follower_count = Column(Integer, default=0, nullable=False)
    following_count = Column(Integer, default=0, nullable=False)
    likes_count = Column(Integer, default=0, nullable=False)
    videos_count = Column(Integer, default=0, nullable=False)
    
    # Browser profile association
    browser_profile_id = Column(Integer, ForeignKey("browser_profiles.id"), nullable=True, index=True)
    
    # Cookie storage (encrypted)
    cookies_data = Column(Text, nullable=True)  # JSON string of cookies
    session_data = Column(JSON, nullable=True)  # Additional session info
    
    # Login tracking
    last_login = Column(DateTime, nullable=True)
    login_count = Column(Integer, default=0, nullable=False)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    
    # Activity limits and tracking
    daily_follow_count = Column(Integer, default=0, nullable=False)
    daily_unfollow_count = Column(Integer, default=0, nullable=False)
    daily_like_count = Column(Integer, default=0, nullable=False)
    daily_comment_count = Column(Integer, default=0, nullable=False)
    
    # Rate limiting
    last_follow_action = Column(DateTime, nullable=True)
    last_unfollow_action = Column(DateTime, nullable=True)
    last_like_action = Column(DateTime, nullable=True)
    last_comment_action = Column(DateTime, nullable=True)
    
    # Account health
    warning_count = Column(Integer, default=0, nullable=False)
    last_warning = Column(DateTime, nullable=True)
    warning_message = Column(Text, nullable=True)
    
    # Notes and tags
    notes = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True, default=list)  # List of tags
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    browser_profile = relationship("BrowserProfile", back_populates="tiktok_accounts")
    follow_tasks = relationship("FollowTask", back_populates="tiktok_account")
    activity_logs = relationship("ActivityLog", back_populates="tiktok_account")
    
    @hybrid_property
    def cookies(self) -> Optional[List[Dict[str, Any]]]:
        """Get decrypted cookies"""
        if not self.cookies_data:
            return None
        
        try:
            # TODO: Implement decryption
            return json.loads(self.cookies_data)
        except (json.JSONDecodeError, Exception):
            return None
    
    @cookies.setter
    def cookies(self, value: Optional[List[Dict[str, Any]]]):
        """Set encrypted cookies"""
        if value is None:
            self.cookies_data = None
        else:
            try:
                # TODO: Implement encryption
                self.cookies_data = json.dumps(value)
            except Exception:
                self.cookies_data = None
    
    @hybrid_property
    def engagement_rate(self) -> float:
        """Calculate engagement rate"""
        if self.follower_count == 0:
            return 0.0
        
        total_engagement = self.likes_count
        return (total_engagement / self.follower_count) * 100
    
    @hybrid_property
    def daily_activity_summary(self) -> Dict[str, int]:
        """Get daily activity summary"""
        return {
            "follows": self.daily_follow_count,
            "unfollows": self.daily_unfollow_count,
            "likes": self.daily_like_count,
            "comments": self.daily_comment_count
        }
    
    def can_perform_action(self, action_type: str, limit: int) -> bool:
        """Check if account can perform specific action based on limits"""
        current_count = getattr(self, f"daily_{action_type}_count", 0)
        return current_count < limit
    
    def update_action_count(self, action_type: str):
        """Update daily action count"""
        current_count = getattr(self, f"daily_{action_type}_count", 0)
        setattr(self, f"daily_{action_type}_count", current_count + 1)
        setattr(self, f"last_{action_type}_action", datetime.utcnow())
    
    def reset_daily_counts(self):
        """Reset daily action counts (called daily)"""
        self.daily_follow_count = 0
        self.daily_unfollow_count = 0
        self.daily_like_count = 0
        self.daily_comment_count = 0
    
    def update_profile_stats(self, stats: Dict[str, int]):
        """Update profile statistics"""
        self.follower_count = stats.get("followers", self.follower_count)
        self.following_count = stats.get("following", self.following_count)
        self.likes_count = stats.get("likes", self.likes_count)
        self.videos_count = stats.get("videos", self.videos_count)
    
    def add_warning(self, message: str):
        """Add warning to account"""
        self.warning_count += 1
        self.last_warning = datetime.utcnow()
        self.warning_message = message
    
    def update_login_status(self, success: bool):
        """Update login status"""
        if success:
            self.is_logged_in = True
            self.last_login = datetime.utcnow()
            self.login_count += 1
            self.failed_login_attempts = 0
        else:
            self.is_logged_in = False
            self.failed_login_attempts += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "username": self.username,
            "display_name": self.display_name,
            "email": self.email,
            "phone": self.phone,
            "is_active": self.is_active,
            "is_logged_in": self.is_logged_in,
            "is_verified": self.is_verified,
            "is_banned": self.is_banned,
            "profile_picture_url": self.profile_picture_url,
            "bio": self.bio,
            "follower_count": self.follower_count,
            "following_count": self.following_count,
            "likes_count": self.likes_count,
            "videos_count": self.videos_count,
            "browser_profile_id": self.browser_profile_id,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "login_count": self.login_count,
            "failed_login_attempts": self.failed_login_attempts,
            "daily_activity": self.daily_activity_summary,
            "engagement_rate": self.engagement_rate,
            "warning_count": self.warning_count,
            "last_warning": self.last_warning.isoformat() if self.last_warning else None,
            "warning_message": self.warning_message,
            "notes": self.notes,
            "tags": self.tags or [],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
    
    def __repr__(self):
        return f"<TikTokAccount(id={self.id}, username='{self.username}', active={self.is_active})>"

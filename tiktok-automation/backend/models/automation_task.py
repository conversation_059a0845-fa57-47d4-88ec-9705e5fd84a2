"""
Automation Task Model
Represents automation tasks for TikTok interactions
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from core.database import Base


class AutomationTask(Base):
    """Model for automation tasks"""
    
    __tablename__ = "automation_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("browser_profiles.id"), nullable=False)
    task_type = Column(String(50), nullable=False)  # follow_users, unfollow_users, follow_competitors_followers
    target_data = Column(JSON, nullable=False)  # Target usernames, competitor info, etc.
    config = Column(JSON, default={})  # Task-specific configuration
    status = Column(String(20), default="created")  # created, running, paused, stopped, completed, failed
    session_id = Column(String(100), nullable=True)  # Engine session ID
    results = Column(JSON, default={})  # Task results and statistics
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    profile = relationship("BrowserProfile", back_populates="automation_tasks")
    
    def __repr__(self):
        return f"<AutomationTask(id={self.id}, type={self.task_type}, status={self.status})>"
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "profile_id": self.profile_id,
            "task_type": self.task_type,
            "target_data": self.target_data,
            "config": self.config,
            "status": self.status,
            "session_id": self.session_id,
            "results": self.results,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "profile_name": self.profile.name if self.profile else None
        }

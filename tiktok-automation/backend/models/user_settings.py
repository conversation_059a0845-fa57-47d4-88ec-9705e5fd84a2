"""
User Settings model for application configuration
"""

from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, Integer, String, Boolean, DateTime, JSON
from sqlalchemy.ext.hybrid import hybrid_property

from core.database import Base


class UserSettings(Base):
    """User settings and application configuration"""
    
    __tablename__ = "user_settings"
    
    # Primary key (singleton pattern - only one record)
    id = Column(Integer, primary_key=True, default=1)
    
    # UI Settings
    theme = Column(String(20), default="dark", nullable=False)  # "dark", "light", "auto"
    language = Column(String(10), default="en", nullable=False)  # "en", "vi", etc.
    
    # Automation Settings
    auto_save_cookies = Column(Boolean, default=True, nullable=False)
    auto_start_tasks = Column(Boolean, default=False, nullable=False)
    auto_retry_failed_tasks = Column(<PERSON>olean, default=True, nullable=False)
    
    # Rate Limiting
    follow_limit_per_hour = Column(Integer, default=50, nullable=False)
    follow_limit_per_day = Column(Integer, default=200, nullable=False)
    unfollow_limit_per_hour = Column(Integer, default=30, nullable=False)
    unfollow_limit_per_day = Column(Integer, default=100, nullable=False)
    like_limit_per_hour = Column(Integer, default=100, nullable=False)
    like_limit_per_day = Column(Integer, default=500, nullable=False)
    
    # Timing Settings
    delay_between_follows_min = Column(Integer, default=2, nullable=False)  # seconds
    delay_between_follows_max = Column(Integer, default=5, nullable=False)  # seconds
    delay_between_unfollows_min = Column(Integer, default=3, nullable=False)  # seconds
    delay_between_unfollows_max = Column(Integer, default=7, nullable=False)  # seconds
    delay_between_likes_min = Column(Integer, default=1, nullable=False)  # seconds
    delay_between_likes_max = Column(Integer, default=3, nullable=False)  # seconds
    
    # Browser Settings
    default_headless = Column(Boolean, default=True, nullable=False)
    browser_timeout = Column(Integer, default=30, nullable=False)  # seconds
    max_concurrent_browsers = Column(Integer, default=3, nullable=False)
    
    # Proxy Settings
    auto_rotate_proxies = Column(Boolean, default=True, nullable=False)
    proxy_timeout = Column(Integer, default=10, nullable=False)  # seconds
    proxy_retry_attempts = Column(Integer, default=3, nullable=False)
    
    # Notification Settings
    enable_notifications = Column(Boolean, default=True, nullable=False)
    notify_on_task_complete = Column(Boolean, default=True, nullable=False)
    notify_on_errors = Column(Boolean, default=True, nullable=False)
    notify_on_warnings = Column(Boolean, default=False, nullable=False)
    
    # System Settings
    minimize_to_tray = Column(Boolean, default=False, nullable=False)
    start_minimized = Column(Boolean, default=False, nullable=False)
    auto_start_with_system = Column(Boolean, default=False, nullable=False)
    
    # Performance Settings
    memory_limit_mb = Column(Integer, default=1024, nullable=False)  # 1GB
    cpu_limit_percent = Column(Integer, default=80, nullable=False)
    log_retention_days = Column(Integer, default=30, nullable=False)
    
    # Security Settings
    encrypt_cookies = Column(Boolean, default=True, nullable=False)
    encrypt_passwords = Column(Boolean, default=True, nullable=False)
    auto_logout_minutes = Column(Integer, default=0, nullable=False)  # 0 = disabled
    
    # Advanced Settings
    advanced_settings = Column(JSON, nullable=True, default=dict)
    
    # Backup Settings
    auto_backup = Column(Boolean, default=True, nullable=False)
    backup_interval_hours = Column(Integer, default=24, nullable=False)
    backup_retention_days = Column(Integer, default=7, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    @hybrid_property
    def rate_limits(self) -> Dict[str, Dict[str, int]]:
        """Get all rate limits"""
        return {
            "follow": {
                "per_hour": self.follow_limit_per_hour,
                "per_day": self.follow_limit_per_day
            },
            "unfollow": {
                "per_hour": self.unfollow_limit_per_hour,
                "per_day": self.unfollow_limit_per_day
            },
            "like": {
                "per_hour": self.like_limit_per_hour,
                "per_day": self.like_limit_per_day
            }
        }
    
    @hybrid_property
    def delay_settings(self) -> Dict[str, Dict[str, int]]:
        """Get all delay settings"""
        return {
            "follow": {
                "min": self.delay_between_follows_min,
                "max": self.delay_between_follows_max
            },
            "unfollow": {
                "min": self.delay_between_unfollows_min,
                "max": self.delay_between_unfollows_max
            },
            "like": {
                "min": self.delay_between_likes_min,
                "max": self.delay_between_likes_max
            }
        }
    
    @hybrid_property
    def notification_settings(self) -> Dict[str, bool]:
        """Get notification settings"""
        return {
            "enabled": self.enable_notifications,
            "task_complete": self.notify_on_task_complete,
            "errors": self.notify_on_errors,
            "warnings": self.notify_on_warnings
        }
    
    def update_rate_limits(self, action_type: str, per_hour: Optional[int] = None, per_day: Optional[int] = None):
        """Update rate limits for specific action type"""
        if action_type == "follow":
            if per_hour is not None:
                self.follow_limit_per_hour = per_hour
            if per_day is not None:
                self.follow_limit_per_day = per_day
        elif action_type == "unfollow":
            if per_hour is not None:
                self.unfollow_limit_per_hour = per_hour
            if per_day is not None:
                self.unfollow_limit_per_day = per_day
        elif action_type == "like":
            if per_hour is not None:
                self.like_limit_per_hour = per_hour
            if per_day is not None:
                self.like_limit_per_day = per_day
    
    def update_delay_settings(self, action_type: str, min_delay: Optional[int] = None, max_delay: Optional[int] = None):
        """Update delay settings for specific action type"""
        if action_type == "follow":
            if min_delay is not None:
                self.delay_between_follows_min = min_delay
            if max_delay is not None:
                self.delay_between_follows_max = max_delay
        elif action_type == "unfollow":
            if min_delay is not None:
                self.delay_between_unfollows_min = min_delay
            if max_delay is not None:
                self.delay_between_unfollows_max = max_delay
        elif action_type == "like":
            if min_delay is not None:
                self.delay_between_likes_min = min_delay
            if max_delay is not None:
                self.delay_between_likes_max = max_delay
    
    def get_advanced_setting(self, key: str, default: Any = None) -> Any:
        """Get advanced setting value"""
        if not self.advanced_settings:
            return default
        return self.advanced_settings.get(key, default)
    
    def set_advanced_setting(self, key: str, value: Any):
        """Set advanced setting value"""
        if not self.advanced_settings:
            self.advanced_settings = {}
        self.advanced_settings[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "ui": {
                "theme": self.theme,
                "language": self.language
            },
            "automation": {
                "auto_save_cookies": self.auto_save_cookies,
                "auto_start_tasks": self.auto_start_tasks,
                "auto_retry_failed_tasks": self.auto_retry_failed_tasks
            },
            "rate_limits": self.rate_limits,
            "delays": self.delay_settings,
            "browser": {
                "default_headless": self.default_headless,
                "timeout": self.browser_timeout,
                "max_concurrent": self.max_concurrent_browsers
            },
            "proxy": {
                "auto_rotate": self.auto_rotate_proxies,
                "timeout": self.proxy_timeout,
                "retry_attempts": self.proxy_retry_attempts
            },
            "notifications": self.notification_settings,
            "system": {
                "minimize_to_tray": self.minimize_to_tray,
                "start_minimized": self.start_minimized,
                "auto_start_with_system": self.auto_start_with_system
            },
            "performance": {
                "memory_limit_mb": self.memory_limit_mb,
                "cpu_limit_percent": self.cpu_limit_percent,
                "log_retention_days": self.log_retention_days
            },
            "security": {
                "encrypt_cookies": self.encrypt_cookies,
                "encrypt_passwords": self.encrypt_passwords,
                "auto_logout_minutes": self.auto_logout_minutes
            },
            "backup": {
                "auto_backup": self.auto_backup,
                "interval_hours": self.backup_interval_hours,
                "retention_days": self.backup_retention_days
            },
            "advanced": self.advanced_settings or {},
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
    
    def __repr__(self):
        return f"<UserSettings(id={self.id}, theme='{self.theme}', language='{self.language}')>"

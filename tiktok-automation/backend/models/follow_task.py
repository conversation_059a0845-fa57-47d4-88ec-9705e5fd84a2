"""
Follow Task model for managing automation tasks
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from core.database import Base


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """Task type enumeration"""
    FOLLOW_FOLLOWERS = "follow_followers"
    FOLLOW_FOLLOWING = "follow_following"
    UNFOLLOW_USERS = "unfollow_users"
    LIKE_VIDEOS = "like_videos"
    COMMENT_VIDEOS = "comment_videos"


class FollowTask(Base):
    """Follow automation task"""
    
    __tablename__ = "follow_tasks"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Task info
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    task_type = Column(SQLEnum(TaskType), nullable=False, index=True)
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, nullable=False, index=True)
    
    # Relationships
    tiktok_account_id = Column(Integer, ForeignKey("tiktok_accounts.id"), nullable=False, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=True, index=True)
    browser_profile_id = Column(Integer, ForeignKey("browser_profiles.id"), nullable=True, index=True)
    
    # Task configuration
    target_count = Column(Integer, nullable=False)  # How many to follow/unfollow
    delay_min = Column(Integer, default=2, nullable=False)  # Min delay between actions (seconds)
    delay_max = Column(Integer, default=5, nullable=False)  # Max delay between actions (seconds)
    
    # Filtering and targeting
    filter_criteria = Column(JSON, nullable=True, default=dict)
    target_usernames = Column(JSON, nullable=True, default=list)  # Specific usernames to target
    
    # Progress tracking
    total_processed = Column(Integer, default=0, nullable=False)
    successful_actions = Column(Integer, default=0, nullable=False)
    failed_actions = Column(Integer, default=0, nullable=False)
    skipped_actions = Column(Integer, default=0, nullable=False)
    
    # Timing
    scheduled_start = Column(DateTime, nullable=True)
    actual_start = Column(DateTime, nullable=True)
    estimated_completion = Column(DateTime, nullable=True)
    actual_completion = Column(DateTime, nullable=True)
    
    # Error handling
    max_retries = Column(Integer, default=3, nullable=False)
    current_retries = Column(Integer, default=0, nullable=False)
    error_message = Column(Text, nullable=True)
    
    # Advanced settings
    randomize_order = Column(Boolean, default=True, nullable=False)
    respect_rate_limits = Column(Boolean, default=True, nullable=False)
    stop_on_error = Column(Boolean, default=False, nullable=False)
    
    # Results and logs
    results_summary = Column(JSON, nullable=True, default=dict)
    execution_log = Column(JSON, nullable=True, default=list)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    tiktok_account = relationship("TikTokAccount", back_populates="follow_tasks")
    competitor = relationship("Competitor", back_populates="follow_tasks")
    browser_profile = relationship("BrowserProfile", back_populates="follow_tasks")
    
    @hybrid_property
    def progress_percentage(self) -> float:
        """Calculate task progress percentage"""
        if self.target_count == 0:
            return 0.0
        return (self.total_processed / self.target_count) * 100
    
    @hybrid_property
    def success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_processed == 0:
            return 0.0
        return (self.successful_actions / self.total_processed) * 100
    
    @hybrid_property
    def estimated_time_remaining(self) -> Optional[int]:
        """Estimate remaining time in seconds"""
        if self.status != TaskStatus.RUNNING or self.total_processed == 0:
            return None
        
        remaining_actions = self.target_count - self.total_processed
        if remaining_actions <= 0:
            return 0
        
        # Calculate average time per action
        if self.actual_start:
            elapsed_time = (datetime.utcnow() - self.actual_start).total_seconds()
            avg_time_per_action = elapsed_time / self.total_processed
            return int(remaining_actions * avg_time_per_action)
        
        # Estimate based on delay settings
        avg_delay = (self.delay_min + self.delay_max) / 2
        return int(remaining_actions * avg_delay)
    
    @hybrid_property
    def is_active(self) -> bool:
        """Check if task is currently active"""
        return self.status in [TaskStatus.RUNNING, TaskStatus.PENDING]
    
    def start_task(self):
        """Start the task execution"""
        self.status = TaskStatus.RUNNING
        self.actual_start = datetime.utcnow()
        self.current_retries = 0
        
        # Estimate completion time
        avg_delay = (self.delay_min + self.delay_max) / 2
        estimated_duration = self.target_count * avg_delay
        self.estimated_completion = datetime.utcnow() + datetime.timedelta(seconds=estimated_duration)
    
    def pause_task(self):
        """Pause the task execution"""
        if self.status == TaskStatus.RUNNING:
            self.status = TaskStatus.PAUSED
    
    def resume_task(self):
        """Resume the task execution"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.RUNNING
    
    def complete_task(self, success: bool = True):
        """Complete the task"""
        self.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        self.actual_completion = datetime.utcnow()
        
        # Update results summary
        self.results_summary = {
            "total_processed": self.total_processed,
            "successful": self.successful_actions,
            "failed": self.failed_actions,
            "skipped": self.skipped_actions,
            "success_rate": self.success_rate,
            "duration_seconds": (self.actual_completion - self.actual_start).total_seconds() if self.actual_start else 0
        }
    
    def cancel_task(self):
        """Cancel the task"""
        self.status = TaskStatus.CANCELLED
        self.actual_completion = datetime.utcnow()
    
    def update_progress(self, success: bool, skipped: bool = False, error_message: Optional[str] = None):
        """Update task progress"""
        self.total_processed += 1
        
        if skipped:
            self.skipped_actions += 1
        elif success:
            self.successful_actions += 1
        else:
            self.failed_actions += 1
            if error_message:
                self.error_message = error_message
        
        # Check if task is complete
        if self.total_processed >= self.target_count:
            self.complete_task(success=True)
    
    def add_log_entry(self, action: str, target: str, result: str, details: Optional[Dict[str, Any]] = None):
        """Add entry to execution log"""
        if not self.execution_log:
            self.execution_log = []
        
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "action": action,
            "target": target,
            "result": result,
            "details": details or {}
        }
        
        self.execution_log.append(log_entry)
        
        # Keep only last 1000 entries to prevent database bloat
        if len(self.execution_log) > 1000:
            self.execution_log = self.execution_log[-1000:]
    
    def retry_task(self):
        """Retry failed task"""
        if self.current_retries < self.max_retries:
            self.current_retries += 1
            self.status = TaskStatus.PENDING
            self.error_message = None
            return True
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "task_type": self.task_type.value,
            "status": self.status.value,
            "tiktok_account_id": self.tiktok_account_id,
            "competitor_id": self.competitor_id,
            "browser_profile_id": self.browser_profile_id,
            "target_count": self.target_count,
            "delay_min": self.delay_min,
            "delay_max": self.delay_max,
            "filter_criteria": self.filter_criteria or {},
            "target_usernames": self.target_usernames or [],
            "progress": {
                "total_processed": self.total_processed,
                "successful": self.successful_actions,
                "failed": self.failed_actions,
                "skipped": self.skipped_actions,
                "percentage": self.progress_percentage,
                "success_rate": self.success_rate
            },
            "timing": {
                "scheduled_start": self.scheduled_start.isoformat() if self.scheduled_start else None,
                "actual_start": self.actual_start.isoformat() if self.actual_start else None,
                "estimated_completion": self.estimated_completion.isoformat() if self.estimated_completion else None,
                "actual_completion": self.actual_completion.isoformat() if self.actual_completion else None,
                "estimated_time_remaining": self.estimated_time_remaining
            },
            "error_info": {
                "max_retries": self.max_retries,
                "current_retries": self.current_retries,
                "error_message": self.error_message
            },
            "settings": {
                "randomize_order": self.randomize_order,
                "respect_rate_limits": self.respect_rate_limits,
                "stop_on_error": self.stop_on_error
            },
            "results_summary": self.results_summary or {},
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
    
    def __repr__(self):
        return f"<FollowTask(id={self.id}, name='{self.name}', status={self.status.value}, progress={self.progress_percentage:.1f}%)>"

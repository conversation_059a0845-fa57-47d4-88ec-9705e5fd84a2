"""
Follow Settings Model
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from core.database import Base


class FollowSettings(Base):
    __tablename__ = "follow_settings"

    id = Column(Integer, primary_key=True, index=True)
    
    # Cài đặt kịch bản
    target_profile_url = Column(String(500), nullable=False, default="")
    videos_to_watch = Column(Integer, nullable=False, default=3)
    watch_time_seconds = Column(Integer, nullable=False, default=30)
    
    # Cài đặt giới hạn
    follows_per_day = Column(Integer, nullable=False, default=50)
    follows_per_session = Column(Integer, nullable=False, default=10)
    break_time_minutes = Column(Integer, nullable=False, default=3600)  # seconds

    # Cài đặt delay (giây)
    delay_between_follows_min = Column(Integer, nullable=False, default=30)
    delay_between_follows_max = Column(Integer, nullable=False, default=60)
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        return {
            "id": self.id,
            "target_profile_url": self.target_profile_url,
            "videos_to_watch": self.videos_to_watch,
            "watch_time_seconds": self.watch_time_seconds,
            "follows_per_day": self.follows_per_day,
            "follows_per_session": self.follows_per_session,
            "break_time_minutes": self.break_time_minutes,
            "delay_between_follows_min": self.delay_between_follows_min,
            "delay_between_follows_max": self.delay_between_follows_max,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

"""
Tests for TikTok Accounts API endpoints
"""

import pytest
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch

from models.tiktok_account import TikTokAccount


class TestAccountsAPI:
    """Test cases for accounts API endpoints"""

    async def test_get_accounts_empty(self, test_client: AsyncClient):
        """Test getting accounts when none exist"""
        response = await test_client.get("/api/v1/accounts/")
        assert response.status_code == 200
        assert response.json() == []

    async def test_create_account_success(self, test_client: AsyncClient, mock_tiktok_account_data, sample_browser_profile):
        """Test successful account creation"""
        account_data = {
            **mock_tiktok_account_data,
            "browser_profile_id": sample_browser_profile.id
        }
        
        response = await test_client.post("/api/v1/accounts/", json=account_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["username"] == account_data["username"]
        assert data["display_name"] == account_data["display_name"]
        assert data["email"] == account_data["email"]
        assert data["browser_profile_id"] == sample_browser_profile.id

    async def test_create_account_duplicate_username(self, test_client: AsyncClient, sample_tiktok_account):
        """Test creating account with duplicate username"""
        duplicate_data = {
            "username": sample_tiktok_account.username,
            "display_name": "Another User",
            "email": "<EMAIL>"
        }
        
        response = await test_client.post("/api/v1/accounts/", json=duplicate_data)
        assert response.status_code == 400

    async def test_get_account_by_id(self, test_client: AsyncClient, sample_tiktok_account):
        """Test getting account by ID"""
        response = await test_client.get(f"/api/v1/accounts/{sample_tiktok_account.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == sample_tiktok_account.id
        assert data["username"] == sample_tiktok_account.username

    async def test_update_account(self, test_client: AsyncClient, sample_tiktok_account):
        """Test updating account"""
        update_data = {
            "display_name": "Updated Display Name",
            "notes": "Updated notes"
        }
        
        response = await test_client.put(
            f"/api/v1/accounts/{sample_tiktok_account.id}",
            json=update_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["display_name"] == update_data["display_name"]
        assert data["notes"] == update_data["notes"]

    async def test_delete_account(self, test_client: AsyncClient, sample_tiktok_account):
        """Test deleting account"""
        response = await test_client.delete(f"/api/v1/accounts/{sample_tiktok_account.id}")
        assert response.status_code == 200
        
        # Verify account is deleted
        get_response = await test_client.get(f"/api/v1/accounts/{sample_tiktok_account.id}")
        assert get_response.status_code == 404

    @patch('services.account_service.AccountService.login_account')
    async def test_login_account(self, mock_login, test_client: AsyncClient, sample_tiktok_account):
        """Test account login"""
        mock_login.return_value = {
            "success": True,
            "message": "Login successful",
            "cookies_saved": True
        }
        
        login_data = {
            "manual_login": True,
            "save_cookies": True
        }
        
        response = await test_client.post(
            f"/api/v1/accounts/{sample_tiktok_account.id}/login",
            json=login_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        mock_login.assert_called_once()

    @patch('services.account_service.AccountService.logout_account')
    async def test_logout_account(self, mock_logout, test_client: AsyncClient, sample_tiktok_account):
        """Test account logout"""
        mock_logout.return_value = {
            "success": True,
            "message": "Logged out successfully"
        }
        
        response = await test_client.post(f"/api/v1/accounts/{sample_tiktok_account.id}/logout")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        mock_logout.assert_called_once()

    @patch('services.account_service.AccountService.check_login_status')
    async def test_check_login_status(self, mock_check, test_client: AsyncClient, sample_tiktok_account):
        """Test checking account login status"""
        mock_check.return_value = {
            "valid": True,
            "cookie_count": 5,
            "message": "Cookies are valid"
        }
        
        response = await test_client.get(f"/api/v1/accounts/{sample_tiktok_account.id}/login-status")
        assert response.status_code == 200
        
        data = response.json()
        assert data["valid"] is True
        mock_check.assert_called_once()

    @patch('services.cookie_service.CookieService.load_cookies')
    async def test_get_account_cookies(self, mock_load_cookies, test_client: AsyncClient, sample_tiktok_account, mock_cookies):
        """Test getting account cookies info"""
        mock_load_cookies.return_value = mock_cookies
        
        response = await test_client.get(f"/api/v1/accounts/{sample_tiktok_account.id}/cookies")
        assert response.status_code == 200
        
        data = response.json()
        assert data["count"] == len(mock_cookies)
        assert ".tiktok.com" in data["domains"]

    @patch('services.cookie_service.CookieService.delete_cookies')
    async def test_delete_account_cookies(self, mock_delete, test_client: AsyncClient, sample_tiktok_account):
        """Test deleting account cookies"""
        mock_delete.return_value = True
        
        response = await test_client.delete(f"/api/v1/accounts/{sample_tiktok_account.id}/cookies")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        mock_delete.assert_called_once()

    @patch('services.cookie_service.CookieService.validate_cookies')
    async def test_validate_account_cookies(self, mock_validate, test_client: AsyncClient, sample_tiktok_account):
        """Test validating account cookies"""
        mock_validate.return_value = {
            "valid": True,
            "status_code": 200,
            "cookie_count": 3
        }
        
        response = await test_client.post(f"/api/v1/accounts/{sample_tiktok_account.id}/cookies/validate")
        assert response.status_code == 200
        
        data = response.json()
        assert data["valid"] is True
        mock_validate.assert_called_once()

    @patch('services.cookie_service.CookieService.export_cookies')
    async def test_export_account_cookies(self, mock_export, test_client: AsyncClient, sample_tiktok_account):
        """Test exporting account cookies"""
        mock_export.return_value = '{"cookies": "data"}'
        
        response = await test_client.post(
            f"/api/v1/accounts/{sample_tiktok_account.id}/cookies/export",
            json={"format": "json"}
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["format"] == "json"
        assert "data" in data
        mock_export.assert_called_once()

    @patch('services.cookie_service.CookieService.import_cookies')
    async def test_import_account_cookies(self, mock_import, test_client: AsyncClient, sample_tiktok_account):
        """Test importing account cookies"""
        mock_import.return_value = True
        
        import_data = {
            "cookies_data": '{"test": "data"}',
            "format": "json"
        }
        
        response = await test_client.post(
            f"/api/v1/accounts/{sample_tiktok_account.id}/cookies/import",
            json=import_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        mock_import.assert_called_once()

    async def test_get_accounts_with_filters(self, test_client: AsyncClient, test_session):
        """Test getting accounts with filters"""
        # Create test accounts
        accounts = [
            TikTokAccount(username="active1", is_active=True, is_logged_in=True),
            TikTokAccount(username="active2", is_active=True, is_logged_in=False),
            TikTokAccount(username="inactive1", is_active=False, is_logged_in=False)
        ]
        
        for account in accounts:
            test_session.add(account)
        await test_session.commit()
        
        # Test active filter
        response = await test_client.get("/api/v1/accounts/?active_only=true")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        # Test logged in filter
        response = await test_client.get("/api/v1/accounts/?logged_in_only=true")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1

    @patch('services.account_service.AccountService.get_account_statistics')
    async def test_account_statistics(self, mock_stats, test_client: AsyncClient):
        """Test account statistics endpoint"""
        mock_stats.return_value = {
            "total": 10,
            "active": 8,
            "logged_in": 5,
            "verified": 2,
            "banned": 1
        }
        
        response = await test_client.get("/api/v1/accounts/statistics/summary")
        assert response.status_code == 200
        
        data = response.json()
        assert data["total"] == 10
        assert data["active"] == 8
        mock_stats.assert_called_once()

    @patch('services.account_service.AccountService.refresh_account_data')
    async def test_refresh_account_data(self, mock_refresh, test_client: AsyncClient, sample_tiktok_account):
        """Test refreshing account data"""
        mock_refresh.return_value = {
            "success": True,
            "data": {
                "followers": 1000,
                "following": 500
            }
        }
        
        response = await test_client.post(f"/api/v1/accounts/{sample_tiktok_account.id}/refresh")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        mock_refresh.assert_called_once()

    async def test_account_validation_rules(self, test_client: AsyncClient):
        """Test account validation rules"""
        # Test invalid username
        invalid_data = {
            "username": "",  # Empty username
            "email": "<EMAIL>"
        }
        
        response = await test_client.post("/api/v1/accounts/", json=invalid_data)
        assert response.status_code == 422
        
        # Test invalid email format
        invalid_email_data = {
            "username": "testuser",
            "email": "invalid-email"
        }
        
        response = await test_client.post("/api/v1/accounts/", json=invalid_email_data)
        assert response.status_code == 422

    async def test_account_not_found_errors(self, test_client: AsyncClient):
        """Test account not found error handling"""
        non_existent_id = 999
        
        # Test get non-existent account
        response = await test_client.get(f"/api/v1/accounts/{non_existent_id}")
        assert response.status_code == 404
        
        # Test update non-existent account
        response = await test_client.put(
            f"/api/v1/accounts/{non_existent_id}",
            json={"display_name": "Test"}
        )
        assert response.status_code == 404
        
        # Test delete non-existent account
        response = await test_client.delete(f"/api/v1/accounts/{non_existent_id}")
        assert response.status_code == 404

    @patch('services.cookie_service.CookieService.cleanup_expired_cookies')
    async def test_cleanup_expired_cookies(self, mock_cleanup, test_client: AsyncClient):
        """Test cleanup expired cookies endpoint"""
        mock_cleanup.return_value = {
            "accounts_processed": 5,
            "cookies_removed": 15,
            "accounts_logged_out": 2
        }
        
        response = await test_client.post("/api/v1/accounts/cookies/cleanup")
        assert response.status_code == 200
        
        data = response.json()
        assert data["accounts_processed"] == 5
        assert data["cookies_removed"] == 15
        mock_cleanup.assert_called_once()

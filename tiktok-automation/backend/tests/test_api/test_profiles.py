"""
Tests for Browser Profiles API endpoints
"""

import pytest
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from models.browser_profile import BrowserProfile


class TestProfilesAPI:
    """Test cases for profiles API endpoints"""

    async def test_get_profiles_empty(self, test_client: AsyncClient):
        """Test getting profiles when none exist"""
        response = await test_client.get("/api/v1/profiles/")
        assert response.status_code == 200
        assert response.json() == []

    async def test_create_profile_success(self, test_client: AsyncClient, mock_browser_profile_data):
        """Test successful profile creation"""
        response = await test_client.post("/api/v1/profiles/", json=mock_browser_profile_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == mock_browser_profile_data["name"]
        assert data["description"] == mock_browser_profile_data["description"]
        assert data["is_active"] is True
        assert "id" in data
        assert "created_at" in data

    async def test_create_profile_invalid_data(self, test_client: AsyncClient):
        """Test profile creation with invalid data"""
        invalid_data = {
            "name": "",  # Empty name should fail
            "description": "Test"
        }
        
        response = await test_client.post("/api/v1/profiles/", json=invalid_data)
        assert response.status_code == 422

    async def test_get_profile_by_id(self, test_client: AsyncClient, sample_browser_profile):
        """Test getting profile by ID"""
        response = await test_client.get(f"/api/v1/profiles/{sample_browser_profile.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == sample_browser_profile.id
        assert data["name"] == sample_browser_profile.name

    async def test_get_profile_not_found(self, test_client: AsyncClient):
        """Test getting non-existent profile"""
        response = await test_client.get("/api/v1/profiles/999")
        assert response.status_code == 404

    async def test_update_profile(self, test_client: AsyncClient, sample_browser_profile):
        """Test updating profile"""
        update_data = {
            "name": "Updated Profile Name",
            "description": "Updated description"
        }
        
        response = await test_client.put(
            f"/api/v1/profiles/{sample_browser_profile.id}", 
            json=update_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]

    async def test_delete_profile(self, test_client: AsyncClient, sample_browser_profile):
        """Test deleting profile"""
        response = await test_client.delete(f"/api/v1/profiles/{sample_browser_profile.id}")
        assert response.status_code == 200
        
        # Verify profile is deleted
        get_response = await test_client.get(f"/api/v1/profiles/{sample_browser_profile.id}")
        assert get_response.status_code == 404

    async def test_generate_fingerprint(self, test_client: AsyncClient, sample_browser_profile):
        """Test fingerprint generation"""
        response = await test_client.post(f"/api/v1/profiles/{sample_browser_profile.id}/generate-fingerprint")
        assert response.status_code == 200
        
        data = response.json()
        assert "fingerprint" in data
        assert "user_agent" in data["fingerprint"]

    async def test_validate_fingerprint(self, test_client: AsyncClient, sample_browser_profile):
        """Test fingerprint validation"""
        response = await test_client.post(f"/api/v1/profiles/{sample_browser_profile.id}/validate-fingerprint")
        assert response.status_code == 200
        
        data = response.json()
        assert "valid" in data
        assert isinstance(data["valid"], bool)

    async def test_get_profiles_with_filters(self, test_client: AsyncClient, test_session: AsyncSession):
        """Test getting profiles with filters"""
        # Create multiple profiles
        profiles = [
            BrowserProfile(name="Active Profile 1", is_active=True),
            BrowserProfile(name="Active Profile 2", is_active=True),
            BrowserProfile(name="Inactive Profile", is_active=False)
        ]
        
        for profile in profiles:
            test_session.add(profile)
        await test_session.commit()
        
        # Test active filter
        response = await test_client.get("/api/v1/profiles/?active_only=true")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert all(profile["is_active"] for profile in data)
        
        # Test limit
        response = await test_client.get("/api/v1/profiles/?limit=1")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1

    async def test_profile_statistics(self, test_client: AsyncClient, test_session: AsyncSession):
        """Test profile statistics endpoint"""
        # Create profiles with different statuses
        profiles = [
            BrowserProfile(name="Profile 1", is_active=True, usage_count=10),
            BrowserProfile(name="Profile 2", is_active=True, usage_count=5),
            BrowserProfile(name="Profile 3", is_active=False, usage_count=0)
        ]
        
        for profile in profiles:
            test_session.add(profile)
        await test_session.commit()
        
        response = await test_client.get("/api/v1/profiles/statistics/summary")
        assert response.status_code == 200
        
        data = response.json()
        assert data["total"] == 3
        assert data["active"] == 2
        assert data["total_usage"] == 15

    async def test_export_profile(self, test_client: AsyncClient, sample_browser_profile):
        """Test profile export"""
        response = await test_client.post(f"/api/v1/profiles/{sample_browser_profile.id}/export")
        assert response.status_code == 200
        
        data = response.json()
        assert "profile_data" in data
        assert data["profile_data"]["name"] == sample_browser_profile.name

    async def test_import_profile(self, test_client: AsyncClient):
        """Test profile import"""
        import_data = {
            "profile_data": {
                "name": "Imported Profile",
                "description": "Profile imported from backup",
                "fingerprint_config": {
                    "user_agent": "Mozilla/5.0 Test",
                    "screen_resolution": "1920x1080"
                }
            }
        }
        
        response = await test_client.post("/api/v1/profiles/import", json=import_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == "Imported Profile"

    async def test_profile_usage_tracking(self, test_client: AsyncClient, sample_browser_profile):
        """Test profile usage tracking"""
        # Record usage
        response = await test_client.post(f"/api/v1/profiles/{sample_browser_profile.id}/use")
        assert response.status_code == 200
        
        # Check usage count increased
        get_response = await test_client.get(f"/api/v1/profiles/{sample_browser_profile.id}")
        data = get_response.json()
        assert data["usage_count"] == 1

    async def test_profile_concurrent_access(self, test_client: AsyncClient, sample_browser_profile):
        """Test concurrent profile access handling"""
        import asyncio
        
        # Simulate concurrent requests
        tasks = [
            test_client.get(f"/api/v1/profiles/{sample_browser_profile.id}")
            for _ in range(5)
        ]
        
        responses = await asyncio.gather(*tasks)
        
        # All requests should succeed
        for response in responses:
            assert response.status_code == 200

    async def test_profile_validation_rules(self, test_client: AsyncClient):
        """Test profile validation rules"""
        # Test name length validation
        long_name_data = {
            "name": "x" * 300,  # Too long
            "description": "Test"
        }
        
        response = await test_client.post("/api/v1/profiles/", json=long_name_data)
        assert response.status_code == 422
        
        # Test invalid OS preference
        invalid_os_data = {
            "name": "Test Profile",
            "os_preference": "invalid_os"
        }
        
        response = await test_client.post("/api/v1/profiles/", json=invalid_os_data)
        assert response.status_code == 422

    async def test_profile_fingerprint_uniqueness(self, test_client: AsyncClient, test_session: AsyncSession):
        """Test that fingerprints are unique"""
        # Create multiple profiles and generate fingerprints
        profiles = []
        for i in range(3):
            profile_data = {
                "name": f"Profile {i}",
                "auto_generate_fingerprint": True
            }
            
            response = await test_client.post("/api/v1/profiles/", json=profile_data)
            assert response.status_code == 200
            profiles.append(response.json())
        
        # Check that fingerprints are different
        fingerprints = [p["fingerprint_config"] for p in profiles]
        user_agents = [fp.get("user_agent") for fp in fingerprints]
        
        # Should have different user agents
        assert len(set(user_agents)) == len(user_agents)

    async def test_profile_error_handling(self, test_client: AsyncClient):
        """Test error handling in profile operations"""
        # Test malformed JSON
        response = await test_client.post(
            "/api/v1/profiles/",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
        
        # Test missing required fields
        response = await test_client.post("/api/v1/profiles/", json={})
        assert response.status_code == 422

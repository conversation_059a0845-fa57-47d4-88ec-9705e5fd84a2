"""
Tests for Human Behavior Simulation
"""

import pytest
import asyncio
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch

from automation.human_behavior import HumanBehavior


class TestHumanBehavior:
    """Test cases for Human Behavior simulation"""

    @pytest.fixture
    def human_behavior(self):
        """Create human behavior instance"""
        return HumanBehavior()

    async def test_human_delay_basic(self, human_behavior):
        """Test basic human delay functionality"""
        start_time = asyncio.get_event_loop().time()
        
        delay = await human_behavior.human_delay(0.1, 0.2, "test")
        
        end_time = asyncio.get_event_loop().time()
        actual_delay = end_time - start_time
        
        assert 0.1 <= delay <= 0.5  # Allow for fatigue and action modifiers
        assert actual_delay >= 0.1  # Should actually wait

    async def test_human_delay_with_fatigue(self, human_behavior):
        """Test human delay with fatigue factor"""
        # Set high fatigue
        human_behavior.fatigue_level = 0.8
        
        delay = await human_behavior.human_delay(1.0, 1.0, "general")
        
        # Should be longer due to fatigue
        assert delay > 1.0

    async def test_human_delay_action_modifiers(self, human_behavior):
        """Test action-specific delay modifiers"""
        # Test different action types
        follow_delay = await human_behavior.human_delay(1.0, 1.0, "follow")
        comment_delay = await human_behavior.human_delay(1.0, 1.0, "comment")
        scroll_delay = await human_behavior.human_delay(1.0, 1.0, "scroll")
        
        # Comment should take longer than follow, scroll should be faster
        assert comment_delay > follow_delay
        assert scroll_delay < follow_delay

    async def test_human_mouse_movement(self, human_behavior):
        """Test human-like mouse movement"""
        # Mock page and element
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_element.bounding_box.return_value = {
            "x": 100, "y": 100, "width": 50, "height": 30
        }
        
        mock_page.wait_for_selector.return_value = mock_element
        mock_page.evaluate.return_value = {"x": 50, "y": 50}
        
        result = await human_behavior.human_mouse_movement(
            mock_page, 
            '[data-testid="button"]'
        )
        
        assert result is True
        mock_page.mouse.move.assert_called()

    async def test_human_mouse_movement_element_not_found(self, human_behavior):
        """Test mouse movement when element not found"""
        mock_page = AsyncMock()
        mock_page.wait_for_selector.return_value = None
        
        result = await human_behavior.human_mouse_movement(
            mock_page,
            '[data-testid="nonexistent"]'
        )
        
        assert result is False

    async def test_human_typing(self, human_behavior):
        """Test human-like typing"""
        mock_page = AsyncMock()
        
        with patch.object(human_behavior.antidetect_config, 'get_typing_pattern') as mock_pattern:
            mock_pattern.return_value = [
                {"char": "h", "delay": 100},
                {"char": "e", "delay": 80},
                {"char": "l", "delay": 120},
                {"char": "l", "delay": 90},
                {"char": "o", "delay": 110}
            ]
            
            result = await human_behavior.human_typing(
                mock_page,
                'input[type="text"]',
                "hello"
            )
            
            assert result is True
            mock_page.focus.assert_called_once()
            mock_page.keyboard.press.assert_called()

    async def test_human_typing_with_clear(self, human_behavior):
        """Test typing with field clearing"""
        mock_page = AsyncMock()
        
        with patch.object(human_behavior.antidetect_config, 'get_typing_pattern') as mock_pattern:
            mock_pattern.return_value = [{"char": "a", "delay": 100}]
            
            await human_behavior.human_typing(
                mock_page,
                'input[type="text"]',
                "a",
                clear_first=True
            )
            
            # Should clear field first
            mock_page.keyboard.press.assert_any_call("Control+A")
            mock_page.keyboard.press.assert_any_call("Delete")

    async def test_human_scroll(self, human_behavior):
        """Test human-like scrolling"""
        mock_page = AsyncMock()
        
        with patch.object(human_behavior.antidetect_config, 'get_scroll_pattern') as mock_pattern:
            mock_pattern.return_value = [
                {"amount": 100, "delay": 50},
                {"amount": 150, "delay": 80},
                {"amount": 100, "delay": 60}
            ]
            
            result = await human_behavior.human_scroll(
                mock_page,
                direction="down",
                distance=350,
                smooth=True
            )
            
            assert result is True
            assert mock_page.mouse.wheel.call_count == 3

    async def test_human_scroll_up(self, human_behavior):
        """Test scrolling up"""
        mock_page = AsyncMock()
        
        result = await human_behavior.human_scroll(
            mock_page,
            direction="up",
            distance=200,
            smooth=False
        )
        
        assert result is True
        # Should scroll with negative distance
        mock_page.mouse.wheel.assert_called_with(0, -200)

    async def test_human_click(self, human_behavior):
        """Test human-like clicking"""
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_element.bounding_box.return_value = {
            "x": 100, "y": 100, "width": 50, "height": 30
        }
        
        mock_page.wait_for_selector.return_value = mock_element
        
        with patch.object(human_behavior, 'human_mouse_movement', return_value=True):
            result = await human_behavior.human_click(
                mock_page,
                'button[type="submit"]'
            )
            
            assert result is True
            mock_page.mouse.click.assert_called()

    async def test_human_click_without_hover(self, human_behavior):
        """Test clicking without hover"""
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_element.bounding_box.return_value = {
            "x": 100, "y": 100, "width": 50, "height": 30
        }
        mock_element.click = AsyncMock()
        
        mock_page.wait_for_selector.return_value = mock_element
        
        result = await human_behavior.human_click(
            mock_page,
            'button[type="submit"]',
            hover_first=False
        )
        
        assert result is True

    async def test_simulate_reading(self, human_behavior):
        """Test reading simulation"""
        start_time = asyncio.get_event_loop().time()
        
        reading_time = await human_behavior.simulate_reading(100)
        
        end_time = asyncio.get_event_loop().time()
        actual_time = end_time - start_time
        
        assert reading_time >= 1.0  # Minimum reading time
        assert actual_time >= reading_time * 0.9  # Should actually wait

    async def test_simulate_reading_long_content(self, human_behavior):
        """Test reading simulation with long content"""
        long_content_length = 1000
        
        reading_time = await human_behavior.simulate_reading(long_content_length)
        
        # Should take longer for more content
        assert reading_time > 5.0

    async def test_take_break(self, human_behavior):
        """Test taking a break"""
        # Set high fatigue
        human_behavior.fatigue_level = 0.8
        initial_fatigue = human_behavior.fatigue_level
        
        with patch.object(human_behavior.antidetect_config, 'get_break_duration', return_value=0.1):
            break_duration = await human_behavior.take_break("fatigue")
            
            assert break_duration == 0.1
            # Fatigue should be reduced
            assert human_behavior.fatigue_level < initial_fatigue

    def test_should_take_break_high_fatigue(self, human_behavior):
        """Test break decision with high fatigue"""
        human_behavior.fatigue_level = 0.8
        
        should_break = human_behavior.should_take_break()
        assert should_break is True

    def test_should_take_break_long_session(self, human_behavior):
        """Test break decision with long session"""
        # Simulate long session
        human_behavior.attention_span = 300  # 5 minutes
        human_behavior.session_start_time = human_behavior.session_start_time.replace(
            minute=human_behavior.session_start_time.minute - 10
        )
        
        should_break = human_behavior.should_take_break()
        assert should_break is True

    def test_should_take_break_fresh_session(self, human_behavior):
        """Test break decision with fresh session"""
        human_behavior.fatigue_level = 0.1
        
        should_break = human_behavior.should_take_break()
        # Might be True due to random factor, but usually False
        assert isinstance(should_break, bool)

    def test_update_fatigue(self, human_behavior):
        """Test fatigue update"""
        initial_fatigue = human_behavior.fatigue_level
        
        human_behavior._update_fatigue()
        
        # Fatigue should increase
        assert human_behavior.fatigue_level > initial_fatigue
        # Should not exceed 1.0
        assert human_behavior.fatigue_level <= 1.0

    def test_generate_natural_mouse_path(self, human_behavior):
        """Test natural mouse path generation"""
        path = human_behavior._generate_natural_mouse_path(0, 0, 100, 100, 5)
        
        assert len(path) == 6  # num_points + 1
        assert path[0] == (0, 0)  # Start point
        assert path[-1] == (100, 100)  # End point
        
        # Path should have some variation
        x_coords = [point[0] for point in path]
        y_coords = [point[1] for point in path]
        
        # Should be generally increasing
        assert x_coords[-1] > x_coords[0]
        assert y_coords[-1] > y_coords[0]

    def test_generate_direct_mouse_path(self, human_behavior):
        """Test direct mouse path generation"""
        path = human_behavior._generate_direct_mouse_path(0, 0, 100, 100, 3)
        
        assert len(path) == 4  # num_points + 1
        assert path[0] == (0, 0)
        assert path[-1] == (100, 100)
        
        # Should be a straight line
        for i, (x, y) in enumerate(path):
            expected_x = i * 100 / 3
            expected_y = i * 100 / 3
            assert abs(x - expected_x) < 0.1
            assert abs(y - expected_y) < 0.1

    def test_log_action(self, human_behavior):
        """Test action logging"""
        initial_count = len(human_behavior.action_history)
        
        human_behavior.log_action("follow", "@testuser", "success")
        
        assert len(human_behavior.action_history) == initial_count + 1
        
        last_action = human_behavior.action_history[-1]
        assert last_action["action_type"] == "follow"
        assert last_action["target"] == "@testuser"
        assert last_action["result"] == "success"

    def test_action_history_limit(self, human_behavior):
        """Test action history size limit"""
        # Add many actions
        for i in range(150):
            human_behavior.log_action("test", f"target_{i}", "success")
        
        # Should be limited to 100
        assert len(human_behavior.action_history) == 100
        
        # Should keep the most recent ones
        last_action = human_behavior.action_history[-1]
        assert last_action["target"] == "target_149"

    async def test_typing_speed_variation(self, human_behavior):
        """Test typing speed affects delay"""
        # Test with different typing speeds
        human_behavior.typing_speed_wpm = 30  # Slow
        
        mock_page = AsyncMock()
        
        with patch.object(human_behavior.antidetect_config, 'get_typing_pattern') as mock_pattern:
            mock_pattern.return_value = [{"char": "a", "delay": 100}]
            
            start_time = asyncio.get_event_loop().time()
            await human_behavior.human_typing(mock_page, 'input', "a")
            end_time = asyncio.get_event_loop().time()
            
            slow_duration = end_time - start_time
            
        # Test with fast typing
        human_behavior.typing_speed_wpm = 80  # Fast
        
        with patch.object(human_behavior.antidetect_config, 'get_typing_pattern') as mock_pattern:
            mock_pattern.return_value = [{"char": "a", "delay": 100}]
            
            start_time = asyncio.get_event_loop().time()
            await human_behavior.human_typing(mock_page, 'input', "a")
            end_time = asyncio.get_event_loop().time()
            
            fast_duration = end_time - start_time
        
        # Slow typing should take longer
        assert slow_duration > fast_duration

    async def test_mouse_speed_variation(self, human_behavior):
        """Test mouse speed affects movement"""
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_element.bounding_box.return_value = {
            "x": 100, "y": 100, "width": 50, "height": 30
        }
        
        mock_page.wait_for_selector.return_value = mock_element
        mock_page.evaluate.return_value = {"x": 0, "y": 0}
        
        # Test with different mouse speeds
        human_behavior.mouse_speed = 0.5  # Slow
        
        start_time = asyncio.get_event_loop().time()
        await human_behavior.human_mouse_movement(mock_page, 'button')
        end_time = asyncio.get_event_loop().time()
        
        slow_duration = end_time - start_time
        
        human_behavior.mouse_speed = 2.0  # Fast
        
        start_time = asyncio.get_event_loop().time()
        await human_behavior.human_mouse_movement(mock_page, 'button')
        end_time = asyncio.get_event_loop().time()
        
        fast_duration = end_time - start_time
        
        # Slow mouse should take longer
        assert slow_duration > fast_duration

"""
Pytest configuration and fixtures
"""

import asyncio
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from httpx import AsyncClient
from fastapi.testclient import TestClient

from main import app
from core.database import Base, get_async_session
from core.config import settings
from models.browser_profile import BrowserProfile
from models.proxy import Proxy, ProxyType
from models.tiktok_account import TikTokAccount
from models.follow_task import FollowTask, TaskType, TaskStatus


# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def test_engine():
    """Create test database engine"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        future=True
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Drop tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_engine):
    """Create test database session"""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session


@pytest_asyncio.fixture
async def test_client(test_session):
    """Create test client with database override"""
    
    async def override_get_session():
        yield test_session
    
    app.dependency_overrides[get_async_session] = override_get_session
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
def sync_test_client():
    """Create synchronous test client for simple tests"""
    return TestClient(app)


# Model fixtures
@pytest_asyncio.fixture
async def sample_browser_profile(test_session):
    """Create a sample browser profile"""
    profile = BrowserProfile(
        name="Test Profile",
        description="Test browser profile",
        fingerprint_config={
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "screen_resolution": "1920x1080",
            "timezone": "America/New_York"
        },
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        timezone="America/New_York",
        locale="en-US"
    )
    
    test_session.add(profile)
    await test_session.commit()
    await test_session.refresh(profile)
    
    return profile


@pytest_asyncio.fixture
async def sample_proxy(test_session):
    """Create a sample proxy"""
    proxy = Proxy(
        name="Test Proxy",
        proxy_type=ProxyType.HTTP,
        host="127.0.0.1",
        port=8080,
        username="testuser",
        password="testpass",
        description="Test proxy server"
    )
    
    test_session.add(proxy)
    await test_session.commit()
    await test_session.refresh(proxy)
    
    return proxy


@pytest_asyncio.fixture
async def sample_tiktok_account(test_session, sample_browser_profile):
    """Create a sample TikTok account"""
    account = TikTokAccount(
        username="testuser123",
        display_name="Test User",
        email="<EMAIL>",
        browser_profile_id=sample_browser_profile.id
    )
    
    test_session.add(account)
    await test_session.commit()
    await test_session.refresh(account)
    
    return account


@pytest_asyncio.fixture
async def sample_follow_task(test_session, sample_tiktok_account):
    """Create a sample follow task"""
    task = FollowTask(
        name="Test Follow Task",
        description="Test automation task",
        task_type=TaskType.FOLLOW_FOLLOWERS,
        tiktok_account_id=sample_tiktok_account.id,
        target_count=100,
        delay_min=2,
        delay_max=5
    )
    
    test_session.add(task)
    await test_session.commit()
    await test_session.refresh(task)
    
    return task


# Mock data fixtures
@pytest.fixture
def mock_browser_profile_data():
    """Mock browser profile data for API tests"""
    return {
        "name": "API Test Profile",
        "description": "Profile created via API test",
        "auto_generate_fingerprint": True,
        "os_preference": "windows",
        "browser_preference": "firefox"
    }


@pytest.fixture
def mock_proxy_data():
    """Mock proxy data for API tests"""
    return {
        "name": "API Test Proxy",
        "proxy_type": "http",
        "host": "*************",
        "port": 3128,
        "username": "apitest",
        "password": "testpass123",
        "description": "Proxy created via API test"
    }


@pytest.fixture
def mock_tiktok_account_data():
    """Mock TikTok account data for API tests"""
    return {
        "username": "apitestuser",
        "display_name": "API Test User",
        "email": "<EMAIL>"
    }


@pytest.fixture
def mock_task_data():
    """Mock task data for API tests"""
    return {
        "name": "API Test Task",
        "task_type": "follow_followers",
        "target_count": 50,
        "delay_min": 1,
        "delay_max": 3,
        "description": "Task created via API test"
    }


# Utility fixtures
@pytest.fixture
def mock_cookies():
    """Mock TikTok cookies for testing"""
    return [
        {
            "name": "sessionid",
            "value": "test_session_id_123",
            "domain": ".tiktok.com",
            "path": "/",
            "secure": True,
            "httpOnly": True,
            "expires": **********  # Future timestamp
        },
        {
            "name": "csrf_token",
            "value": "test_csrf_token_456",
            "domain": ".tiktok.com",
            "path": "/",
            "secure": True,
            "httpOnly": False,
            "expires": **********
        }
    ]


@pytest.fixture
def mock_fingerprint_config():
    """Mock fingerprint configuration"""
    return {
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "screen_resolution": "1920x1080",
        "color_depth": 24,
        "timezone": "America/New_York",
        "language": "en-US",
        "platform": "Win32",
        "do_not_track": "1",
        "canvas_fingerprint": "test_canvas_hash",
        "webgl_fingerprint": "test_webgl_hash",
        "audio_fingerprint": "test_audio_hash"
    }


# Test environment setup
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables"""
    monkeypatch.setenv("ENVIRONMENT", "testing")
    monkeypatch.setenv("DATABASE_URL", TEST_DATABASE_URL)
    monkeypatch.setenv("SECRET_KEY", "test_secret_key_for_testing_only")
    monkeypatch.setenv("DEBUG", "true")


# Cleanup fixtures
@pytest.fixture(autouse=True)
async def cleanup_after_test():
    """Cleanup after each test"""
    yield
    # Any cleanup code here
    pass


# Performance testing fixtures
@pytest.fixture
def performance_threshold():
    """Performance thresholds for testing"""
    return {
        "api_response_time": 1.0,  # seconds
        "database_query_time": 0.5,  # seconds
        "memory_usage_mb": 100  # MB
    }

"""
Integration Tests for TikTok Automation System
Tests the complete system with browserscan.net and real scenarios
"""

import asyncio
import pytest
from typing import Dict, Any
from datetime import datetime
from loguru import logger

from services.integrated_automation_service import integrated_automation_service
from services.monitoring_service import monitoring_service
from models.browser_profile import Browser<PERSON>rofile
from models.tiktok_account import T<PERSON><PERSON><PERSON><PERSON><PERSON>unt
from core.database import get_async_session
from camoufox_integration.antidetect_config import AntidetectConfig


class TestIntegratedSystem:
    """Test suite for integrated automation system"""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """Setup and teardown for each test"""
        
        # Setup
        await integrated_automation_service.initialize()
        yield
        
        # Teardown
        await integrated_automation_service.shutdown()
    
    async def test_browserscan_detection(self):
        """Test anti-detection capabilities with browserscan.net"""
        
        try:
            logger.info("Testing anti-detection with browserscan.net")
            
            # Create test profile
            test_profile = await self._create_test_profile()
            
            # Start automation session
            session_result = await integrated_automation_service.start_automation_session(
                profile_id=test_profile.id,
                automation_type="test"
            )
            
            assert session_result["success"], f"Failed to start session: {session_result.get('error')}"
            
            # Get browser context
            session_info = integrated_automation_service.active_sessions[test_profile.id]
            context = session_info["context"]
            
            # Test browserscan.net
            page = await context.new_page()
            
            # Navigate to browserscan.net
            await page.goto("https://www.browserscan.net/", timeout=30000)
            await page.wait_for_load_state("networkidle")
            
            # Wait for scan to complete
            await asyncio.sleep(10)
            
            # Check for detection indicators
            detection_indicators = [
                "Automation detected",
                "Bot detected", 
                "Headless browser",
                "WebDriver detected",
                "Suspicious"
            ]
            
            page_content = await page.content()
            detected = False
            
            for indicator in detection_indicators:
                if indicator.lower() in page_content.lower():
                    detected = True
                    logger.warning(f"Detection indicator found: {indicator}")
                    break
            
            # Take screenshot for analysis
            screenshot_path = f"test_browserscan_{int(datetime.utcnow().timestamp())}.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"Screenshot saved: {screenshot_path}")
            
            await page.close()
            
            # Stop session
            await integrated_automation_service.stop_automation_session(test_profile.id)
            
            # Assert no detection
            assert not detected, "Anti-detection failed - bot detected by browserscan.net"
            
            logger.info("✅ Browserscan.net test passed - no detection")
            
        except Exception as e:
            logger.error(f"Error in browserscan test: {e}")
            raise
    
    async def test_tiktok_navigation(self):
        """Test TikTok navigation and basic functionality"""
        
        try:
            logger.info("Testing TikTok navigation")
            
            # Create test profile
            test_profile = await self._create_test_profile()
            
            # Start automation session
            session_result = await integrated_automation_service.start_automation_session(
                profile_id=test_profile.id,
                automation_type="test"
            )
            
            assert session_result["success"], f"Failed to start session: {session_result.get('error')}"
            
            # Get browser context
            session_info = integrated_automation_service.active_sessions[test_profile.id]
            context = session_info["context"]
            
            # Test TikTok navigation
            page = await context.new_page()
            
            # Navigate to TikTok
            response = await page.goto("https://www.tiktok.com/", timeout=30000)
            assert response.status == 200, f"Failed to load TikTok: {response.status}"
            
            await page.wait_for_load_state("networkidle")
            
            # Check for basic TikTok elements
            tiktok_indicators = [
                "[data-e2e='nav-logo']",
                "[data-e2e='search-box']",
                ".video-feed",
                "[data-e2e='recommend-list-item']"
            ]
            
            elements_found = 0
            for selector in tiktok_indicators:
                element = await page.query_selector(selector)
                if element:
                    elements_found += 1
            
            # Check for CAPTCHA or blocks
            detection_event = await monitoring_service.check_for_detection(
                page, test_profile.id
            )
            
            await page.close()
            
            # Stop session
            await integrated_automation_service.stop_automation_session(test_profile.id)
            
            # Assert successful navigation
            assert elements_found > 0, "No TikTok elements found - possible block or CAPTCHA"
            assert detection_event is None, f"Detection event found: {detection_event.detection_type if detection_event else None}"
            
            logger.info(f"✅ TikTok navigation test passed - {elements_found} elements found")
            
        except Exception as e:
            logger.error(f"Error in TikTok navigation test: {e}")
            raise
    
    async def test_persistent_session_cookies(self):
        """Test persistent session cookie management"""
        
        try:
            logger.info("Testing persistent session cookies")
            
            # Create test profile and account
            test_profile = await self._create_test_profile()
            test_account = await self._create_test_account()
            
            # Start automation session
            session_result = await integrated_automation_service.start_automation_session(
                profile_id=test_profile.id,
                account_id=test_account.id,
                automation_type="test"
            )
            
            assert session_result["success"], f"Failed to start session: {session_result.get('error')}"
            
            # Get browser context
            session_info = integrated_automation_service.active_sessions[test_profile.id]
            context = session_info["context"]
            
            # Navigate to TikTok and set some test cookies
            page = await context.new_page()
            await page.goto("https://www.tiktok.com/", timeout=30000)
            
            # Add test cookies
            test_cookies = [
                {
                    "name": "test_cookie_1",
                    "value": "test_value_1",
                    "domain": ".tiktok.com",
                    "path": "/"
                },
                {
                    "name": "test_cookie_2", 
                    "value": "test_value_2",
                    "domain": ".tiktok.com",
                    "path": "/"
                }
            ]
            
            await context.add_cookies(test_cookies)
            
            # Sync cookies
            sync_result = await integrated_automation_service.browser_pool.sync_persistent_session_cookies(
                session_info["instance_id"],
                test_account.id
            )
            
            assert sync_result, "Failed to sync cookies"
            
            await page.close()
            
            # Stop session (this should save state)
            await integrated_automation_service.stop_automation_session(test_profile.id, save_state=True)
            
            # Start new session to test persistence
            session_result2 = await integrated_automation_service.start_automation_session(
                profile_id=test_profile.id,
                account_id=test_account.id,
                automation_type="test"
            )
            
            assert session_result2["success"], f"Failed to start second session: {session_result2.get('error')}"
            
            # Check if cookies persisted
            session_info2 = integrated_automation_service.active_sessions[test_profile.id]
            context2 = session_info2["context"]
            
            page2 = await context2.new_page()
            await page2.goto("https://www.tiktok.com/", timeout=30000)
            
            # Get cookies
            cookies = await context2.cookies()
            
            # Check if test cookies exist
            test_cookie_names = [cookie["name"] for cookie in test_cookies]
            found_cookies = [cookie["name"] for cookie in cookies if cookie["name"] in test_cookie_names]
            
            await page2.close()
            
            # Stop second session
            await integrated_automation_service.stop_automation_session(test_profile.id)
            
            # Assert cookies persisted
            assert len(found_cookies) > 0, f"Test cookies not persisted. Found: {found_cookies}"
            
            logger.info(f"✅ Cookie persistence test passed - {len(found_cookies)} cookies persisted")
            
        except Exception as e:
            logger.error(f"Error in cookie persistence test: {e}")
            raise
    
    async def test_follow_workflow(self):
        """Test the complete follow workflow"""
        
        try:
            logger.info("Testing follow workflow")
            
            # Create test profile
            test_profile = await self._create_test_profile()
            
            # Start automation session
            session_result = await integrated_automation_service.start_automation_session(
                profile_id=test_profile.id,
                automation_type="follow"
            )
            
            assert session_result["success"], f"Failed to start session: {session_result.get('error')}"
            
            # Test follow workflow with a known public account
            test_usernames = ["tiktok"]  # Official TikTok account
            
            follow_result = await integrated_automation_service.execute_follow_automation(
                profile_id=test_profile.id,
                target_usernames=test_usernames,
                pattern="conservative",
                mobile_mode=False
            )
            
            # Stop session
            await integrated_automation_service.stop_automation_session(test_profile.id)
            
            # Assert workflow executed
            assert follow_result["success"], f"Follow workflow failed: {follow_result.get('error')}"
            assert follow_result["total_users"] == 1, "Incorrect number of users processed"
            
            # Check if workflow completed without detection
            results = follow_result["results"]
            assert len(results) == 1, "No results returned"
            
            # Note: We don't assert success because we might not be logged in
            # But we should assert that the workflow executed without errors
            result = results[0]
            logger.info(f"Follow result for {result['username']}: {result['result']}")
            
            logger.info("✅ Follow workflow test completed")
            
        except Exception as e:
            logger.error(f"Error in follow workflow test: {e}")
            raise
    
    async def test_monitoring_system(self):
        """Test monitoring and detection system"""
        
        try:
            logger.info("Testing monitoring system")
            
            # Create test profile
            test_profile = await self._create_test_profile()
            
            # Start automation session
            session_result = await integrated_automation_service.start_automation_session(
                profile_id=test_profile.id,
                automation_type="test"
            )
            
            assert session_result["success"], f"Failed to start session: {session_result.get('error')}"
            
            # Record some test performance metrics
            await monitoring_service.record_performance_metric(
                profile_id=test_profile.id,
                action_type="test_action",
                duration=5.0,
                success=True
            )
            
            await monitoring_service.record_performance_metric(
                profile_id=test_profile.id,
                action_type="test_action",
                duration=2.0,
                success=True
            )
            
            # Get health status
            health_status = await monitoring_service.get_profile_health_status(test_profile.id)
            
            # Get system overview
            system_overview = await monitoring_service.get_system_health_overview()
            
            # Stop session
            await integrated_automation_service.stop_automation_session(test_profile.id)
            
            # Assert monitoring data
            assert health_status["profile_id"] == test_profile.id
            assert health_status["health_status"] in ["healthy", "warning", "critical"]
            assert health_status["recent_metrics"] >= 2
            
            assert "system_health" in system_overview
            assert "total_profiles" in system_overview
            
            logger.info(f"✅ Monitoring system test passed - Health: {health_status['health_status']}")
            
        except Exception as e:
            logger.error(f"Error in monitoring system test: {e}")
            raise
    
    async def test_service_overview(self):
        """Test service overview functionality"""
        
        try:
            logger.info("Testing service overview")
            
            # Get service overview
            overview = await integrated_automation_service.get_service_overview()
            
            # Assert overview structure
            assert "service_running" in overview
            assert "initialization_complete" in overview
            assert "active_sessions" in overview
            assert "system_health" in overview
            
            assert overview["service_running"] is True
            assert overview["initialization_complete"] is True
            
            logger.info("✅ Service overview test passed")
            
        except Exception as e:
            logger.error(f"Error in service overview test: {e}")
            raise
    
    async def _create_test_profile(self) -> BrowserProfile:
        """Create a test browser profile"""
        
        async with get_async_session() as session:
            # Create test profile
            profile = BrowserProfile(
                name=f"test_profile_{int(datetime.utcnow().timestamp())}",
                description="Test profile for integration tests",
                fingerprint_config={},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                is_active=True
            )
            
            session.add(profile)
            await session.commit()
            await session.refresh(profile)
            
            return profile
    
    async def _create_test_account(self) -> TikTokAccount:
        """Create a test TikTok account"""
        
        async with get_async_session() as session:
            # Create test account
            account = TikTokAccount(
                username=f"test_account_{int(datetime.utcnow().timestamp())}",
                email="<EMAIL>",
                is_active=True,
                is_logged_in=False
            )
            
            session.add(account)
            await session.commit()
            await session.refresh(account)
            
            return account


# Test runner function
async def run_integration_tests():
    """Run all integration tests"""
    
    logger.info("🚀 Starting Integration Tests")
    
    test_suite = TestIntegratedSystem()
    
    tests = [
        ("Browserscan Detection Test", test_suite.test_browserscan_detection),
        ("TikTok Navigation Test", test_suite.test_tiktok_navigation),
        ("Persistent Session Cookies Test", test_suite.test_persistent_session_cookies),
        ("Follow Workflow Test", test_suite.test_follow_workflow),
        ("Monitoring System Test", test_suite.test_monitoring_system),
        ("Service Overview Test", test_suite.test_service_overview)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            logger.info(f"🧪 Running: {test_name}")
            
            # Setup
            await integrated_automation_service.initialize()
            
            # Run test
            await test_func()
            
            # Teardown
            await integrated_automation_service.shutdown()
            
            results.append({"test": test_name, "status": "PASSED"})
            logger.info(f"✅ {test_name} PASSED")
            
        except Exception as e:
            results.append({"test": test_name, "status": "FAILED", "error": str(e)})
            logger.error(f"❌ {test_name} FAILED: {e}")
            
            # Ensure cleanup
            try:
                await integrated_automation_service.shutdown()
            except:
                pass
    
    # Print summary
    passed = sum(1 for r in results if r["status"] == "PASSED")
    failed = sum(1 for r in results if r["status"] == "FAILED")
    
    logger.info(f"\n📊 Test Results Summary:")
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    logger.info(f"📈 Success Rate: {(passed / len(results)) * 100:.1f}%")
    
    for result in results:
        status_emoji = "✅" if result["status"] == "PASSED" else "❌"
        logger.info(f"{status_emoji} {result['test']}: {result['status']}")
        if result["status"] == "FAILED":
            logger.error(f"   Error: {result.get('error', 'Unknown error')}")
    
    return results


if __name__ == "__main__":
    # Run tests
    asyncio.run(run_integration_tests())

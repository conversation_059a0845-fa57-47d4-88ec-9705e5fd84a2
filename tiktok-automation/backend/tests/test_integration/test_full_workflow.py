"""
Integration tests for full automation workflow
"""

import pytest
import asyncio
from httpx import As<PERSON><PERSON><PERSON>
from unittest.mock import AsyncMock, patch

from models.browser_profile import BrowserProfile
from models.proxy import Proxy, ProxyType
from models.tiktok_account import TikTokAccount
from models.follow_task import FollowTask, TaskType, TaskStatus


class TestFullWorkflow:
    """Integration tests for complete automation workflow"""

    async def test_complete_automation_workflow(self, test_client: AsyncClient, test_session):
        """Test complete workflow from profile creation to task execution"""
        
        # Step 1: Create browser profile
        profile_data = {
            "name": "Integration Test Profile",
            "description": "Profile for integration testing",
            "auto_generate_fingerprint": True,
            "os_preference": "windows",
            "browser_preference": "chrome"
        }
        
        profile_response = await test_client.post("/api/v1/profiles/", json=profile_data)
        assert profile_response.status_code == 200
        profile = profile_response.json()
        profile_id = profile["id"]
        
        # Step 2: Create proxy
        proxy_data = {
            "name": "Integration Test Proxy",
            "proxy_type": "http",
            "host": "127.0.0.1",
            "port": 8080,
            "username": "testuser",
            "password": "testpass"
        }
        
        proxy_response = await test_client.post("/api/v1/proxies/", json=proxy_data)
        assert proxy_response.status_code == 200
        proxy = proxy_response.json()
        proxy_id = proxy["id"]
        
        # Step 3: Link proxy to profile
        link_response = await test_client.post(f"/api/v1/profiles/{profile_id}/link-proxy/{proxy_id}")
        assert link_response.status_code == 200
        
        # Step 4: Create TikTok account
        account_data = {
            "username": "integration_test_user",
            "display_name": "Integration Test User",
            "email": "<EMAIL>",
            "browser_profile_id": profile_id
        }
        
        account_response = await test_client.post("/api/v1/accounts/", json=account_data)
        assert account_response.status_code == 200
        account = account_response.json()
        account_id = account["id"]
        
        # Step 5: Mock login process
        with patch('services.account_service.AccountService.login_account') as mock_login:
            mock_login.return_value = {
                "success": True,
                "message": "Login successful",
                "cookies_saved": True
            }
            
            login_response = await test_client.post(
                f"/api/v1/accounts/{account_id}/login",
                json={"manual_login": True, "save_cookies": True}
            )
            assert login_response.status_code == 200
            assert login_response.json()["success"] is True
        
        # Step 6: Create automation task
        task_data = {
            "name": "Integration Test Task",
            "task_type": "follow_followers",
            "tiktok_account_id": account_id,
            "target_count": 10,
            "delay_min": 1,
            "delay_max": 2,
            "description": "Integration test automation task"
        }
        
        task_response = await test_client.post("/api/v1/tasks/", json=task_data)
        assert task_response.status_code == 200
        task = task_response.json()
        task_id = task["id"]
        
        # Step 7: Start task
        with patch('services.task_service.TaskService.start_task') as mock_start:
            mock_start.return_value = {
                "success": True,
                "message": "Task started successfully"
            }
            
            start_response = await test_client.post(f"/api/v1/tasks/{task_id}/start")
            assert start_response.status_code == 200
            assert start_response.json()["success"] is True
        
        # Step 8: Check task status
        status_response = await test_client.get(f"/api/v1/tasks/{task_id}")
        assert status_response.status_code == 200
        task_status = status_response.json()
        assert task_status["id"] == task_id
        
        # Step 9: Get task progress
        progress_response = await test_client.get(f"/api/v1/tasks/{task_id}/progress")
        assert progress_response.status_code == 200
        progress = progress_response.json()
        assert "progress_percentage" in progress
        
        # Step 10: Stop task
        with patch('services.task_service.TaskService.stop_task') as mock_stop:
            mock_stop.return_value = {
                "success": True,
                "message": "Task stopped successfully"
            }
            
            stop_response = await test_client.post(f"/api/v1/tasks/{task_id}/stop")
            assert stop_response.status_code == 200
            assert stop_response.json()["success"] is True

    async def test_profile_proxy_integration(self, test_client: AsyncClient):
        """Test profile and proxy integration"""
        
        # Create profile
        profile_data = {
            "name": "Proxy Test Profile",
            "auto_generate_fingerprint": True
        }
        
        profile_response = await test_client.post("/api/v1/profiles/", json=profile_data)
        profile_id = profile_response.json()["id"]
        
        # Create multiple proxies
        proxy_ids = []
        for i in range(3):
            proxy_data = {
                "name": f"Test Proxy {i+1}",
                "proxy_type": "http",
                "host": f"192.168.1.{i+1}",
                "port": 8080 + i
            }
            
            proxy_response = await test_client.post("/api/v1/proxies/", json=proxy_data)
            proxy_ids.append(proxy_response.json()["id"])
        
        # Test proxy linking
        for proxy_id in proxy_ids:
            link_response = await test_client.post(f"/api/v1/profiles/{profile_id}/link-proxy/{proxy_id}")
            assert link_response.status_code == 200
        
        # Test proxy rotation
        rotate_response = await test_client.post(f"/api/v1/profiles/{profile_id}/rotate-proxy")
        assert rotate_response.status_code == 200
        
        # Verify profile has proxy assigned
        profile_response = await test_client.get(f"/api/v1/profiles/{profile_id}")
        profile = profile_response.json()
        assert profile["proxy_id"] is not None

    async def test_account_cookie_workflow(self, test_client: AsyncClient):
        """Test account and cookie management workflow"""
        
        # Create profile and account
        profile_data = {"name": "Cookie Test Profile"}
        profile_response = await test_client.post("/api/v1/profiles/", json=profile_data)
        profile_id = profile_response.json()["id"]
        
        account_data = {
            "username": "cookie_test_user",
            "browser_profile_id": profile_id
        }
        account_response = await test_client.post("/api/v1/accounts/", json=account_data)
        account_id = account_response.json()["id"]
        
        # Mock cookie operations
        mock_cookies = [
            {
                "name": "sessionid",
                "value": "test_session_123",
                "domain": ".tiktok.com"
            }
        ]
        
        # Test cookie import
        with patch('services.cookie_service.CookieService.import_cookies') as mock_import:
            mock_import.return_value = True
            
            import_response = await test_client.post(
                f"/api/v1/accounts/{account_id}/cookies/import",
                json={
                    "cookies_data": '{"test": "data"}',
                    "format": "json"
                }
            )
            assert import_response.status_code == 200
        
        # Test cookie validation
        with patch('services.cookie_service.CookieService.validate_cookies') as mock_validate:
            mock_validate.return_value = {"valid": True, "status_code": 200}
            
            validate_response = await test_client.post(f"/api/v1/accounts/{account_id}/cookies/validate")
            assert validate_response.status_code == 200
            assert validate_response.json()["valid"] is True
        
        # Test cookie export
        with patch('services.cookie_service.CookieService.export_cookies') as mock_export:
            mock_export.return_value = '{"exported": "data"}'
            
            export_response = await test_client.post(
                f"/api/v1/accounts/{account_id}/cookies/export",
                json={"format": "json"}
            )
            assert export_response.status_code == 200
            assert "data" in export_response.json()

    async def test_task_lifecycle_management(self, test_client: AsyncClient, test_session):
        """Test complete task lifecycle"""
        
        # Setup prerequisites
        profile = BrowserProfile(name="Task Test Profile")
        test_session.add(profile)
        await test_session.commit()
        await test_session.refresh(profile)
        
        account = TikTokAccount(
            username="task_test_user",
            browser_profile_id=profile.id
        )
        test_session.add(account)
        await test_session.commit()
        await test_session.refresh(account)
        
        # Create task
        task_data = {
            "name": "Lifecycle Test Task",
            "task_type": "follow_followers",
            "tiktok_account_id": account.id,
            "target_count": 5,
            "delay_min": 1,
            "delay_max": 2
        }
        
        task_response = await test_client.post("/api/v1/tasks/", json=task_data)
        task_id = task_response.json()["id"]
        
        # Test task states
        with patch('services.task_service.TaskService.start_task') as mock_start:
            mock_start.return_value = {"success": True}
            
            # Start task
            start_response = await test_client.post(f"/api/v1/tasks/{task_id}/start")
            assert start_response.status_code == 200
        
        with patch('services.task_service.TaskService.pause_task') as mock_pause:
            mock_pause.return_value = {"success": True}
            
            # Pause task
            pause_response = await test_client.post(f"/api/v1/tasks/{task_id}/pause")
            assert pause_response.status_code == 200
        
        with patch('services.task_service.TaskService.resume_task') as mock_resume:
            mock_resume.return_value = {"success": True}
            
            # Resume task
            resume_response = await test_client.post(f"/api/v1/tasks/{task_id}/resume")
            assert resume_response.status_code == 200
        
        with patch('services.task_service.TaskService.stop_task') as mock_stop:
            mock_stop.return_value = {"success": True}
            
            # Stop task
            stop_response = await test_client.post(f"/api/v1/tasks/{task_id}/stop")
            assert stop_response.status_code == 200

    async def test_system_health_monitoring(self, test_client: AsyncClient):
        """Test system health monitoring endpoints"""
        
        # Test health check
        health_response = await test_client.get("/health")
        assert health_response.status_code == 200
        health_data = health_response.json()
        assert "status" in health_data
        
        # Test system status
        with patch('services.profile_service.ProfileService.get_profile_statistics') as mock_stats:
            mock_stats.return_value = {"total": 5, "active": 3}
            
            status_response = await test_client.get("/api/v1/system/status")
            assert status_response.status_code == 200
            status_data = status_response.json()
            assert "memory_usage" in status_data

    async def test_error_handling_workflow(self, test_client: AsyncClient):
        """Test error handling throughout the workflow"""
        
        # Test creating account with invalid profile
        invalid_account_data = {
            "username": "error_test_user",
            "browser_profile_id": 999  # Non-existent profile
        }
        
        account_response = await test_client.post("/api/v1/accounts/", json=invalid_account_data)
        assert account_response.status_code == 400
        
        # Test creating task with invalid account
        invalid_task_data = {
            "name": "Error Test Task",
            "task_type": "follow_followers",
            "tiktok_account_id": 999,  # Non-existent account
            "target_count": 10
        }
        
        task_response = await test_client.post("/api/v1/tasks/", json=invalid_task_data)
        assert task_response.status_code == 400

    async def test_concurrent_operations(self, test_client: AsyncClient):
        """Test concurrent operations handling"""
        
        # Create profile
        profile_data = {"name": "Concurrent Test Profile"}
        profile_response = await test_client.post("/api/v1/profiles/", json=profile_data)
        profile_id = profile_response.json()["id"]
        
        # Test concurrent profile updates
        update_tasks = []
        for i in range(5):
            update_data = {"description": f"Updated description {i}"}
            task = test_client.put(f"/api/v1/profiles/{profile_id}", json=update_data)
            update_tasks.append(task)
        
        # Execute concurrent updates
        responses = await asyncio.gather(*update_tasks, return_exceptions=True)
        
        # All should succeed or handle gracefully
        for response in responses:
            if not isinstance(response, Exception):
                assert response.status_code in [200, 409]  # Success or conflict

    async def test_data_consistency(self, test_client: AsyncClient, test_session):
        """Test data consistency across operations"""
        
        # Create related entities
        profile_data = {"name": "Consistency Test Profile"}
        profile_response = await test_client.post("/api/v1/profiles/", json=profile_data)
        profile_id = profile_response.json()["id"]
        
        account_data = {
            "username": "consistency_test_user",
            "browser_profile_id": profile_id
        }
        account_response = await test_client.post("/api/v1/accounts/", json=account_data)
        account_id = account_response.json()["id"]
        
        # Verify relationships
        account_detail = await test_client.get(f"/api/v1/accounts/{account_id}")
        assert account_detail.json()["browser_profile_id"] == profile_id
        
        # Test cascade operations
        delete_response = await test_client.delete(f"/api/v1/profiles/{profile_id}")
        
        # Account should handle profile deletion gracefully
        account_after_delete = await test_client.get(f"/api/v1/accounts/{account_id}")
        if account_after_delete.status_code == 200:
            # If account still exists, profile_id should be None or handled
            account_data = account_after_delete.json()
            # Implementation dependent - could be None or default profile

    async def test_performance_under_load(self, test_client: AsyncClient):
        """Test system performance under load"""
        
        # Create multiple entities rapidly
        start_time = asyncio.get_event_loop().time()
        
        # Create 10 profiles concurrently
        profile_tasks = []
        for i in range(10):
            profile_data = {"name": f"Load Test Profile {i}"}
            task = test_client.post("/api/v1/profiles/", json=profile_data)
            profile_tasks.append(task)
        
        responses = await asyncio.gather(*profile_tasks)
        end_time = asyncio.get_event_loop().time()
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
        
        # Should complete within reasonable time (adjust threshold as needed)
        duration = end_time - start_time
        assert duration < 10.0  # 10 seconds threshold

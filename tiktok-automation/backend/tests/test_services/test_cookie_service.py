"""
Tests for Cookie Service
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from services.cookie_service import CookieService
from core.encryption import encrypt_cookies, decrypt_cookies


class TestCookieService:
    """Test cases for Cookie Service"""

    @pytest.fixture
    def cookie_service(self):
        """Create cookie service instance"""
        return CookieService()

    async def test_save_cookies_success(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test successful cookie saving"""
        result = await cookie_service.save_cookies(
            sample_tiktok_account.id,
            mock_cookies
        )
        
        assert result is True
        
        # Verify account was updated
        assert sample_tiktok_account.is_logged_in is True
        assert sample_tiktok_account.cookies_data is not None

    async def test_save_cookies_invalid_account(self, cookie_service, mock_cookies):
        """Test saving cookies for non-existent account"""
        result = await cookie_service.save_cookies(999, mock_cookies)
        assert result is False

    async def test_save_cookies_empty_cookies(self, cookie_service, sample_tiktok_account):
        """Test saving empty cookies list"""
        result = await cookie_service.save_cookies(sample_tiktok_account.id, [])
        assert result is False

    async def test_load_cookies_success(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test successful cookie loading"""
        # First save cookies
        await cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies)
        
        # Then load them
        loaded_cookies = await cookie_service.load_cookies(sample_tiktok_account.id)
        
        assert loaded_cookies is not None
        assert len(loaded_cookies) == len(mock_cookies)
        assert loaded_cookies[0]["name"] == mock_cookies[0]["name"]

    async def test_load_cookies_no_cookies(self, cookie_service, sample_tiktok_account):
        """Test loading cookies when none exist"""
        cookies = await cookie_service.load_cookies(sample_tiktok_account.id)
        assert cookies is None

    async def test_load_cookies_invalid_account(self, cookie_service):
        """Test loading cookies for non-existent account"""
        cookies = await cookie_service.load_cookies(999)
        assert cookies is None

    async def test_delete_cookies_success(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test successful cookie deletion"""
        # First save cookies
        await cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies)
        
        # Then delete them
        result = await cookie_service.delete_cookies(sample_tiktok_account.id)
        
        assert result is True
        assert sample_tiktok_account.cookies_data is None
        assert sample_tiktok_account.is_logged_in is False

    async def test_delete_cookies_invalid_account(self, cookie_service):
        """Test deleting cookies for non-existent account"""
        result = await cookie_service.delete_cookies(999)
        assert result is False

    @patch('camoufox_integration.browser_manager.BrowserManager')
    async def test_validate_cookies_success(self, mock_browser_manager, cookie_service, sample_tiktok_account, mock_cookies):
        """Test successful cookie validation"""
        # Setup mocks
        mock_browser = AsyncMock()
        mock_context = AsyncMock()
        mock_page = AsyncMock()
        
        mock_browser_manager.return_value.create_browser_instance.return_value = mock_browser
        mock_browser_manager.return_value.create_browser_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        mock_page.goto.return_value = MagicMock(status=200)
        
        # Mock login status check
        with patch.object(cookie_service, '_check_login_status', return_value=True):
            # Save cookies first
            await cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies)
            
            # Validate cookies
            result = await cookie_service.validate_cookies(sample_tiktok_account.id)
            
            assert result["valid"] is True
            assert result["status_code"] == 200

    async def test_validate_cookies_no_cookies(self, cookie_service, sample_tiktok_account):
        """Test validating when no cookies exist"""
        result = await cookie_service.validate_cookies(sample_tiktok_account.id)
        assert result["valid"] is False
        assert "No cookies found" in result["error"]

    async def test_export_cookies_json(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test exporting cookies in JSON format"""
        # Save cookies first
        await cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies)
        
        # Export cookies
        exported = await cookie_service.export_cookies(sample_tiktok_account.id, "json")
        
        assert exported is not None
        assert "sessionid" in exported
        assert "csrf_token" in exported

    async def test_export_cookies_netscape(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test exporting cookies in Netscape format"""
        # Save cookies first
        await cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies)
        
        # Export cookies
        exported = await cookie_service.export_cookies(sample_tiktok_account.id, "netscape")
        
        assert exported is not None
        assert "# Netscape HTTP Cookie File" in exported
        assert ".tiktok.com" in exported

    async def test_import_cookies_json(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test importing cookies from JSON format"""
        import json
        
        cookies_json = json.dumps(mock_cookies)
        result = await cookie_service.import_cookies(
            sample_tiktok_account.id,
            cookies_json,
            "json"
        )
        
        assert result is True

    async def test_import_cookies_netscape(self, cookie_service, sample_tiktok_account):
        """Test importing cookies from Netscape format"""
        netscape_data = """# Netscape HTTP Cookie File
.tiktok.com	TRUE	/	TRUE	**********	sessionid	test_session_123
.tiktok.com	TRUE	/	FALSE	**********	csrf_token	test_csrf_456"""
        
        result = await cookie_service.import_cookies(
            sample_tiktok_account.id,
            netscape_data,
            "netscape"
        )
        
        assert result is True

    async def test_cleanup_expired_cookies(self, cookie_service, test_session):
        """Test cleanup of expired cookies"""
        from models.tiktok_account import TikTokAccount
        
        # Create account with expired cookies
        expired_cookies = [
            {
                "name": "expired_cookie",
                "value": "test_value",
                "domain": ".tiktok.com",
                "expires": datetime.utcnow().timestamp() - 3600  # Expired 1 hour ago
            }
        ]
        
        account = TikTokAccount(
            username="test_expired",
            cookies_data=encrypt_cookies(expired_cookies)
        )
        test_session.add(account)
        await test_session.commit()
        
        # Run cleanup
        result = await cookie_service.cleanup_expired_cookies()
        
        assert result["accounts_processed"] >= 1
        assert result["cookies_removed"] >= 1

    def test_filter_valid_cookies(self, cookie_service):
        """Test filtering valid cookies"""
        cookies = [
            {
                "name": "valid_cookie",
                "value": "test_value",
                "domain": ".tiktok.com"
            },
            {
                "name": "invalid_domain",
                "value": "test_value",
                "domain": ".facebook.com"  # Wrong domain
            },
            {
                "name": "no_value",
                "value": "",  # Empty value
                "domain": ".tiktok.com"
            },
            {
                # Missing required fields
                "value": "test_value"
            }
        ]
        
        valid_cookies = cookie_service._filter_valid_cookies(cookies)
        
        assert len(valid_cookies) == 1
        assert valid_cookies[0]["name"] == "valid_cookie"

    def test_filter_expired_cookies(self, cookie_service):
        """Test filtering expired cookies"""
        future_time = datetime.utcnow().timestamp() + 3600  # 1 hour from now
        past_time = datetime.utcnow().timestamp() - 3600    # 1 hour ago
        
        cookies = [
            {
                "name": "valid_cookie",
                "value": "test_value",
                "domain": ".tiktok.com",
                "expires": future_time
            },
            {
                "name": "expired_cookie",
                "value": "test_value",
                "domain": ".tiktok.com",
                "expires": past_time
            },
            {
                "name": "no_expiry",
                "value": "test_value",
                "domain": ".tiktok.com"
                # No expires field - should be kept
            }
        ]
        
        valid_cookies = cookie_service._filter_expired_cookies(cookies)
        
        assert len(valid_cookies) == 2
        assert any(c["name"] == "valid_cookie" for c in valid_cookies)
        assert any(c["name"] == "no_expiry" for c in valid_cookies)
        assert not any(c["name"] == "expired_cookie" for c in valid_cookies)

    def test_convert_to_netscape_format(self, cookie_service):
        """Test converting cookies to Netscape format"""
        cookies = [
            {
                "name": "test_cookie",
                "value": "test_value",
                "domain": ".tiktok.com",
                "path": "/",
                "secure": True,
                "expires": **********
            }
        ]
        
        netscape_format = cookie_service._convert_to_netscape_format(cookies)
        
        assert "# Netscape HTTP Cookie File" in netscape_format
        assert ".tiktok.com" in netscape_format
        assert "test_cookie" in netscape_format
        assert "test_value" in netscape_format

    def test_parse_netscape_format(self, cookie_service):
        """Test parsing Netscape format cookies"""
        netscape_data = """# Netscape HTTP Cookie File
.tiktok.com	TRUE	/	TRUE	**********	test_cookie	test_value
.tiktok.com	FALSE	/path	FALSE	0	another_cookie	another_value"""
        
        cookies = cookie_service._parse_netscape_format(netscape_data)
        
        assert len(cookies) == 2
        assert cookies[0]["name"] == "test_cookie"
        assert cookies[0]["value"] == "test_value"
        assert cookies[0]["domain"] == ".tiktok.com"
        assert cookies[0]["secure"] is True

    async def test_cookie_encryption_decryption(self, cookie_service, mock_cookies):
        """Test cookie encryption and decryption"""
        # Encrypt cookies
        encrypted = encrypt_cookies(mock_cookies)
        assert encrypted != mock_cookies
        
        # Decrypt cookies
        decrypted = decrypt_cookies(encrypted)
        assert decrypted == mock_cookies

    async def test_concurrent_cookie_operations(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test concurrent cookie operations"""
        import asyncio
        
        # Run multiple operations concurrently
        tasks = [
            cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies),
            cookie_service.load_cookies(sample_tiktok_account.id),
            cookie_service.validate_cookies(sample_tiktok_account.id)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Should not raise exceptions
        for result in results:
            assert not isinstance(result, Exception)

    async def test_cookie_file_backup(self, cookie_service, sample_tiktok_account, mock_cookies):
        """Test cookie file backup functionality"""
        # Save cookies (should create backup file)
        await cookie_service.save_cookies(sample_tiktok_account.id, mock_cookies)
        
        # Check if backup file exists
        backup_file = cookie_service.cookie_storage_path / f"account_{sample_tiktok_account.id}_cookies.json"
        assert backup_file.exists()
        
        # Delete cookies (should remove backup file)
        await cookie_service.delete_cookies(sample_tiktok_account.id)
        assert not backup_file.exists()

#!/bin/bash

# TikTok Automation Development Setup Script
# This script sets up the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS or Linux
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

log_info "Setting up TikTok Automation development environment on $MACHINE..."

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    log_info "Python version: $PYTHON_VERSION"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    log_info "Node.js version: $NODE_VERSION"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    log_info "npm version: $NPM_VERSION"
    
    log_success "All requirements satisfied"
}

# Setup backend
setup_backend() {
    log_info "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
        log_success "Virtual environment created"
    else
        log_info "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    log_info "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    log_info "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Initialize database
    log_info "Initializing database..."
    python -c "
import asyncio
from core.database import init_db
asyncio.run(init_db())
print('Database initialized successfully')
    " || {
        log_warning "Database initialization failed, will retry on first run"
    }
    
    # Create necessary directories
    mkdir -p data logs profiles
    
    cd ..
    log_success "Backend setup completed"
}

# Setup frontend
setup_frontend() {
    log_info "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    log_info "Installing Node.js dependencies..."
    npm install
    
    # Create build directory
    mkdir -p build
    
    cd ..
    log_success "Frontend setup completed"
}

# Setup Electron (optional)
setup_electron() {
    log_info "Setting up Electron..."
    
    cd electron
    
    # Install dependencies
    log_info "Installing Electron dependencies..."
    npm install
    
    cd ..
    log_success "Electron setup completed"
}

# Create development configuration
create_dev_config() {
    log_info "Creating development configuration..."
    
    # Backend .env file
    if [ ! -f "backend/.env" ]; then
        cat > backend/.env << EOF
# Development Environment Configuration
DEBUG=True
LOG_LEVEL=DEBUG
DATABASE_URL=sqlite:///./data/tiktok_automation.db
SECRET_KEY=dev-secret-key-change-in-production
CORS_ORIGINS=["http://localhost:3000"]

# Development settings
RELOAD=true
WORKERS=1
EOF
        log_success "Backend .env file created"
    else
        log_info "Backend .env file already exists"
    fi
    
    # Frontend .env file
    if [ ! -f "frontend/.env" ]; then
        cat > frontend/.env << EOF
# Frontend Development Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_ENV=development
GENERATE_SOURCEMAP=true
EOF
        log_success "Frontend .env file created"
    else
        log_info "Frontend .env file already exists"
    fi
}

# Install browser dependencies
install_browser_deps() {
    log_info "Installing browser dependencies..."
    
    if [ "$MACHINE" = "Mac" ]; then
        # macOS specific setup
        if command -v brew &> /dev/null; then
            log_info "Installing Firefox via Homebrew..."
            brew install --cask firefox || log_warning "Firefox installation failed"
        else
            log_warning "Homebrew not found. Please install Firefox manually."
        fi
    elif [ "$MACHINE" = "Linux" ]; then
        # Linux specific setup
        if command -v apt-get &> /dev/null; then
            log_info "Installing Firefox via apt..."
            sudo apt-get update
            sudo apt-get install -y firefox
        elif command -v yum &> /dev/null; then
            log_info "Installing Firefox via yum..."
            sudo yum install -y firefox
        else
            log_warning "Package manager not found. Please install Firefox manually."
        fi
    fi
    
    log_success "Browser dependencies setup completed"
}

# Create start scripts
create_start_scripts() {
    log_info "Creating start scripts..."
    
    # Backend start script
    cat > start-backend.sh << 'EOF'
#!/bin/bash
cd backend
source venv/bin/activate
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
EOF
    chmod +x start-backend.sh
    
    # Frontend start script
    cat > start-frontend.sh << 'EOF'
#!/bin/bash
cd frontend
npm start
EOF
    chmod +x start-frontend.sh
    
    # Combined start script
    cat > start-dev.sh << 'EOF'
#!/bin/bash

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}Starting TikTok Automation Development Environment...${NC}"

# Function to kill background processes on exit
cleanup() {
    echo -e "\n${BLUE}Shutting down...${NC}"
    kill $(jobs -p) 2>/dev/null || true
    exit 0
}

trap cleanup SIGINT SIGTERM

# Start backend
echo -e "${GREEN}Starting backend...${NC}"
./start-backend.sh &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 5

# Start frontend
echo -e "${GREEN}Starting frontend...${NC}"
./start-frontend.sh &
FRONTEND_PID=$!

echo -e "${GREEN}Development environment started!${NC}"
echo -e "Frontend: http://localhost:3000"
echo -e "Backend API: http://localhost:8000"
echo -e "API Docs: http://localhost:8000/docs"
echo -e "\nPress Ctrl+C to stop all services"

# Wait for processes
wait
EOF
    chmod +x start-dev.sh
    
    log_success "Start scripts created"
}

# Create VS Code configuration
create_vscode_config() {
    log_info "Creating VS Code configuration..."
    
    mkdir -p .vscode
    
    # Settings
    cat > .vscode/settings.json << EOF
{
    "python.defaultInterpreterPath": "./backend/venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/node_modules": true,
        "**/__pycache__": true,
        "**/venv": true,
        "**/.pytest_cache": true
    }
}
EOF

    # Launch configuration
    cat > .vscode/launch.json << EOF
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: FastAPI",
            "type": "python",
            "request": "launch",
            "program": "\${workspaceFolder}/backend/venv/bin/uvicorn",
            "args": ["main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
            "cwd": "\${workspaceFolder}/backend",
            "env": {
                "PYTHONPATH": "\${workspaceFolder}/backend"
            },
            "console": "integratedTerminal"
        }
    ]
}
EOF

    # Tasks
    cat > .vscode/tasks.json << EOF
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Start Backend",
            "type": "shell",
            "command": "./start-backend.sh",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            }
        },
        {
            "label": "Start Frontend",
            "type": "shell",
            "command": "./start-frontend.sh",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            }
        }
    ]
}
EOF

    log_success "VS Code configuration created"
}

# Main setup function
main() {
    log_info "TikTok Automation Development Setup"
    log_info "===================================="
    
    check_requirements
    setup_backend
    setup_frontend
    
    # Ask if user wants Electron
    read -p "Do you want to set up Electron? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_electron
    fi
    
    create_dev_config
    install_browser_deps
    create_start_scripts
    create_vscode_config
    
    log_success "Development environment setup completed!"
    echo
    log_info "Next steps:"
    echo "1. Review configuration files (.env files)"
    echo "2. Start development environment: ./start-dev.sh"
    echo "3. Open http://localhost:3000 in your browser"
    echo "4. Check API documentation at http://localhost:8000/docs"
    echo
    log_info "Individual services:"
    echo "- Backend only: ./start-backend.sh"
    echo "- Frontend only: ./start-frontend.sh"
    echo
    log_warning "Make sure to configure your proxies and TikTok accounts before running automation tasks!"
}

# Run main function
main "$@"

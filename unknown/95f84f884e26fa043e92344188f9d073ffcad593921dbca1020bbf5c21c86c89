#!/usr/bin/env python3
"""
TikTok Automation Demo Script
Demonstrates the key features of the application
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api/v1"

class TikTokAutomationDemo:
    def __init__(self):
        self.session = None
        self.profiles = []
        self.proxies = []
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request to backend API"""
        url = f"{BASE_URL}{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await response.json()
            elif method.upper() == "PUT":
                async with self.session.put(url, json=data) as response:
                    return await response.json()
            elif method.upper() == "DELETE":
                async with self.session.delete(url) as response:
                    return await response.json()
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return {"error": str(e)}
    
    async def demo_system_status(self):
        """Demo: Check system status"""
        print("\n🔍 Checking System Status...")
        
        try:
            status = await self.make_request("GET", "/system/status")
            if "error" not in status:
                print("✅ Backend is running")
                print(f"   - Status: {status.get('status', 'Unknown')}")
                print(f"   - Version: {status.get('version', 'Unknown')}")
                print(f"   - Uptime: {status.get('uptime', 'Unknown')}")
            else:
                print("❌ Backend connection failed")
                return False
        except Exception as e:
            print(f"❌ System check failed: {e}")
            return False
        
        return True
    
    async def demo_proxy_management(self):
        """Demo: Proxy management"""
        print("\n🌐 Proxy Management Demo...")
        
        # Create test proxy
        proxy_data = {
            "name": "Demo Proxy",
            "proxy_type": "http",
            "host": "proxy.example.com",
            "port": 8080,
            "username": "demo_user",
            "password": "demo_pass",
            "description": "Demo proxy for testing",
            "validate_on_create": False  # Skip validation for demo
        }
        
        print("📝 Creating demo proxy...")
        proxy = await self.make_request("POST", "/proxies/", proxy_data)
        
        if "error" not in proxy and "id" in proxy:
            print(f"✅ Proxy created: {proxy.get('name')} (ID: {proxy.get('id')})")
            self.proxies.append(proxy)
        else:
            print(f"❌ Proxy creation failed: {proxy.get('error', proxy.get('detail', 'Unknown error'))}")
        
        # List proxies
        print("📋 Listing all proxies...")
        proxies = await self.make_request("GET", "/proxies/")
        
        if "error" not in proxies and isinstance(proxies, list):
            print(f"✅ Found {len(proxies)} proxies")
            for proxy in proxies:
                print(f"   - {proxy.get('name')}: {proxy.get('host')}:{proxy.get('port')}")
        else:
            print("❌ Failed to list proxies")
    
    async def demo_profile_management(self):
        """Demo: Profile management"""
        print("\n👤 Profile Management Demo...")
        
        # Create test profiles
        profiles_data = [
            {
                "name": "Demo Profile 1",
                "description": "First demo profile",
                "auto_generate_fingerprint": True,
                "os_preference": "windows",
                "browser_preference": "firefox"
            },
            {
                "name": "Demo Profile 2", 
                "description": "Second demo profile with proxy",
                "auto_generate_fingerprint": True,
                "os_preference": "macos",
                "browser_preference": "firefox",
                "proxy_id": self.proxies[0]["id"] if self.proxies else None
            }
        ]
        
        for i, profile_data in enumerate(profiles_data, 1):
            print(f"📝 Creating demo profile {i}...")
            profile = await self.make_request("POST", "/profiles/", profile_data)
            
            if "error" not in profile and "id" in profile:
                print(f"✅ Profile created: {profile.get('name')} (ID: {profile.get('id')})")
                self.profiles.append(profile)
            else:
                print(f"❌ Profile creation failed: {profile.get('error', profile.get('detail', 'Unknown error'))}")
        
        # List profiles
        print("📋 Listing all profiles...")
        profiles = await self.make_request("GET", "/profiles/")
        
        if "error" not in profiles and isinstance(profiles, list):
            print(f"✅ Found {len(profiles)} profiles")
            for profile in profiles:
                print(f"   - {profile.get('name')}: {profile.get('description')}")
        else:
            print("❌ Failed to list profiles")
    
    async def demo_antidetect_testing(self):
        """Demo: Antidetect testing"""
        print("\n🛡️ Antidetect Testing Demo...")
        
        if not self.profiles:
            print("❌ No profiles available for testing")
            return
        
        profile = self.profiles[0]
        print(f"🧪 Testing antidetect capabilities for: {profile.get('name')}")
        
        # Get available test sites
        print("📋 Getting available test sites...")
        test_sites = await self.make_request("GET", "/antidetect/test-sites")
        
        if "error" not in test_sites:
            sites = test_sites.get("test_sites", [])
            print(f"✅ Found {len(sites)} test sites:")
            for site in sites[:3]:  # Show first 3
                print(f"   - {site.get('name')}: {site.get('url')}")
        
        # Run antidetect test (simplified for demo)
        print("🔬 Running antidetect test...")
        test_data = {
            "profile_id": profile.get("id"),
            "test_sites": ["bot_sannysoft"],  # Test just one site for demo
            "include_tiktok_test": False,  # Skip TikTok test for demo
            "headless": True
        }
        
        # Note: This would normally take time to run actual browser tests
        # For demo purposes, we'll just show the API call
        print("⏳ Antidetect test would run here...")
        print("   (Skipped in demo to avoid browser launch)")
        print("✅ Test completed - Results would show detection score and recommendations")
    
    async def demo_monitoring(self):
        """Demo: Monitoring and logs"""
        print("\n📊 Monitoring Demo...")
        
        # Get system logs
        print("📋 Fetching system logs...")
        logs = await self.make_request("GET", "/system/logs", {"limit": 5})
        
        if "error" not in logs:
            print("✅ Recent system logs:")
            log_entries = logs.get("logs", [])
            for log in log_entries[:3]:  # Show first 3
                print(f"   [{log.get('timestamp')}] {log.get('level')}: {log.get('message')}")
        else:
            print("❌ Failed to fetch logs")
        
        # Get detection statistics
        print("📈 Getting detection statistics...")
        stats = await self.make_request("GET", "/antidetect/stats/detection")
        
        if "error" not in stats:
            print("✅ Detection statistics:")
            print(f"   - Profiles tested: {stats.get('total_profiles_tested', 0)}")
            print(f"   - Average score: {stats.get('average_detection_score', 0)}%")
            print(f"   - TikTok success rate: {stats.get('tiktok_success_rate', 0)}%")
        else:
            print("❌ Failed to fetch statistics")
    
    async def cleanup_demo_data(self):
        """Clean up demo data"""
        print("\n🧹 Cleaning up demo data...")
        
        # Delete demo profiles
        for profile in self.profiles:
            result = await self.make_request("DELETE", f"/profiles/{profile['id']}")
            if "error" not in result:
                print(f"✅ Deleted profile: {profile['name']}")
            else:
                print(f"❌ Failed to delete profile: {profile['name']}")
        
        # Delete demo proxies
        for proxy in self.proxies:
            result = await self.make_request("DELETE", f"/proxies/{proxy['id']}")
            if "error" not in result:
                print(f"✅ Deleted proxy: {proxy['name']}")
            else:
                print(f"❌ Failed to delete proxy: {proxy['name']}")
    
    async def run_full_demo(self):
        """Run complete demo"""
        print("🚀 TikTok Automation Demo Starting...")
        print("=" * 50)
        
        # Check system status
        if not await self.demo_system_status():
            print("❌ Demo aborted - Backend not available")
            return
        
        # Demo proxy management
        await self.demo_proxy_management()
        
        # Demo profile management
        await self.demo_profile_management()
        
        # Demo antidetect testing
        await self.demo_antidetect_testing()
        
        # Demo monitoring
        await self.demo_monitoring()
        
        # Cleanup
        await self.cleanup_demo_data()
        
        print("\n" + "=" * 50)
        print("✅ Demo completed successfully!")
        print("\n📖 Next steps:")
        print("   1. Open the frontend at http://localhost:3000")
        print("   2. Create your own profiles with real proxies")
        print("   3. Run antidetect tests to verify stealth")
        print("   4. Start TikTok automation tasks")
        print("\n🎯 Happy automating!")

async def main():
    """Main demo function"""
    try:
        async with TikTokAutomationDemo() as demo:
            await demo.run_full_demo()
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")

if __name__ == "__main__":
    print("🎬 TikTok Automation Demo")
    print("Make sure the backend is running on http://localhost:8000")
    print("Press Ctrl+C to stop the demo at any time")
    print()
    
    asyncio.run(main())

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "camoufox"
version = "0.4.11"
description = "Wrapper around <PERSON><PERSON> to help launch Camoufox"
authors = ["daijro <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/daijro/camoufox"
homepage = "https://camoufox.com/python"
readme = "README.md"
keywords = [
    "client",
    "fingerprint",
    "browser",
    "scraping",
    "injector",
    "firefox",
    "playwright",
]
classifiers = [
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

[tool.poetry.dependencies]
python = "^3.8"
click = "*"
requests = "*"
orjson = "*"
browserforge = "^1.2.1"
playwright = "*"
pyyaml = "*"
platformdirs = "*"
tqdm = "*"
numpy = "*"
ua_parser = "*"
typing_extensions = "*"
screeninfo = "*"
lxml = "*"
language-tags = "*"
pysocks = "*"
geoip2 = {version = "*", optional = true}

[tool.poetry.extras]
geoip = ["geoip2"]

[tool.poetry.scripts]
camoufox = "camoufox.__main__:cli"

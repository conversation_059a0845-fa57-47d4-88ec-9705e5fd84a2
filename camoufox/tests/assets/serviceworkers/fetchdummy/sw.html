<script>
  window.registrationPromise = navigator.serviceWorker.register('sw.js');
  window.activationPromise = new Promise(resolve => navigator.serviceWorker.oncontrollerchange = resolve);

  async function fetchDummy(name) {
    const response = await fetch(name);
    if (!response.ok)
      return 'FAILURE: ' + response.statusText;
    const text = await response.text();
    return text;
  }
</script>

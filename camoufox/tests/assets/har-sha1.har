{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.23.0-next"}, "browser": {"name": "chromium", "version": "103.0.5060.33"}, "pages": [{"startedDateTime": "2022-06-10T04:27:32.125Z", "id": "page@b17b177f1c2e66459db3dcbe44636ffd", "title": "Hey", "pageTimings": {"onContentLoad": 70, "onLoad": 70}}], "entries": [{"_frameref": "frame@c7467fc0f1f86f09fc3b0d727a3862ea", "_monotonicTime": 270572145.898, "startedDateTime": "2022-06-10T04:27:32.146Z", "time": 8.286, "request": {"method": "GET", "url": "http://no.playwright/", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.33 Safari/537.36"}], "queryString": [], "headersSize": 326, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "content-length", "value": "12"}, {"name": "content-type", "value": "text/html"}], "content": {"size": 12, "mimeType": "text/html", "compression": 0, "_file": "har-sha1-main-response.txt"}, "headersSize": 64, "bodySize": 71, "redirectURL": "", "_transferSize": 71}, "cache": {"beforeRequest": null, "afterRequest": null}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 8.286, "receive": -1}, "pageref": "page@b17b177f1c2e66459db3dcbe44636ffd", "_securityDetails": {}}]}}
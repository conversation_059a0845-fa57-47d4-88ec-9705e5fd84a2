<!DOCTYPE html>
<html>
  <head>
    <title>Rotated button test</title>
  </head>
  <body>
    <script src="mouse-helper.js"></script>
    <button onclick="clicked();">Click target</button>
    <style>
      button {
        transform: rotateY(180deg);
      }
    </style>
    <script>
      window.result = 'Was not clicked';
      function clicked() {
        result = 'Clicked';
      }
    </script>
  </body>
</html>

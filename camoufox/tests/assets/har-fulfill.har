{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.23.0-next"}, "browser": {"name": "chromium", "version": "103.0.5060.33"}, "pages": [{"startedDateTime": "2022-06-10T04:27:32.125Z", "id": "page@b17b177f1c2e66459db3dcbe44636ffd", "title": "Hey", "pageTimings": {"onContentLoad": 70, "onLoad": 70}}], "entries": [{"_frameref": "frame@c7467fc0f1f86f09fc3b0d727a3862ea", "_monotonicTime": 270572145.898, "startedDateTime": "2022-06-10T04:27:32.146Z", "time": 8.286, "request": {"method": "GET", "url": "http://no.playwright/", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.33 Safari/537.36"}], "queryString": [], "headersSize": 326, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "content-length", "value": "111"}, {"name": "content-type", "value": "text/html"}], "content": {"size": 111, "mimeType": "text/html", "compression": 0, "text": "<title>Hey</title><link rel='stylesheet' href='./style.css'><script src='./script.js'></script><div>hello</div>"}, "headersSize": 65, "bodySize": 170, "redirectURL": "", "_transferSize": 170}, "cache": {"beforeRequest": null, "afterRequest": null}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 8.286, "receive": -1}, "pageref": "page@b17b177f1c2e66459db3dcbe44636ffd", "_securityDetails": {}}, {"_frameref": "frame@c7467fc0f1f86f09fc3b0d727a3862ea", "_monotonicTime": 270572174.683, "startedDateTime": "2022-06-10T04:27:32.172Z", "time": 7.132, "request": {"method": "POST", "url": "http://no.playwright/style.css", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "text/css,*/*;q=0.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://no.playwright/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.33 Safari/537.36"}], "queryString": [], "headersSize": 220, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "content-length", "value": "24"}, {"name": "content-type", "value": "text/css"}], "content": {"size": 24, "mimeType": "text/css", "compression": 0, "text": "body { background:cyan }"}, "headersSize": 63, "bodySize": 81, "redirectURL": "", "_transferSize": 81}, "cache": {"beforeRequest": null, "afterRequest": null}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 8.132, "receive": -1}, "pageref": "page@b17b177f1c2e66459db3dcbe44636ffd", "_securityDetails": {}}, {"_frameref": "frame@c7467fc0f1f86f09fc3b0d727a3862ea", "_monotonicTime": 270572174.683, "startedDateTime": "2022-06-10T04:27:32.174Z", "time": 8.132, "request": {"method": "GET", "url": "http://no.playwright/style.css", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "text/css,*/*;q=0.1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://no.playwright/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.33 Safari/537.36"}], "queryString": [], "headersSize": 220, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "content-length", "value": "24"}, {"name": "content-type", "value": "text/css"}], "content": {"size": 24, "mimeType": "text/css", "compression": 0, "text": "body { background: red }"}, "headersSize": 63, "bodySize": 81, "redirectURL": "", "_transferSize": 81}, "cache": {"beforeRequest": null, "afterRequest": null}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 8.132, "receive": -1}, "pageref": "page@b17b177f1c2e66459db3dcbe44636ffd", "_securityDetails": {}}, {"_frameref": "frame@c7467fc0f1f86f09fc3b0d727a3862ea", "_monotonicTime": 270572175.042, "startedDateTime": "2022-06-10T04:27:32.175Z", "time": 15.997, "request": {"method": "GET", "url": "http://no.playwright/script.js", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://no.playwright/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.33 Safari/537.36"}], "queryString": [], "headersSize": 205, "bodySize": 0}, "response": {"status": 301, "statusText": "Moved Permanently", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "location", "value": "http://no.playwright/script2.js"}], "content": {"size": -1, "mimeType": "x-unknown", "compression": 0}, "headersSize": 77, "bodySize": 0, "redirectURL": "http://no.playwright/script2.js", "_transferSize": 77}, "cache": {"beforeRequest": null, "afterRequest": null}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 7.673, "receive": 8.324}, "pageref": "page@b17b177f1c2e66459db3dcbe44636ffd", "_securityDetails": {}}, {"_frameref": "frame@c7467fc0f1f86f09fc3b0d727a3862ea", "_monotonicTime": 270572181.822, "startedDateTime": "2022-06-10T04:27:32.182Z", "time": 6.735, "request": {"method": "GET", "url": "http://no.playwright/script2.js", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://no.playwright/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.33 Safari/537.36"}], "queryString": [], "headersSize": 206, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "content-length", "value": "18"}, {"name": "content-type", "value": "text/javascript"}], "content": {"size": 18, "mimeType": "text/javascript", "compression": 0, "text": "window.value='foo'"}, "headersSize": 70, "bodySize": 82, "redirectURL": "", "_transferSize": 82}, "cache": {"beforeRequest": null, "afterRequest": null}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 6.735, "receive": -1}, "pageref": "page@b17b177f1c2e66459db3dcbe44636ffd", "_securityDetails": {}}]}}
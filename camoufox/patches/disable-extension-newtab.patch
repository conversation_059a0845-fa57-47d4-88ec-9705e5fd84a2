diff --git a/browser/components/extensions/parent/ext-browser.js b/browser/components/extensions/parent/ext-browser.js
index 179816fa96..c7de1c2278 100644
--- a/browser/components/extensions/parent/ext-browser.js
+++ b/browser/components/extensions/parent/ext-browser.js
@@ -62,6 +62,9 @@ extensions.on("page-shutdown", (type, context) => {
 /* eslint-enable mozilla/balanced-listeners */
 
 global.openOptionsPage = extension => {
+  if (!ChromeUtils.camouGetBool('allowAddonNewtab', false)) {
+    return Promise.reject({ message: "Rejected by <PERSON>oufox." });
+  }
   let window = windowTracker.topWindow;
   if (!window) {
     return Promise.reject({ message: "No browser window available" });
diff --git a/browser/components/extensions/parent/ext-tabs.js b/browser/components/extensions/parent/ext-tabs.js
index b47f0510e3..649caccc60 100644
--- a/browser/components/extensions/parent/ext-tabs.js
+++ b/browser/components/extensions/parent/ext-tabs.js
@@ -700,6 +700,9 @@ this.tabs = class extends ExtensionAPIPersistent {
         }).api(),
 
         create(createProperties) {
+          if (!ChromeUtils.camouGetBool('allowAddonNewtab', false)) {
+            return Promise.reject({ message: "Rejected by Camoufox." });
+          }
           return new Promise(resolve => {
             let window =
               createProperties.windowId !== null

diff --git a/browser/base/content/browser.js b/browser/base/content/browser.js
index 43c84c3..4b4aad2 100644
--- a/browser/base/content/browser.js
+++ b/browser/base/content/browser.js
@@ -5478,7 +5478,6 @@ var XULBrowserWindow = {
 
     SafeBrowsingNotificationBox.onLocationChange(aLocationURI);
 
-    SaveToPocket.onLocationChange(window);
 
     let originalURI;
     if (
diff --git a/browser/components/BrowserGlue.jsm b/browser/components/BrowserGlue.sys.mjs
index b2479db..7670f08 100644
--- a/browser/components/BrowserGlue.sys.mjs
+++ b/browser/components/BrowserGlue.sys.mjs
@@ -1274,7 +1274,6 @@ BrowserGlue.prototype = {
       lazy.Normandy.init();
     }
 
-    lazy.SaveToPocket.init();
 
     AboutHomeStartupCache.init();
 
diff --git a/browser/components/moz.build b/browser/components/moz.build
index 3b2126e..32b7831 100644
--- a/browser/components/moz.build
+++ b/browser/components/moz.build
@@ -44,7 +44,6 @@ DIRS += [
     "originattributes",
     "pagedata",
     "places",
-    "pocket",
     "preferences",
     "privatebrowsing",
     "prompts",

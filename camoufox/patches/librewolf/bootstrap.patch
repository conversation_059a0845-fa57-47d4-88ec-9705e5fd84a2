diff --git a/python/mozversioncontrol/mozversioncontrol/repo/source.py b/python/mozversioncontrol/mozversioncontrol/repo/source.py
index 59c1d5fbd238..410177d94d89 100644
--- a/python/mozversioncontrol/mozversioncontrol/repo/source.py
+++ b/python/mozversioncontrol/mozversioncontrol/repo/source.py
@@ -91,7 +91,7 @@ class SrcRepository(Repository):
         for root, dirs, files in os.walk(self.path):
             base = os.path.relpath(root, self.path)
             for name in files:
-                res.append(os.path.join(base, name))
+                res.append(os.path.join(base, name).replace("\\", "/"))
         return res
 
     def get_tracked_files_finder(self, path):

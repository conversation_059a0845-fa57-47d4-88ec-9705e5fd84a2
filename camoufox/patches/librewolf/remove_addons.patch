diff --git a/browser/extensions/moz.build b/browser/extensions/moz.build
index 3c6e7eb88689..d923333aa100 100644
--- a/browser/extensions/moz.build
+++ b/browser/extensions/moz.build
@@ -8,7 +8,6 @@ DIRS += [
     "formautofill",
     "screenshots",
     "webcompat",
-    "report-site-issue",
     "pictureinpicture",
     "search-detection",
 ]
diff --git a/browser/locales/Makefile.in b/browser/locales/Makefile.in
index 7296ca447757..1278582e79d7 100644
--- a/browser/locales/Makefile.in
+++ b/browser/locales/Makefile.in
@@ -56,7 +56,6 @@ l10n-%:
 ifneq (,$(wildcard ../extensions/formautofill/locales))
 	@$(MAKE) -C ../extensions/formautofill/locales AB_CD=$* XPI_NAME=locale-$*
 endif
-	@$(MAKE) -C ../extensions/report-site-issue/locales AB_CD=$* XPI_NAME=locale-$*
 	@$(MAKE) -C ../../devtools/client/locales AB_CD=$* XPI_NAME=locale-$* XPI_ROOT_APPID='$(XPI_ROOT_APPID)'
 	@$(MAKE) -C ../../devtools/startup/locales AB_CD=$* XPI_NAME=locale-$* XPI_ROOT_APPID='$(XPI_ROOT_APPID)'
 	@$(MAKE) l10n AB_CD=$* XPI_NAME=locale-$* PREF_DIR=$(PREF_DIR)
@@ -78,7 +77,6 @@ endif
 	@$(MAKE) -C ../../devtools/startup/locales chrome AB_CD=$*
 	@$(MAKE) chrome AB_CD=$*
 	@$(MAKE) -C $(DEPTH)/$(MOZ_BRANDING_DIRECTORY)/locales chrome AB_CD=$*
-	@$(MAKE) -C ../extensions/report-site-issue/locales chrome AB_CD=$*
 
 package-win32-installer: $(SUBMAKEFILES)
 	$(MAKE) -C ../installer/windows CONFIG_DIR=l10ngen ZIP_IN='$(ZIP_OUT)' installer
diff --git a/browser/locales/filter.py b/browser/locales/filter.py
index 22eb5cbdb177..5e1f09ff383e 100644
--- a/browser/locales/filter.py
+++ b/browser/locales/filter.py
@@ -17,7 +17,6 @@ def test(mod, path, entity=None):
         "devtools/startup",
         "browser",
         "browser/extensions/formautofill",
-        "browser/extensions/report-site-issue",
         "extensions/spellcheck",
         "other-licenses/branding/firefox",
         "browser/branding/official",
diff --git a/browser/locales/l10n.ini b/browser/locales/l10n.ini
index 7a6599740b20..7f976679eefc 100644
--- a/browser/locales/l10n.ini
+++ b/browser/locales/l10n.ini
@@ -13,7 +13,6 @@ dirs = browser
      devtools/client
      devtools/startup
      browser/extensions/formautofill
-     browser/extensions/report-site-issue
 
 [includes]
 # non-central apps might want to use %(topsrcdir)s here, or other vars
diff --git a/browser/locales/l10n.toml b/browser/locales/l10n.toml
index e9d50107cb10..914a2f1a6310 100644
--- a/browser/locales/l10n.toml
+++ b/browser/locales/l10n.toml
@@ -133,10 +133,6 @@ locales = [
     reference = "browser/extensions/formautofill/locales/en-US/**"
     l10n = "{l}browser/extensions/formautofill/**"
 
-[[paths]]
-    reference = "browser/extensions/report-site-issue/locales/en-US/**"
-    l10n = "{l}browser/extensions/report-site-issue/**"
-
 
 [[includes]]
     path = "toolkit/locales/l10n.toml"
-- 
2.37.3


diff --git a/browser/base/content/navigator-toolbox.inc.xhtml b/browser/base/content/navigator-toolbox.inc.xhtml
index eb2d8670874b..e57694a72bd3 100644
--- a/browser/base/content/navigator-toolbox.inc.xhtml
+++ b/browser/base/content/navigator-toolbox.inc.xhtml
@@ -35,14 +35,6 @@
     <hbox flex="1" align="end" class="toolbar-items">
       <toolbartabstop/>
       <hbox id="TabsToolbar-customization-target" flex="1">
-        <toolbarbutton id="firefox-view-button"
-                       class="toolbarbutton-1 chromeclass-toolbar-additional"
-                       data-l10n-id="toolbar-button-firefox-view-2"
-                       role="button"
-                       aria-pressed="false"
-                       cui-areatype="toolbar"
-                       removable="true"/>
-
         <tabs id="tabbrowser-tabs"
               is="tabbrowser-tabs"
               aria-multiselectable="true"
@@ -667,5 +659,13 @@
                    closemenu="none"
                    cui-areatype="toolbar"
                    data-l10n-id="navbar-library"/>
+
+    <toolbarbutton id="firefox-view-button"
+                   class="toolbarbutton-1 chromeclass-toolbar-additional"
+                   data-l10n-id="toolbar-button-firefox-view-2"
+                   role="button"
+                   aria-pressed="false"
+                   cui-areatype="toolbar"
+                   removable="true"/>
   </html:template>
 </toolbox>
diff --git a/browser/components/customizableui/CustomizableUI.sys.mjs b/browser/components/customizableui/CustomizableUI.sys.mjs
index 45d0e0e4872e..4ed336da6bb5 100644
--- a/browser/components/customizableui/CustomizableUI.sys.mjs
+++ b/browser/components/customizableui/CustomizableUI.sys.mjs
@@ -347,7 +347,6 @@ var CustomizableUIInternal = {
       {
         type: CustomizableUI.TYPE_TOOLBAR,
         defaultPlacements: [
-          "firefox-view-button",
           "tabbrowser-tabs",
           "new-tab-button",
           "alltabs-button",
@@ -707,18 +706,6 @@ var CustomizableUIInternal = {
       }
     }
 
-    // Add firefox-view if not present
-    if (currentVersion < 18) {
-      let tabstripPlacements =
-        gSavedState.placements[CustomizableUI.AREA_TABSTRIP];
-      if (
-        tabstripPlacements &&
-        !tabstripPlacements.includes("firefox-view-button")
-      ) {
-        tabstripPlacements.unshift("firefox-view-button");
-      }
-    }
-
     // Unified Extensions addon button migration, which puts any browser action
     // buttons in the overflow menu into the addons panel instead.
     if (currentVersion < 19) {

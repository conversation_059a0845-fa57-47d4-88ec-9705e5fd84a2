diff --git a/dom/base/nsScreen.cpp b/dom/base/nsScreen.cpp
index 0f4b4b081c..0ec313f725 100644
--- a/dom/base/nsScreen.cpp
+++ b/dom/base/nsScreen.cpp
@@ -65,6 +65,12 @@ nsDeviceContext* nsScreen::GetDeviceContext() const {
 }
 
 CSSIntRect nsScreen::GetRect() {
+  // Check for height and width overrides
+  if (auto height = MaskConfig::GetInt32("screen.height"),
+      width = MaskConfig::GetInt32("screen.width");
+      height && width) {
+    return {0, 0, width.value(), height.value()};
+  }
   // Return window inner rect to prevent fingerprinting.
   if (ShouldResistFingerprinting(RFPTarget::ScreenRect)) {
     return GetTopWindowInnerRectForRFP();
diff --git a/gfx/src/moz.build b/gfx/src/moz.build
index a2b3e60fe5..faa0c113bc 100644
--- a/gfx/src/moz.build
+++ b/gfx/src/moz.build
@@ -95,3 +95,6 @@ FINAL_LIBRARY = "xul"
 if CONFIG["MOZ_WIDGET_TOOLKIT"] == "gtk":
     CXXFLAGS += CONFIG["MOZ_GTK3_CFLAGS"]
     CXXFLAGS += CONFIG["MOZ_PANGO_CFLAGS"]
+
+# DOM Mask
+LOCAL_INCLUDES += ["/camoucfg"]
\ No newline at end of file
diff --git a/gfx/src/nsDeviceContext.cpp b/gfx/src/nsDeviceContext.cpp
index 7a4fa8d48f..408b754d92 100644
--- a/gfx/src/nsDeviceContext.cpp
+++ b/gfx/src/nsDeviceContext.cpp
@@ -5,6 +5,7 @@
  * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
 
 #include "nsDeviceContext.h"
+#include "MaskConfig.hpp"
 #include <algorithm>  // for max
 #include "gfxContext.h"
 #include "gfxPoint.h"    // for gfxSize
@@ -177,6 +178,13 @@ bool nsDeviceContext::GetScreenIsHDR() {
 }
 
 nsSize nsDeviceContext::GetDeviceSurfaceDimensions() {
+  // Check for height and width overrides from MaskConfig
+  if (auto height = MaskConfig::GetInt32("screen.height"),
+      width = MaskConfig::GetInt32("screen.width");
+      height && width) {
+    nsRect outRect = CSSPixel::ToAppUnits(CSSIntRect(0, 0, width.value(), height.value()));
+    return outRect.Size();
+  }
   return GetRect().Size();
 }
 
diff --git a/layout/style/nsMediaFeatures.cpp b/layout/style/nsMediaFeatures.cpp
index f83e2c21ca..5362dd77d4 100644
--- a/layout/style/nsMediaFeatures.cpp
+++ b/layout/style/nsMediaFeatures.cpp
@@ -66,14 +66,6 @@ static nsSize GetDeviceSize(const Document& aDocument) {
     return GetSize(aDocument);
   }
 
-  // Media queries in documents in an RDM pane should use the simulated
-  // device size.
-  Maybe<CSSIntSize> deviceSize =
-      nsGlobalWindowOuter::GetRDMDeviceSize(aDocument);
-  if (deviceSize.isSome()) {
-    return CSSPixel::ToAppUnits(deviceSize.value());
-  }
-
   nsPresContext* pc = aDocument.GetPresContext();
   // NOTE(emilio): We should probably figure out how to return an appropriate
   // device size here, though in a multi-screen world that makes no sense

diff --git a/browser/components/urlbar/UrlbarProviderInterventions.sys.mjs b/browser/components/urlbar/UrlbarProviderInterventions.sys.mjs
index 988f5831f2..62f64589a4 100644
--- a/browser/components/urlbar/UrlbarProviderInterventions.sys.mjs
+++ b/browser/components/urlbar/UrlbarProviderInterventions.sys.mjs
@@ -494,7 +494,7 @@ class ProviderInterventions extends UrlbarProvider {
    * @returns {boolean} Whether this provider should be invoked for the search.
    */
   isActive(queryContext) {
-    if (
+    if (true ||
       !queryContext.searchString ||
       queryContext.searchString.length > UrlbarUtils.MAX_TEXT_LENGTH ||
       lazy.UrlbarTokenizer.REGEXP_LIKE_PROTOCOL.test(
diff --git a/toolkit/components/search/SearchEngineSelector.sys.mjs b/toolkit/components/search/SearchEngineSelector.sys.mjs
index 3dab73ee2f..c5c1259217 100644
--- a/toolkit/components/search/SearchEngineSelector.sys.mjs
+++ b/toolkit/components/search/SearchEngineSelector.sys.mjs
@@ -158,6 +158,21 @@ export class SearchEngineSelector {
    *   could be obtained.
    */
   async _getConfiguration(firstTime = true) {
+    if (true) {
+      return [
+        {
+          "appliesTo": [{
+            "default": "yes",
+            "included": {
+              "everywhere": true
+            },
+            "webExtension": {
+              "id": "<EMAIL>"
+            }
+          }],
+        },
+      ];
+    }
     let result = [];
     let failed = false;
     try {

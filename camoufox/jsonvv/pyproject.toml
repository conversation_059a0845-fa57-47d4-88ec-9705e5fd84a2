[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "jsonvv"
version = "0.2.2"
description = "JSON value validator"
authors = ["daijro <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/daijro/camoufox"
homepage = "https://github.com/daijro/camoufox/tree/main/pythonlib/jsonvv"
readme = "README.md"
keywords = [
    "json",
    "validator",
    "validation",
    "typing",
]

[tool.poetry.dependencies]
python = "^3.8"

[tool.poetry.scripts]
jsonvv = "jsonvv.__main__:main"

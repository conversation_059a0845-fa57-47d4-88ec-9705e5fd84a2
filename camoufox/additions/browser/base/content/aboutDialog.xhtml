<?xml version="1.0"?> <!-- -*- Mode: HTML -*- -->

# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

<?xml-stylesheet href="chrome://global/skin/global.css" type="text/css"?>
<?xml-stylesheet href="chrome://browser/content/aboutDialog.css" type="text/css"?>

<window xmlns:html="http://www.w3.org/1999/xhtml"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        id="aboutDialog"
        windowtype="Browser:About"
        onload="init(event);"
#ifdef XP_MACOSX
        inwindowmenu="false"
#else
        data-l10n-id="aboutDialog-title"
#endif
        role="dialog"
        aria-describedby="version distribution distributionId communityDesc contributeDesc trademark"
        >
#ifdef XP_MACOSX
#include macWindow.inc.xhtml
#else
  <script src="chrome://browser/content/utilityOverlay.js"/>
#endif

  <linkset>
    <html:link rel="localization" href="branding/brand.ftl"/>
    <html:link rel="localization" href="browser/aboutDialog.ftl"/>
  </linkset>

  <script src="chrome://browser/content/aboutDialog.js"/>

  <div id="grid">
    <div id="left" />
    <div id="right">
      <label id="wordmark">Camoufox</label>
      <label id="versionNumber" />
      <label id="distribution" />
      <label id="distributionId" />
      <label id="aboutText">
        Camoufox is an independent fork of Firefox for webscraping.
      </label>
      <label id="websiteLink" is="text-link" href="https://github.com/daijro/camoufox">https://github.com/daijro/camoufox</label>
    </div>
  </div>

  <keyset>
    <key keycode="VK_ESCAPE" oncommand="window.close();"/>
  </keyset>

</window>


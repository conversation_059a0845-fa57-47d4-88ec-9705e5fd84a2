# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

malformedURI2=Please check that the URL is correct and try again.
fileNotFound=Camoufox can’t find the file at %S.
fileAccessDenied=The file at %S is not readable.
dnsNotFound2=We can’t connect to the server at %S.
unknownProtocolFound=Camoufox doesn’t know how to open this address, because one of the following protocols (%S) isn’t associated with any program or is not allowed in this context.
connectionFailure=Camoufox can’t establish a connection to the server at %S.
netInterrupt=The connection to %S was interrupted while the page was loading.
netTimeout=The server at %S is taking too long to respond.
redirectLoop=Camoufox has detected that the server is redirecting the request for this address in a way that will never complete.
## LOCALIZATION NOTE (confirmRepostPrompt): In this item, don’t translate "%S"
confirmRepostPrompt=To display this page, %S must send information that will repeat any action (such as a search or order confirmation) that was performed earlier.
resendButton.label=Resend
unknownSocketType=Camoufox doesn’t know how to communicate with the server.
netReset=The connection to the server was reset while the page was loading.
notCached=This document is no longer available.
netOffline=Camoufox is currently in offline mode and can’t browse the Web.
isprinting=The document cannot change while Printing or in Print Preview.
deniedPortAccess=This address uses a network port which is normally used for purposes other than Web browsing. Camoufox has canceled the request for your protection.
proxyResolveFailure=Camoufox is configured to use a proxy server that can’t be found.
proxyConnectFailure=Camoufox is configured to use a proxy server that is refusing connections.
contentEncodingError=The page you are trying to view cannot be shown because it uses an invalid or unsupported form of compression.
unsafeContentType=The page you are trying to view cannot be shown because it is contained in a file type that may not be safe to open. Please contact the website owners to inform them of this problem.
externalProtocolTitle=External Protocol Request
externalProtocolPrompt=An external application must be launched to handle %1$S: links.\n\n\nRequested link:\n\n%2$S\n\nApplication: %3$S\n\n\nIf you were not expecting this request it may be an attempt to exploit a weakness in that other program. Cancel this request unless you are sure it is not malicious.\n
#LOCALIZATION NOTE (externalProtocolUnknown): The following string is shown if the application name can't be determined
externalProtocolUnknown=<Unknown>
externalProtocolChkMsg=Remember my choice for all links of this type.
externalProtocolLaunchBtn=Launch application
malwareBlocked=The site at %S has been reported as an attack site and has been blocked based on your security preferences.
harmfulBlocked=The site at %S has been reported as a potentially harmful site and has been blocked based on your security preferences.
unwantedBlocked=The site at %S has been reported as serving unwanted software and has been blocked based on your security preferences.
deceptiveBlocked=This web page at %S has been reported as a deceptive site and has been blocked based on your security preferences.
cspBlocked=This page has a content security policy that prevents it from being loaded in this way.
xfoBlocked=This page has an X-Frame-Options policy that prevents it from being loaded in this context.
corruptedContentErrorv2=The site at %S has experienced a network protocol violation that cannot be repaired.
## LOCALIZATION NOTE (sslv3Used) - Do not translate "%S".
sslv3Used=Camoufox cannot guarantee the safety of your data on %S because it uses SSLv3, a broken security protocol.
inadequateSecurityError=The website tried to negotiate an inadequate level of security.
blockedByPolicy=Your organization has blocked access to this page or website.
networkProtocolError=Camoufox has experienced a network protocol violation that cannot be repaired.

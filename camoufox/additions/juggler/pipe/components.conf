# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{d69ecefe-3df7-4d11-9dc7-f604edb96da2}',
        'contract_ids': ['@mozilla.org/juggler/remotedebuggingpipe;1'],
        'type': 'nsIRemoteDebuggingPipe',
        'constructor': 'mozilla::nsRemoteDebuggingPipe::GetSingleton',
        'headers': ['/juggler/pipe/nsRemoteDebuggingPipe.h'],
    },
]

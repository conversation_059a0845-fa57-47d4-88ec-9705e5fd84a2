# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

XPIDL_SOURCES += [
    'nsIScreencastService.idl',
]

XPIDL_MODULE = 'jugglerscreencast'

SOURCES += [
    'HeadlessWindowCapturer.cpp',
    'nsScreencastService.cpp',
    'ScreencastEncoder.cpp',
]

XPCOM_MANIFESTS += [
    'components.conf',
]

LOCAL_INCLUDES += [
    '/dom/media/systemservices',
    '/media/libyuv/libyuv/include',
    '/third_party/abseil-cpp',
    '/third_party/libwebrtc',
]

LOCAL_INCLUDES += [
    '/widget',
    '/widget/headless',
]

LOCAL_INCLUDES += [
    '/third_party/aom/third_party/libwebm',
]

SOURCES += [
    '/third_party/aom/third_party/libwebm/mkvmuxer/mkvmuxer.cc',
    '/third_party/aom/third_party/libwebm/mkvmuxer/mkvmuxerutil.cc',
    '/third_party/aom/third_party/libwebm/mkvmuxer/mkvwriter.cc',
    'WebMFileWriter.cpp',
]

include('/dom/media/webrtc/third_party_build/webrtc.mozbuild')
include('/ipc/chromium/chromium-config.mozbuild')

FINAL_LIBRARY = 'xul'

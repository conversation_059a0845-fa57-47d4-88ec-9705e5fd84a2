// =================================================================
// CAMOUFOX FUNCTIONALITY
// =================================================================

// Use dark theme
defaultPref("extensions.activeThemeID", "<EMAIL>");

// Allow remote
defaultPref("devtools.debugger.remote-enabled", true);
defaultPref("devtools.debugger.prompt-connection", false);

// Dont restore sessions
defaultPref("browser.sessionstore.max_resumed_crashes", 0);
defaultPref("browser.sessionstore.restore_on_demand", false);
defaultPref("browser.sessionstore.restore_tabs_lazily", false);

// Allow pasting in console
defaultPref("devtools.selfxss.count", 100);

// WebRTC intercept IP
defaultPref("media.peerconnection.ice.no_host", true);

// Use bundled fonts
defaultPref("gfx.bundled-fonts.activate", 1);

// Use the patched Skia engine
defaultPref("gfx.canvas.azure.backends", "skia");
defaultPref("gfx.content.azure.backends", "skia");

// Do not truncate pastes
defaultPref("editor.truncate_user_pastes", false);

// Camoufox prefs
defaultPref("camoufox.debugger.force_detach", true);
defaultPref("camoufox.console.logging_disabled", false);
defaultPref(
  "camoufox.uBO.assetsBootstrapLocation",
  "https://raw.githubusercontent.com/daijro/camoufox/refs/heads/main/assets/uBOAssets.json"
);

// Tweaks that undo Playwright:

// Enable content isolation (WAFs can detect this!)
defaultPref("fission.autostart", true);
defaultPref("fission.webContentIsolationStrategy", 1);

// Use dark theme by default
defaultPref("ui.systemUsesDarkTheme", 1);

// Enable PDFJS
defaultPref("pdfjs.disabled", false);


// =================================================================
// DEBLOAT
// =================================================================


// === From Peskyfox ===

// VPN
defaultPref("browser.privatebrowsing.vpnpromourl", ""); // Disable VPN promotions
defaultPref("browser.vpn_promo.enabled", false);

// Extensions
defaultPref("extensions.getAddons.showPane", false); // HIDDEN - Disable recommendations pane in about:addons
defaultPref("extensions.htmlaboutaddons.recommendations.enabled", false); // Disable recommendations in about:addons
defaultPref("browser.discovery.enabled", false); // Disable Personalized Extension Recommendations
defaultPref("browser.shell.checkDefaultBrowser", false); // Don't check if Firefox is the default browser
defaultPref("browser.newtabpage.activity-stream.asrouter.userprefs.cfr.addons", false); // Disable extension recommendations
defaultPref(
  "browser.newtabpage.activity-stream.asrouter.userprefs.cfr.features",
  false
); // Disable feature recommendations

// No places
defaultPref("browser.places.interactions.enabled", false);

// Popups
defaultPref("browser.preferences.moreFromMozilla", false); // Hide "More from Mozilla" in Settings
defaultPref("browser.aboutConfig.showWarning", false); // Disable warning when opening about:config
defaultPref("browser.startup.homepage_override.mstone", "ignore"); // Disable What's New page after updates
defaultPref("browser.aboutwelcome.enabled", false); // Disable welcome screens
defaultPref("startup.homepage_welcome_url", "");
defaultPref("startup.homepage_welcome_url.additional", "");
defaultPref("startup.homepage_override_url", "");
defaultPref("browser.messaging-system.whatsNewPanel.enabled", false); // Disable What's New toolbar icon
defaultPref("browser.tabs.tabmanager.enabled", false); // Only show tab overflow dropdown when needed
defaultPref("screenshots.browser.component.enabled", false); // Disable new screenshot tool
defaultPref("browser.device-migration.help-menu.hidden", true);

// Theme adjustments
defaultPref("toolkit.legacyUserProfileCustomizations.stylesheets", true); // Enable userChrome/userContent
defaultPref("browser.display.focus_ring_on_anything", true); // Remove focus indicator for links
defaultPref("browser.display.focus_ring_style", 0);
defaultPref("browser.display.focus_ring_width", 0);

// Cookie banners (commented out since uBlock covers this)
// defaultPref("cookiebanners.service.mode", 2); // Reject cookie banners if possible
// defaultPref("cookiebanners.service.mode.privateBrowsing", 2);

// Translations
defaultPref("browser.translations.enable", false); // Disable Firefox Translations
defaultPref("browser.translations.autoTranslate", false);

// Fullscreen
defaultPref("full-screen-api.transition-duration.enter", "0 0"); // Remove fullscreen transition
defaultPref("full-screen-api.transition-duration.leave", "0 0");
defaultPref("full-screen-api.warning.delay", -1); // Disable fullscreen warning
defaultPref("full-screen-api.warning.timeout", 0);

// URL bar
defaultPref("browser.tabs.firefox-view", false); // Disable Firefox view
defaultPref("browser.urlbar.suggest.history", false); // Minimize URL bar suggestions
defaultPref("browser.urlbar.suggest.bookmark", false);
defaultPref("browser.urlbar.suggest.clipboard", false);
defaultPref("browser.urlbar.suggest.openpage", false);
defaultPref("browser.urlbar.suggest.engines", false);
defaultPref("browser.urlbar.suggest.searches", false);
defaultPref("browser.urlbar.quickactions.enabled", false);
defaultPref("browser.urlbar.shortcuts.quickactions", false);
defaultPref("browser.urlbar.suggest.weather", false);
defaultPref("browser.urlbar.weather.ignoreVPN", false);
defaultPref("browser.urlbar.suggest.calculator", false);
defaultPref("browser.urlbar.unitConversion.enabled", false);
defaultPref("browser.urlbar.suggest.topsites", false);
defaultPref("browser.urlbar.trending.featureGate", false);
defaultPref("browser.urlbar.suggest.trending", false);
defaultPref("browser.urlbar.addons.featureGate", false); // [FF115+]
defaultPref("browser.urlbar.mdn.featureGate", false); // [FF117+] [HIDDEN PREF]
defaultPref("browser.urlbar.pocket.featureGate", false); // [FF116+] [DEFAULT: false]
defaultPref("browser.urlbar.weather.featureGate", false); // [FF108+] [DEFAULT: false]
defaultPref("browser.urlbar.clipboard.featureGate", false); // [FF118+] [DEFAULT: true FF125+]
defaultPref("browser.urlbar.yelp.featureGate", false); // [FF124+] [DEFAULT: false]
defaultPref("browser.urlbar.autoFill", false); // [DEFAULT]
defaultPref("browser.urlbar.autoFill.adaptiveHistory.enabled", false);
defaultPref("browser.urlbar.maxRichResults", 0); // Disable URL bar dropdown results

// Autoplay
defaultPref("media.autoplay.default", 0); // Allow autoplay
defaultPref("media.block-autoplay-until-in-foreground", true);
defaultPref("media.autoplay.blocking_policy", 0);

// Startup/New Tab
defaultPref("browser.startup.page", 0); // Show blank page on startup
defaultPref("browser.startup.homepage", "about:blank");
defaultPref("browser.newtabpage.enabled", false); // Disable activity stream on new tab
defaultPref("browser.newtabpage.activity-stream.discoverystream.enabled", false);
defaultPref("browser.newtabpage.activity-stream.showSearch", true); // NTP Web Search [DEFAULT]
defaultPref("browser.newtabpage.activity-stream.feeds.topsites", false); // Shortcuts
defaultPref("browser.newtabpage.activity-stream.showSponsoredTopSites", false); // Shortcuts > Sponsored shortcuts [FF83+]
defaultPref("browser.newtabpage.activity-stream.showWeather", false); // Weather [FF128+ NIGHTLY]
defaultPref("browser.newtabpage.activity-stream.system.showWeather", false); // Weather [FF128+ NIGHTLY]
defaultPref("browser.newtabpage.activity-stream.showSponsored", false); // Sponsored Stories [FF58+]
defaultPref("browser.newtabpage.activity-stream.feeds.section.highlights", false); // Recent Activity [DEFAULT]
defaultPref(
  "browser.newtabpage.activity-stream.section.highlights.includeBookmarks",
  false
);
defaultPref(
  "browser.newtabpage.activity-stream.section.highlights.includeDownloads",
  false
);
defaultPref(
  "browser.newtabpage.activity-stream.section.highlights.includePocket",
  false
);
defaultPref(
  "browser.newtabpage.activity-stream.section.highlights.includeVisited",
  false
);
defaultPref("browser.newtabpage.activity-stream.feeds.snippets", false); // [DEFAULT]
defaultPref("browser.newtabpage.activity-stream.newtabWallpapers.enabled", false); // Wallpapers
defaultPref("browser.newtabpage.activity-stream.default.sites", "");
defaultPref("browser.toolbars.bookmarks.visibility", "newtab");

// Pocket
defaultPref("extensions.pocket.enabled", false); // Disable Pocket integration
defaultPref("extensions.pocket.api", " ");
defaultPref("extensions.pocket.oAuthConsumerKey", " ");
defaultPref("extensions.pocket.site", " ");
defaultPref("extensions.pocket.showHome", false);

// Miscellaneous
defaultPref("identity.fxaccounts.enabled", false);
defaultPref("findbar.highlightAll", false);
defaultPref("layout.word_select.eat_space_to_next_word", false);
defaultPref("privacy.popups.showBrowserMessage", false);
defaultPref("browser.tabs.cardPreview.enabled", false);
defaultPref("browser.backspace_action", 2);
defaultPref("reader.parse-on-load.enabled", false);
defaultPref("ui.key.menuAccessKeyFocuses", false);
defaultPref("layout.spellcheckDefault", 0);
defaultPref("ui.SpellCheckerUnderlineStyle", 1);
defaultPref("browser.bookmarks.max_backups", 0);
defaultPref("browser.zoom.full", false);
defaultPref("print.tab_modal.enabled", false);
defaultPref("browser.tabs.tabMinWidth", 120);

// === Debloat pt.2 (from Librewolf) ===

// Forms
defaultPref("signon.rememberSignons", false);
defaultPref("signon.autofillForms", false);
defaultPref("extensions.formautofill.addresses.enabled", false);
defaultPref("extensions.formautofill.creditCards.enabled", false);
defaultPref("signon.formlessCapture.enabled", false);
// Remove shopping
defaultPref("browser.shopping.experience2023.enabled", false);
defaultPref("browser.shopping.experience2023.optedIn", 2);
defaultPref("browser.shopping.experience2023.active", false);
// Remove first launch
defaultPref("browser.uitour.enabled", false);
defaultPref("browser.uitour.url", "");
// disable telemetry in Firefox Home
defaultPref("browser.newtabpage.activity-stream.feeds.telemetry", false);
defaultPref("browser.newtabpage.activity-stream.telemetry", false);
// hide stories and sponsored content from Firefox Home
defaultPref("browser.newtabpage.activity-stream.feeds.section.topstories", false);
defaultPref(
  "browser.newtabpage.activity-stream.feeds.section.topstories.options",
  '{"hidden":true}'
);
// clean about
defaultPref("browser.contentblocking.report.lockwise.enabled", false);
defaultPref("browser.contentblocking.report.hide_vpn_banner", true);
defaultPref("browser.contentblocking.report.vpn.enabled", false);
defaultPref("browser.contentblocking.report.show_mobile_app", false);
defaultPref("browser.promo.focus.enabled", false);
defaultPref("lightweightThemes.getMoreURL", ""); // disable button to get more themes
defaultPref("browser.topsites.useRemoteSetting", false); // hide sponsored shortcuts button

// Extensions
defaultPref("extensions.webextensions.restrictedDomains", "");
defaultPref("extensions.enabledScopes", 5); // hidden
defaultPref("extensions.postDownloadThirdPartyPrompt", false);
defaultPref("extensions.quarantinedDomains.enabled", false);
defaultPref("extensions.systemAddon.update.enabled", false);
defaultPref("extensions.systemAddon.update.url", "");
defaultPref("extensions.webcompat-reporter.enabled", false);
defaultPref("extensions.webcompat-reporter.newIssueEndpoint", "");
defaultPref("privacy.history.custom", true);
defaultPref("browser.privatebrowsing.autostart", false);
defaultPref("browser.formfill.enable", false); // disable form history

// Remove all telementry
defaultPref("toolkit.telemetry.unified", false); // master switch
defaultPref("toolkit.telemetry.enabled", false); // master switch
defaultPref("toolkit.telemetry.server", "data:,");
defaultPref("toolkit.telemetry.archive.enabled", false);
defaultPref("toolkit.telemetry.newProfilePing.enabled", false);
defaultPref("toolkit.telemetry.updatePing.enabled", false);
defaultPref("toolkit.telemetry.firstShutdownPing.enabled", false);
defaultPref("toolkit.telemetry.shutdownPingSender.enabled", false);
defaultPref("toolkit.telemetry.bhrPing.enabled", false);
defaultPref("toolkit.telemetry.cachedClientID", "");
defaultPref("toolkit.telemetry.previousBuildID", "");
defaultPref("toolkit.telemetry.server_owner", "");
defaultPref("toolkit.coverage.opt-out", true); // hidden
defaultPref("toolkit.telemetry.coverage.opt-out", true); // hidden
defaultPref("toolkit.coverage.enabled", false);
defaultPref("toolkit.coverage.endpoint.base", "");
defaultPref("toolkit.crashreporter.infoURL", "");
defaultPref("datareporting.healthreport.uploadEnabled", false);
defaultPref("security.protectionspopup.recordEventTelemetry", false);
defaultPref("browser.ping-centre.telemetry", false);

// opt-out of normandy and studies
defaultPref("app.shield.optoutstudies.enabled", false);
defaultPref("network.protocol-handler.external.ms-windows-store", false); // prevent links from launching windows store

// disable crash report
defaultPref("browser.tabs.crashReporting.sendReport", false);
defaultPref("breakpad.reportURL", "");

// disable connectivity checks
defaultPref("network.connectivity-service.enabled", false);

// disable captive portal
defaultPref("network.captive-portal-service.enabled", false);
defaultPref("captivedetect.canonicalURL", "");

// =================================================================
// MEMORY & SESSION
// =================================================================

// Limit image memory (commented out to help performance)
// defaultPref("image.mem.decode_bytes_at_a_time", 16384); // default=16384; alt=65536; chunk size for calls to the image decoders
// defaultPref("media.memory_cache_max_size", 8192); // default=8192; AF=65536; alt=131072

// Disable DNS prefetching
defaultPref("network.dns.disablePrefetch", true);
defaultPref("network.dns.disablePrefetchFromHTTPS", true); // (FF127+ false)
defaultPref("network.prefetch-next", false);
defaultPref("network.predictor.enabled", false);
defaultPref("browser.preferences.defaultPerformanceSettings.enabled", false);

// Disable network seperations
defaultPref("privacy.partition.network_state", false);

// Turn off bfcache
// Source: https://github.com/Floorp-Projects/Floorp/issues/140
defaultPref("browser.sessionstore.max_tabs_undo", 0);
defaultPref("browser.sessionstore.max_windows_undo", 0);
defaultPref("browser.sessionstore.resuming_after_os_restart", false);
defaultPref("browser.sessionstore.resume_session_once", false);
defaultPref("browser.sessionstore.upgradeBackup.maxUpgradeBackups", 0);
defaultPref("browser.sessionhistory.max_entries", 0);
defaultPref("browser.sessionhistory.max_total_viewers", 0);
defaultPref("browser.fullscreen.autohide", false);

// Force disk cache
defaultPref("browser.cache.memory.enable", false); // Disable memory cache
defaultPref("browser.cache.disk.enable", true); // Enable disk cache
defaultPref("browser.cache.disk_cache_ssl", true); // Enable disk cache for SSL

// Disable offline cache (commented out to help performance)
// defaultPref("browser.cache.offline.enable", false); 
// defaultPref("browser.cache.offline.capacity", 0);
// defaultPref("browser.cache.disk.smart_size.enabled", false); // Disables cache size auto-tuning

// Release memory after closing tabs
defaultPref("memory.free_dirty_pages", true);

// Datareporting
defaultPref("datareporting.sessions.current.clean", true);

// Session storage
defaultPref("browser.sessionstore.privacy_level", 2);

// Do not reopen windows on startup
defaultPref("privacy.clearOnShutdown.openWindows", true);

// RFP
defaultPref("privacy.resistFingerprinting", false); // This will actually hurt fingerprinting.


// =================================================================
// THEMING
// =================================================================

// New tab
defaultPref("browser.newtabpage.enhanced", false);

// Bookmark bar
defaultPref("browser.toolbars.bookmarks.visibility", "never");

// Theming (from Firefox-UI-Fix)
defaultPref("userChrome.tab.connect_to_window", true); // Original, Photon
defaultPref("userChrome.tab.color_like_toolbar", true); // Original, Photon

defaultPref("userChrome.tab.lepton_like_padding", true); // Original
defaultPref("userChrome.tab.photon_like_padding", false); // Photon

defaultPref("userChrome.tab.dynamic_separator", true); // Original, Proton
defaultPref("userChrome.tab.static_separator", false); // Photon
defaultPref("userChrome.tab.static_separator.selected_accent", false); // Just option
defaultPref("userChrome.tab.bar_separator", false); // Just option

defaultPref("userChrome.tab.newtab_button_like_tab", true); // Original
defaultPref("userChrome.tab.newtab_button_smaller", false); // Photon
defaultPref("userChrome.tab.newtab_button_proton", false); // Proton

defaultPref("userChrome.icon.panel_full", true); // Original, Proton
defaultPref("userChrome.icon.panel_photon", false); // Photon

// Original Only
defaultPref("userChrome.tab.box_shadow", true);
defaultPref("userChrome.tab.bottom_rounded_corner", true);

// Photon Only
defaultPref("userChrome.tab.photon_like_contextline", false);
defaultPref("userChrome.rounding.square_tab", false);

// Theme fixes
defaultPref("userChrome.compatibility.theme", true);
defaultPref("userChrome.compatibility.os", true);

defaultPref("userChrome.theme.built_in_contrast", true);
defaultPref("userChrome.theme.system_default", true);
defaultPref("userChrome.theme.proton_color", true);
defaultPref("userChrome.theme.proton_chrome", true); // Need proton_color
defaultPref("userChrome.theme.fully_color", true); // Need proton_color
defaultPref("userChrome.theme.fully_dark", true); // Need proton_color

defaultPref("userChrome.decoration.cursor", true);
defaultPref("userChrome.decoration.field_border", true);
defaultPref("userChrome.decoration.download_panel", true);

defaultPref("userChrome.padding.tabbar_width", true);
defaultPref("userChrome.padding.tabbar_height", true);
defaultPref("userChrome.padding.toolbar_button", true);
defaultPref("userChrome.padding.navbar_width", true);
defaultPref("userChrome.padding.urlbar", true);
defaultPref("userChrome.padding.bookmarkbar", true);
defaultPref("userChrome.padding.infobar", true);
defaultPref("userChrome.padding.menu", true);
defaultPref("userChrome.padding.bookmark_menu", true);
defaultPref("userChrome.padding.global_menubar", true);
defaultPref("userChrome.padding.panel", true);
defaultPref("userChrome.padding.popup_panel", true);

defaultPref("userChrome.tab.multi_selected", true);
defaultPref("userChrome.tab.unloaded", true);
defaultPref("userChrome.tab.letters_cleary", true);
defaultPref("userChrome.tab.close_button_at_hover", true);
defaultPref("userChrome.tab.sound_hide_label", true);
defaultPref("userChrome.tab.sound_with_favicons", true);
defaultPref("userChrome.tab.container", true);
defaultPref("userChrome.tab.crashed", true);

defaultPref("userChrome.fullscreen.overlap", true);
defaultPref("userChrome.fullscreen.show_bookmarkbar", true);

defaultPref("userChrome.icon.library", true);
defaultPref("userChrome.icon.panel", true);
defaultPref("userChrome.icon.menu", true);
defaultPref("userChrome.icon.context_menu", true);
defaultPref("userChrome.icon.global_menu", true);
defaultPref("userChrome.icon.global_menubar", true);
defaultPref("userChrome.icon.1-25px_stroke", true);


// ================ [FROM PLAYWRIGHT] ================ 


// =================================================================
// THESE ARE THE PROPERTIES THAT MUST BE ENABLED FOR JUGGLER TO WORK
// =================================================================
defaultPref("dom.input_events.security.minNumTicks", 0);
defaultPref("dom.input_events.security.minTimeElapsedInMS", 0);

defaultPref("dom.iframe_lazy_loading.enabled", false);

defaultPref("datareporting.policy.dataSubmissionEnabled", false);
defaultPref("datareporting.policy.dataSubmissionPolicyAccepted", false);
defaultPref("datareporting.policy.dataSubmissionPolicyBypassNotification", true);

// -------
// CAMOUFOX: Removed pref to disable pdf.js
// -------

// This preference breaks our authentication flow.
defaultPref("network.auth.use_redirect_for_retries", false);

// Disable cross-process iframes, but not cross-process navigations.
// defaultPref("fission.webContentIsolationStrategy", 0);

// Disable BFCache in parent process.
// We also separately disable BFCache in content via docSchell property.
defaultPref("fission.bfcacheInParent", false);

// Disable first-party-based cookie partitioning.
// When it is enabled, we have to retain "thirdPartyCookie^" permissions
// in the storageState.
defaultPref("network.cookie.cookieBehavior", 4);

// Increase max number of child web processes so that new pages
// get a new process by default and we have a process isolation
// between pages from different contexts. If this becomes a performance
// issue we can povide custom '@mozilla.org/ipc/processselector;1'

// -------
// Using 16 is to balance process isolation and resource usage
// The 60000 PlayWright is using is impractical and may cause instability.
defaultPref("dom.ipc.processCount", 16);
// Thanks @D4Vinci!
/// ------

// Never reuse processes as they may keep previously overridden values
// (locale, timezone etc.).
defaultPref("dom.ipc.processPrelaunch.enabled", false);

// Isolate permissions by user context.
defaultPref("permissions.isolateBy.userContext", true);

// Allow creating files in content process - required for
// |Page.setFileInputFiles| protocol method.
defaultPref("dom.file.createInChild", true);

// Allow uploading directorys in content process.
pref("dom.filesystem.pathcheck.disabled", true);

// Do not warn when closing all open tabs
defaultPref("browser.tabs.warnOnClose", false);

// Do not warn when closing all other open tabs
defaultPref("browser.tabs.warnOnCloseOtherTabs", false);

// Do not warn when multiple tabs will be opened
defaultPref("browser.tabs.warnOnOpen", false);

// Do not warn on quitting Firefox
defaultPref("browser.warnOnQuit", false);

// Disable popup-blocker
defaultPref("dom.disable_open_during_load", false);

// Disable the ProcessHangMonitor
defaultPref("dom.ipc.reportProcessHangs", false);
defaultPref("hangmonitor.timeout", 0);

// Allow the application to have focus even it runs in the background
defaultPref("focusmanager.testmode", true);

// No ICC color correction. We need this for reproducible screenshots.
// See https://developer.mozilla.org/en/docs/Mozilla/Firefox/Releases/3.5/ICC_color_correction_in_Firefox.
defaultPref("gfx.color_management.mode", 0);
defaultPref("gfx.color_management.rendering_intent", 3);

// Always use network provider for geolocation tests so we bypass the
// macOS dialog raised by the corelocation provider
defaultPref("geo.provider.testing", true);

// =================================================================
// THESE ARE NICHE PROPERTIES THAT ARE NICE TO HAVE
// =================================================================

// We never want to have interactive screen capture picker enabled in FF build.
pref("media.getdisplaymedia.screencapturekit.enabled", false);
pref("media.getdisplaymedia.screencapturekit.picker.enabled", false);

// Enable software-backed webgl. See https://phabricator.services.mozilla.com/D164016
defaultPref("webgl.forbid-software", false);

// Disable auto-fill for credit cards and addresses.
// See https://github.com/microsoft/playwright/issues/21393
defaultPref("extensions.formautofill.creditCards.supported", "off");
defaultPref("extensions.formautofill.addresses.supported", "off");

// Allow access to system-added self-signed certificates. This aligns
// firefox behavior with other browser defaults.
defaultPref("security.enterprise_roots.enabled", true);

// Theres a security features warning that might be shown on certain Linux distributions & configurations:
// https://support.mozilla.org/en-US/kb/install-firefox-linux#w_security-features-warning
// This notification should never be shown in automation scenarios.
pref("security.sandbox.warn_unprivileged_namespaces", false);

// Avoid stalling on shutdown, after "xpcom-will-shutdown" phase.
// This at least happens when shutting down soon after launching.
// See AppShutdown.cpp for more details on shutdown phases.
defaultPref("toolkit.shutdown.fastShutdownStage", 3);

// -------
// CAMOUFOX: Removed pref to use light theme by default (make less like headless)
// -------

// Only allow the old modal dialogs. This should be removed when there is
// support for the new modal UI (see Bug 1686743).
defaultPref("prompts.contentPromptSubDialog", false);

// Do not use system colors - they are affected by themes.
defaultPref("ui.use_standins_for_native_colors", true);

// Turn off the Push service.
defaultPref("dom.push.serverURL", "");
// Prevent Remote Settings (firefox.settings.services.mozilla.com) to issue non local connections.
defaultPref("services.settings.server", "");
// Prevent location.services.mozilla.com to issue non local connections.
defaultPref("browser.region.network.url", "");
defaultPref("browser.pocket.enabled", false);
defaultPref("browser.newtabpage.activity-stream.feeds.topsites", false);
// Disable sponsored tiles from "Mozilla Tiles Service"
defaultPref("browser.newtabpage.activity-stream.showSponsoredTopSites", false);
// required to prevent non-local access to push.services.mozilla.com
defaultPref("dom.push.connection.enabled", false);
// Prevent contile.services.mozilla.com to issue non local connections.
defaultPref("browser.topsites.contile.enabled", false);
defaultPref("browser.safebrowsing.provider.mozilla.updateURL", "");
defaultPref("browser.library.activity-stream.enabled", false);
defaultPref("browser.search.geoSpecificDefaults", false);
defaultPref("browser.search.geoSpecificDefaults.url", "");
defaultPref("captivedetect.canonicalURL", "");
defaultPref("network.captive-portal-service.enabled", false);
defaultPref("network.connectivity-service.enabled", false);
defaultPref("browser.newtabpage.activity-stream.asrouter.providers.snippets", "");

// Make sure Shield doesnt hit the network.
defaultPref("app.normandy.api_url", "");
defaultPref("app.normandy.enabled", false);

// Disable updater
defaultPref("app.update.enabled", false);
// Disable Firefox old build background check
defaultPref("app.update.checkInstallTime", false);
// Disable automatically upgrading Firefox
defaultPref("app.update.disabledForTesting", true);

// make absolutely sure it is really off
defaultPref("app.update.auto", false);
defaultPref("app.update.mode", 0);
defaultPref("app.update.service.enabled", false);
// Dislabe newtabpage
defaultPref("browser.startup.homepage", "about:blank");
defaultPref("browser.startup.page", 0);
defaultPref("browser.newtabpage.enabled", false);
// Do not redirect user when a milstone upgrade of Firefox is detected
defaultPref("browser.startup.homepage_override.mstone", "ignore");

// Disable topstories
defaultPref("browser.newtabpage.activity-stream.feeds.section.topstories", false);
// DevTools JSONViewer sometimes fails to load dependencies with its require.js.
// This spams console with a lot of unpleasant errors.
// (bug 1424372)
defaultPref("devtools.jsonview.enabled", false);

// Increase the APZ content response timeout in tests to 1 minute.
// This is to accommodate the fact that test environments tends to be
// slower than production environments (with the b2g emulator being
// the slowest of them all), resulting in the production timeout value
// sometimes being exceeded and causing false-positive test failures.
//
// (bug 1176798, bug 1177018, bug 1210465)
defaultPref("apz.content_response_timeout", 60000);

// Indicate that the download panel has been shown once so that
// whichever download test runs first doesnt show the popup
// inconsistently.
defaultPref("browser.download.panel.shown", true);
// Background thumbnails in particular cause grief, and disabling
// thumbnails in general cannot hurt
defaultPref("browser.pagethumbnails.capturing_disabled", true);
// Disable safebrowsing components.
defaultPref("browser.safebrowsing.blockedURIs.enabled", false);
defaultPref("browser.safebrowsing.downloads.enabled", false);
defaultPref("browser.safebrowsing.passwords.enabled", false);
defaultPref("browser.safebrowsing.malware.enabled", false);
defaultPref("browser.safebrowsing.phishing.enabled", false);
// Disable updates to search engines.
defaultPref("browser.search.update", false);
// Do not restore the last open set of tabs if the browser has crashed
defaultPref("browser.sessionstore.resume_from_crash", false);
// Dont check for the default web browser during startup.
defaultPref("browser.shell.checkDefaultBrowser", false);

// Disable browser animations (tabs, fullscreen, sliding alerts)
defaultPref("toolkit.cosmeticAnimations.enabled", false);

// Close the window when the last tab gets closed
defaultPref("browser.tabs.closeWindowWithLastTab", true);

// Do not allow background tabs to be zombified on Android, otherwise for
// tests that open additional tabs, the test harness tab itself might get
// unloaded
defaultPref("browser.tabs.disableBackgroundZombification", false);

// Disable first run splash page on Windows 10
defaultPref("browser.usedOnWindows10.introURL", "");

// Disable the UI tour.
//
// Should be set in profile.
defaultPref("browser.uitour.enabled", false);

// Turn off search suggestions in the location bar so as not to trigger
// network connections.
defaultPref("browser.urlbar.suggest.searches", false);

// Do not show datareporting policy notifications which can
// interfere with tests
defaultPref("datareporting.healthreport.documentServerURI", "");
defaultPref("datareporting.healthreport.about.reportUrl", "");
defaultPref("datareporting.healthreport.logging.consoleEnabled", false);
defaultPref("datareporting.healthreport.service.enabled", false);
defaultPref("datareporting.healthreport.service.firstRun", false);
defaultPref("datareporting.healthreport.uploadEnabled", false);

// Automatically unload beforeunload alerts
defaultPref("dom.disable_beforeunload", false);

// Disable slow script dialogues
defaultPref("dom.max_chrome_script_run_time", 0);
defaultPref("dom.max_script_run_time", 0);

// Only load extensions from the application and user profile
// AddonManager.SCOPE_PROFILE + AddonManager.SCOPE_APPLICATION
defaultPref("extensions.autoDisableScopes", 0);
defaultPref("extensions.enabledScopes", 5);

// Disable metadata caching for installed add-ons by default
defaultPref("extensions.getAddons.cache.enabled", false);

// Disable installing any distribution extensions or add-ons.
defaultPref("extensions.installDistroAddons", false);

// Turn off extension updates so they do not bother tests
defaultPref("extensions.update.enabled", false);
defaultPref("extensions.update.notifyUser", false);

// Make sure opening about:addons will not hit the network
defaultPref("extensions.webservice.discoverURL", "");

defaultPref("extensions.screenshots.disabled", true);
defaultPref("extensions.screenshots.upload-disabled", true);

// Disable useragent updates
defaultPref("general.useragent.updates.enabled", false);

// Do not scan Wifi
defaultPref("geo.wifi.scan", false);

// Show chrome errors and warnings in the error console
defaultPref("javascript.options.showInConsole", true);

// Disable download and usage of OpenH264: and Widevine plugins
defaultPref("media.gmp-manager.updateEnabled", false);

// Do not prompt with long usernames or passwords in URLs
defaultPref("network.http.phishy-userpass-length", 255);

// Do not prompt for temporary redirects
defaultPref("network.http.prompt-temp-redirect", false);

// Disable speculative connections so they are not reported as leaking
// when they are hanging around
defaultPref("network.http.speculative-parallel-limit", 0);

// Do not automatically switch between offline and online
defaultPref("network.manage-offline-status", false);

// Make sure SNTP requests do not hit the network
defaultPref("network.sntp.pools", "");

// Disable Flash
defaultPref("plugin.state.flash", 0);

defaultPref("privacy.trackingprotection.enabled", false);

defaultPref("security.certerrors.mitm.priming.enabled", false);

// Local documents have access to all other local documents,
// including directory listings
defaultPref("security.fileuri.strict_origin_policy", false);

// Tests do not wait for the notification button security delay
defaultPref("security.notification_enable_delay", 0);

// Do not automatically fill sign-in forms with known usernames and
// passwords
defaultPref("signon.autofillForms", false);

// Disable password capture, so that tests that include forms are not
// influenced by the presence of the persistent doorhanger notification
defaultPref("signon.rememberSignons", false);

// Disable first-run welcome page
defaultPref("startup.homepage_welcome_url", "about:blank");
defaultPref("startup.homepage_welcome_url.additional", "");

// Prevent starting into safe mode after application crashes
defaultPref("toolkit.startup.max_resumed_crashes", -1);
defaultPref("toolkit.crashreporter.enabled", false);

defaultPref("toolkit.telemetry.enabled", false);
defaultPref("toolkit.telemetry.server", "");

// Disable downloading the list of blocked extensions.
defaultPref("extensions.blocklist.enabled", false);

// Force Firefox Devtools to open in a separate window.
defaultPref("devtools.toolbox.host", "window");

// Disable auto translations
defaultPref("browser.translations.enable", false);

// Disable spell check
defaultPref("layout.spellcheckDefault", 0);

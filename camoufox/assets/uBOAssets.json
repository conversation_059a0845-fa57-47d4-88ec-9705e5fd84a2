{"assets.json": {"content": "internal", "updateAfter": 13, "contentURL": "https://gitlab.com/librewolf-community/browser/source/-/raw/main/assets/uBOAssets.json"}, "public_suffix_list.dat": {"content": "internal", "updateAfter": 19, "contentURL": ["https://publicsuffix.org/list/public_suffix_list.dat", "assets/thirdparties/publicsuffix.org/list/effective_tld_names.dat"]}, "ublock-badlists": {"content": "internal", "updateAfter": 29, "contentURL": ["https://ublockorigin.github.io/uAssets/filters/badlists.txt", "assets/ublock/badlists.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/badlists.txt", "https://ublockorigin.pages.dev/filters/badlists.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/badlists.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/badlists.txt"]}, "ublock-filters": {"content": "filters", "group": "default", "parent": "uBlock filters", "title": "uBlock filters – Ads", "tags": "ads", "contentURL": ["https://ublockorigin.github.io/uAssets/filters/filters.txt", "assets/ublock/filters.min.txt", "assets/ublock/filters.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/filters.min.txt", "https://ublockorigin.pages.dev/filters/filters.min.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/filters.min.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/filters.min.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "ublock-privacy": {"content": "filters", "group": "default", "parent": "uBlock filters", "title": "uBlock filters – Privacy", "tags": "privacy", "contentURL": ["https://ublockorigin.github.io/uAssets/filters/privacy.txt", "assets/ublock/privacy.min.txt", "assets/ublock/privacy.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/privacy.min.txt", "https://ublockorigin.pages.dev/filters/privacy.min.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/privacy.min.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/privacy.min.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "ublock-unbreak": {"content": "filters", "group": "default", "parent": "uBlock filters", "title": "uBlock filters – Unbreak", "contentURL": ["https://ublockorigin.github.io/uAssets/filters/unbreak.txt", "assets/ublock/unbreak.min.txt", "assets/ublock/unbreak.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/unbreak.min.txt", "https://ublockorigin.pages.dev/filters/unbreak.min.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/unbreak.min.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/unbreak.min.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "ublock-quick-fixes": {"content": "filters", "group": "default", "parent": "uBlock filters", "title": "uBlock filters – Quick fixes", "contentURL": ["https://ublockorigin.github.io/uAssets/filters/quick-fixes.txt", "assets/ublock/quick-fixes.min.txt", "assets/ublock/quick-fixes.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/quick-fixes.min.txt", "https://ublockorigin.pages.dev/filters/quick-fixes.min.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/quick-fixes.min.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/quick-fixes.min.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "adguard-generic": {"content": "filters", "group": "ads", "off": true, "title": "<PERSON><PERSON><PERSON> <PERSON> Ad<PERSON>", "tags": "ads", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/2_without_easylist.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "adguard-mobile": {"content": "filters", "group": "ads", "off": true, "title": "AdGuard – Mobile Ads", "tags": "ads mobile", "ua": "mobile", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/11.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "easylist": {"content": "filters", "group": "ads", "title": "EasyList", "tags": "ads", "preferred": true, "contentURL": ["https://ublockorigin.github.io/uAssets/thirdparties/easylist.txt", "assets/thirdparties/easylist/easylist.txt"], "cdnURLs": ["https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist.txt", "https://ublockorigin.pages.dev/thirdparties/easylist.txt"], "supportURL": "https://easylist.to/"}, "adguard-spyware-url": {"content": "filters", "group": "privacy", "title": "AdGuard URL Tracking Protection", "tags": "privacy", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/17.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "adguard-spyware": {"content": "filters", "group": "privacy", "off": true, "title": "AdGuard Tracking Protection", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/3.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "block-lan": {"content": "filters", "group": "privacy", "off": true, "title": "Block Outsider Intrusion into LAN", "tags": "privacy security", "contentURL": "https://ublockorigin.github.io/uAssets/filters/lan-block.txt", "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/lan-block.txt", "https://ublockorigin.pages.dev/filters/lan-block.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/lan-block.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/lan-block.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "easyprivacy": {"content": "filters", "group": "privacy", "title": "EasyPrivacy", "tags": "privacy", "preferred": true, "contentURL": ["https://ublockorigin.github.io/uAssets/thirdparties/easyprivacy.txt", "assets/thirdparties/easylist/easyprivacy.txt"], "cdnURLs": ["https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easyprivacy.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easyprivacy.txt", "https://ublockorigin.pages.dev/thirdparties/easyprivacy.txt"], "supportURL": "https://easylist.to/"}, "adguard-cookies": {"content": "filters", "group": "annoyances", "group2": "cookies", "parent": "AdGuard/uBO – <PERSON><PERSON> Notices", "off": true, "title": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "tags": "annoyances cookies", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/18.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "ublock-cookies-adguard": {"content": "filters", "group": "annoyances", "group2": "cookies", "parent": "AdGuard/uBO – <PERSON><PERSON> Notices", "off": true, "title": "uBlock filters – <PERSON><PERSON>ices", "tags": "annoyances cookies", "contentURL": "https://ublockorigin.github.io/uAssets/filters/annoyances-cookies.txt", "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/annoyances-cookies.txt", "https://ublockorigin.pages.dev/filters/annoyances-cookies.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/annoyances-cookies.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/annoyances-cookies.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "fanboy-cookiemonster": {"content": "filters", "group": "annoyances", "group2": "cookies", "parent": "EasyList/uBO – <PERSON><PERSON>ices", "off": true, "title": "EasyList – <PERSON><PERSON>", "tags": "annoyances cookies", "preferred": true, "contentURL": ["https://ublockorigin.github.io/uAssets/thirdparties/easylist-cookies.txt", "https://secure.fanboy.co.nz/fanboy-cookiemonster_ubo.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/thirdparties/easylist-cookies.txt", "https://ublockorigin.pages.dev/thirdparties/easylist-cookies.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist-cookies.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist-cookies.txt", "https://secure.fanboy.co.nz/fanboy-cookiemonster_ubo.txt"], "supportURL": "https://github.com/easylist/easylist#fanboy-lists"}, "ublock-cookies-easylist": {"content": "filters", "group": "annoyances", "group2": "cookies", "parent": "EasyList/uBO – <PERSON><PERSON>ices", "off": true, "title": "uBlock filters – <PERSON><PERSON>ices", "tags": "annoyances cookies", "preferred": true, "contentURL": "https://ublockorigin.github.io/uAssets/filters/annoyances-cookies.txt", "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/annoyances-cookies.txt", "https://ublockorigin.pages.dev/filters/annoyances-cookies.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/annoyances-cookies.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/annoyances-cookies.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "adguard-social": {"content": "filters", "group": "annoyances", "group2": "social", "parent": null, "off": true, "title": "<PERSON><PERSON><PERSON> <PERSON> Social Widgets", "tags": "annoyances social", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/4.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "fanboy-social": {"content": "filters", "group": "annoyances", "group2": "social", "parent": null, "off": true, "title": "EasyList – Social Widgets", "tags": "annoyances social", "preferred": true, "contentURL": ["https://ublockorigin.github.io/uAssets/thirdparties/easylist-social.txt", "https://secure.fanboy.co.nz/fanboy-social_ubo.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/thirdparties/easylist-social.txt", "https://ublockorigin.pages.dev/thirdparties/easylist-social.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist-social.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist-social.txt", "https://secure.fanboy.co.nz/fanboy-social_ubo.txt"], "supportURL": "https://easylist.to/"}, "fanboy-thirdparty_social": {"content": "filters", "group": "annoyances", "group2": "social", "off": true, "title": "Fanboy – Anti-Facebook", "tags": "privacy", "contentURL": "https://secure.fanboy.co.nz/fanboy-antifacebook.txt", "supportURL": "https://github.com/ryanbr/fanboy-adblock/issues"}, "adguard-popup-overlays": {"content": "filters", "group": "annoyances", "parent": "<PERSON><PERSON><PERSON> <PERSON>", "off": true, "title": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> Overlays", "tags": "annoyances", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/19.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "adguard-mobile-app-banners": {"content": "filters", "group": "annoyances", "parent": "<PERSON><PERSON><PERSON> <PERSON>", "off": true, "title": "AdGuard – Mobile App Banners", "tags": "annoyances mobile", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/20.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "adguard-other-annoyances": {"content": "filters", "group": "annoyances", "parent": "<PERSON><PERSON><PERSON> <PERSON>", "off": true, "title": "<PERSON><PERSON><PERSON> – Other Annoyances", "tags": "annoyances", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/21.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "adguard-widgets": {"content": "filters", "group": "annoyances", "parent": "<PERSON><PERSON><PERSON> <PERSON>", "off": true, "title": "<PERSON><PERSON><PERSON> <PERSON> Widgets", "tags": "annoyances", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/22.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "easylist-annoyances": {"content": "filters", "group": "annoyances", "parent": "EasyList – Annoyances", "off": true, "title": "EasyList – Other Annoyances", "tags": "annoyances", "preferred": true, "contentURL": "https://ublockorigin.github.io/uAssets/thirdparties/easylist-annoyances.txt", "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/thirdparties/easylist-annoyances.txt", "https://ublockorigin.pages.dev/thirdparties/easylist-annoyances.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist-annoyances.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist-annoyances.txt"], "supportURL": "https://github.com/easylist/easylist#fanboy-lists"}, "easylist-chat": {"content": "filters", "group": "annoyances", "parent": "EasyList – Annoyances", "off": true, "title": "EasyList – <PERSON><PERSON>", "tags": "annoyances", "preferred": true, "contentURL": "https://ublockorigin.github.io/uAssets/thirdparties/easylist-chat.txt", "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/thirdparties/easylist-chat.txt", "https://ublockorigin.pages.dev/thirdparties/easylist-chat.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist-chat.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist-chat.txt"], "supportURL": "https://github.com/easylist/easylist#fanboy-lists"}, "easylist-newsletters": {"content": "filters", "group": "annoyances", "parent": "EasyList – Annoyances", "off": true, "title": "EasyList – Newsletter Notices", "tags": "annoyances", "preferred": true, "contentURL": ["https://ublockorigin.github.io/uAssets/thirdparties/easylist-newsletters.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/thirdparties/easylist-newsletters.txt", "https://ublockorigin.pages.dev/thirdparties/easylist-newsletters.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist-newsletters.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist-newsletters.txt"], "supportURL": "https://easylist.to/"}, "easylist-notifications": {"content": "filters", "group": "annoyances", "parent": "EasyList – Annoyances", "off": true, "title": "EasyList – Notifications", "tags": "annoyances", "preferred": true, "contentURL": ["https://ublockorigin.github.io/uAssets/thirdparties/easylist-notifications.txt"], "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/thirdparties/easylist-notifications.txt", "https://ublockorigin.pages.dev/thirdparties/easylist-notifications.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/thirdparties/easylist-notifications.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/thirdparties/easylist-notifications.txt"], "supportURL": "https://easylist.to/"}, "ublock-annoyances": {"content": "filters", "group": "annoyances", "off": true, "title": "uBlock filters – Annoyances", "tags": "annoyances", "contentURL": "https://ublockorigin.github.io/uAssets/filters/annoyances.txt", "cdnURLs": ["https://ublockorigin.github.io/uAssetsCDN/filters/annoyances.min.txt", "https://ublockorigin.pages.dev/filters/annoyances.min.txt", "https://cdn.jsdelivr.net/gh/uBlockOrigin/uAssetsCDN@main/filters/annoyances.min.txt", "https://cdn.statically.io/gh/uBlockOrigin/uAssetsCDN/main/filters/annoyances.min.txt"], "supportURL": "https://github.com/uBlockOrigin/uAssets"}, "dpollock-0": {"content": "filters", "group": "multipurpose", "updateAfter": 13, "off": true, "title": "<PERSON>’s hosts file", "tags": "ads privacy security", "contentURL": "https://someonewhocares.org/hosts/hosts", "supportURL": "https://someonewhocares.org/hosts/"}, "plowe-0": {"content": "filters", "group": "multipurpose", "updateAfter": 13, "title": "<PERSON>’s Ad and tracking server list", "tags": "ads privacy security", "preferred": true, "contentURL": ["https://pgl.yoyo.org/adservers/serverlist.php?hostformat=hosts&showintro=1&mimetype=plaintext", "assets/thirdparties/pgl.yoyo.org/as/serverlist.txt", "assets/thirdparties/pgl.yoyo.org/as/serverlist"], "supportURL": "https://pgl.yoyo.org/adservers/"}, "ALB-0": {"content": "filters", "group": "regions", "off": true, "title": "🇦🇱al 🇽🇰xk: Adblock List for Albania", "tags": "ads albania shqipja", "lang": "sq", "contentURL": "https://raw.githubusercontent.com/AnXh3L0/blocklist/master/albanian-easylist-addition/Albania.txt", "supportURL": "https://github.com/AnXh3L0/blocklist"}, "ara-0": {"content": "filters", "group": "regions", "off": true, "title": "🇪🇬eg 🇸🇦sa 🇲🇦ma 🇩🇿dz: Liste AR", "tags": "ads arabic اَلْعَرَبِيَّةُ‎", "lang": "ar", "contentURL": "https://easylist-downloads.adblockplus.org/Liste_AR.txt", "supportURL": "https://forums.lanik.us/viewforum.php?f=98"}, "BGR-0": {"content": "filters", "group": "regions", "off": true, "title": "🇧🇬bg: Bulgarian Adblock list", "tags": "ads bulgarian България macedonian Македонија", "lang": "bg mk", "contentURL": "https://stanev.org/abp/adblock_bg.txt", "supportURL": "https://stanev.org/abp/"}, "CHN-0": {"content": "filters", "group": "regions", "off": true, "title": "🇨🇳cn 🇹🇼tw: <PERSON><PERSON>uard <PERSON> (中文)", "tags": "ads chinese 中文", "lang": "ug zh", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/224.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters"}, "CZE-0": {"content": "filters", "group": "regions", "off": true, "title": "🇨🇿cz 🇸🇰sk: EasyList Czech and Slovak", "tags": "ads czech česká slovak slovenská", "lang": "cs sk", "contentURL": "https://raw.githubusercontent.com/tomasko126/easylistczechandslovak/master/filters.txt", "supportURL": "https://github.com/tomasko126/easylistczechandslovak"}, "DEU-0": {"content": "filters", "group": "regions", "off": true, "title": "🇩🇪de 🇨🇭ch 🇦🇹at: EasyList Germany", "tags": "ads german deutschland luxembourgish lëtzebuerg romansh", "lang": "de dsb hsb lb rm", "contentURL": ["https://easylist.to/easylistgermany/easylistgermany.txt", "https://easylist-downloads.adblockplus.org/easylistgermany.txt"], "supportURL": "https://forums.lanik.us/viewforum.php?f=90"}, "EST-0": {"content": "filters", "group": "regions", "off": true, "title": "🇪🇪ee: <PERSON><PERSON><PERSON> sa<PERSON>e kohandatud filter", "tags": "ads estonian", "lang": "et", "contentURL": "https://adblock.ee/list.php", "supportURL": "https://adblock.ee/"}, "FIN-0": {"content": "filters", "group": "regions", "off": true, "title": "🇫🇮fi: Adblock List for Finland", "tags": "ads finnish", "lang": "fi", "contentURL": "https://raw.githubusercontent.com/finnish-easylist-addition/finnish-easylist-addition/gh-pages/Finland_adb.txt", "supportURL": "https://github.com/finnish-easylist-addition/finnish-easylist-addition"}, "FRA-0": {"content": "filters", "group": "regions", "off": true, "title": "🇫🇷fr 🇨🇦ca: <PERSON><PERSON><PERSON>", "tags": "ads french", "lang": "ar br ff fr lb oc son", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/16.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters"}, "GRC-0": {"content": "filters", "group": "regions", "off": true, "title": "🇬🇷gr 🇨🇾cy: Greek AdBlock Filter", "tags": "ads greek", "lang": "el", "contentURL": "https://www.void.gr/kargig/void-gr-filters.txt", "supportURL": "https://github.com/kargig/greek-adblockplus-filter"}, "HRV-0": {"content": "filters", "group": "regions", "off": true, "title": "🇭🇷hr 🇷🇸rs: <PERSON><PERSON><PERSON>'s Serbo-Croatian filters", "tags": "ads croatian serbian bosnian", "lang": "bs hr sr", "contentURL": ["https://raw.githubusercontent.com/DandelionSprout/adfilt/master/SerboCroatianList.txt", "https://cdn.jsdelivr.net/gh/DandelionSprout/adfilt@master/SerboCroatianList.txt", "https://cdn.statically.io/gl/DandelionSprout/adfilt/master/SerboCroatianList.txt"], "supportURL": "https://github.com/DandelionSprout/adfilt#readme"}, "HUN-0": {"content": "filters", "group": "regions", "off": true, "title": "🇭🇺hu: hufilter", "tags": "ads hungarian", "lang": "hu", "contentURL": "https://cdn.jsdelivr.net/gh/hufilter/hufilter@gh-pages/hufilter-ublock.txt", "supportURL": "https://github.com/hufilter/hufilter"}, "IDN-0": {"content": "filters", "group": "regions", "off": true, "title": "🇮🇩id 🇲🇾my: ABPindo", "tags": "ads indonesian malay", "lang": "id ms", "contentURL": "https://raw.githubusercontent.com/ABPindo/indonesianadblockrules/master/subscriptions/abpindo.txt", "supportURL": "https://github.com/ABPindo/indonesianadblockrules"}, "IND-0": {"content": "filters", "group": "regions", "off": true, "title": "🇮🇳in 🇱🇰lk 🇳🇵np: IndianList", "tags": "ads assamese bengali gujarati hindi kannada malayalam marathi nepali punjabi sinhala tamil telugu", "lang": "as bn gu hi kn ml mr ne pa si ta te", "contentURL": "https://easylist-downloads.adblockplus.org/indianlist.txt", "supportURL": "https://github.com/mediumkreation/IndianList"}, "IRN-0": {"content": "filters", "group": "regions", "off": true, "title": "🇮🇷ir: Persian<PERSON><PERSON>er", "tags": "ads af ir persian pashto tajik tj", "lang": "fa ps tg", "contentURL": ["https://raw.githubusercontent.com/MasterKia/PersianBlocker/main/PersianBlocker.txt", "https://cdn.statically.io/gh/MasterKia/PersianBlocker/main/PersianBlocker.txt"], "cdnURLs": ["https://cdn.jsdelivr.net/gh/MasterKia/PersianBlocker@main/PersianBlocker.txt", "https://cdn.statically.io/gh/MasterKia/PersianBlocker/main/PersianBlocker.txt"], "supportURL": "https://github.com/MasterKia/PersianBlocker"}, "ISL-0": {"content": "filters", "group": "regions", "off": true, "title": "🇮🇸is: Icelandic ABP List", "tags": "ads icelandic", "lang": "is", "contentURL": "https://raw.githubusercontent.com/brave/adblock-lists/master/custom/is.txt", "supportURL": "https://github.com/brave/adblock-lists/issues"}, "ISR-0": {"content": "filters", "group": "regions", "off": true, "title": "🇮🇱il: EasyList Hebrew", "tags": "ads hebrew", "lang": "he", "contentURL": "https://raw.githubusercontent.com/easylist/EasyListHebrew/master/EasyListHebrew.txt", "supportURL": "https://github.com/easylist/EasyListHebrew"}, "ITA-0": {"content": "filters", "group": "regions", "off": true, "title": "🇮🇹it: EasyList Italy", "tags": "ads italian", "lang": "it lij", "contentURL": "https://easylist-downloads.adblockplus.org/easylistitaly.txt", "supportURL": "https://forums.lanik.us/viewforum.php?f=96"}, "JPN-1": {"content": "filters", "group": "regions", "off": true, "title": "🇯🇵jp: Ad<PERSON>uard <PERSON>", "tags": "ads japanese 日本語", "lang": "ja", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/7.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "KOR-1": {"content": "filters", "group": "regions", "off": true, "title": "🇰🇷kr: List-KR", "tags": "ads korean 한국어", "lang": "ko", "contentURL": "https://cdn.jsdelivr.net/gh/List-KR/List-KR@latest/filter-uBlockOrigin.txt", "supportURL": "https://github.com/List-KR/List-KR#readme"}, "LTU-0": {"content": "filters", "group": "regions", "off": true, "title": "🇱🇹lt: EasyList Lithuania", "tags": "ads lithuanian", "lang": "lt", "contentURL": "https://raw.githubusercontent.com/EasyList-Lithuania/easylist_lithuania/master/easylistlithuania.txt", "cdnURLs": ["https://cdn.jsdelivr.net/gh/EasyList-Lithuania/easylist_lithuania@master/easylistlithuania.txt", "https://cdn.statically.io/gh/EasyList-Lithuania/easylist_lithuania/master/easylistlithuania.txt"], "supportURL": "https://github.com/EasyList-Lithuania/easylist_lithuania"}, "LVA-0": {"content": "filters", "group": "regions", "off": true, "title": "🇱🇻lv: Latvian List", "tags": "ads latvian", "lang": "lv", "contentURL": "https://raw.githubusercontent.com/Latvian-List/adblock-latvian/master/lists/latvian-list.txt", "supportURL": "https://github.com/Latvian-List/adblock-latvian"}, "MKD-0": {"content": "filters", "group": "regions", "off": true, "title": "🇲🇰mk: Macedonian adBlock Filters", "tags": "ads macedonian", "lang": "mk", "contentURL": "https://raw.githubusercontent.com/DeepSpaceHarbor/Macedonian-adBlock-Filters/master/Filters", "supportURL": "https://github.com/DeepSpaceHarbor/Macedonian-adBlock-Filters"}, "NLD-0": {"content": "filters", "group": "regions", "off": true, "title": "🇳🇱nl 🇧🇪be: <PERSON><PERSON><PERSON>", "tags": "ads afrikaans be belgië frisian dutch flemish nederlands netherlands nl sr suriname za", "lang": "af fy nl", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/8.txt", "cdnURLs": null, "supportURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "NOR-0": {"content": "filters", "group": "regions", "off": true, "title": "🇳🇴no 🇩🇰dk 🇮🇸is: Dandelion Sprouts nordiske filtre", "tags": "ads norwegian danish icelandic", "lang": "nb nn no da is", "contentURL": ["https://raw.githubusercontent.com/DandelionSprout/adfilt/master/NorwegianList.txt", "https://cdn.jsdelivr.net/gh/DandelionSprout/adfilt@master/NorwegianList.txt", "https://cdn.statically.io/gl/DandelionSprout/adfilt/master/NorwegianList.txt"], "supportURL": "https://github.com/DandelionSprout/adfilt"}, "POL-0": {"content": "filters", "group": "regions", "parent": "🇵🇱pl: Oficjalne Polskie Filtry", "off": true, "title": "🇵🇱pl: Oficjalne Polskie Filtry do uBlocka Origin", "tags": "ads polish polski", "lang": "szl pl", "contentURL": "https://raw.githubusercontent.com/MajkiIT/polish-ads-filter/master/polish-adblock-filters/adblock.txt", "supportURL": "https://github.com/MajkiIT/polish-ads-filter/issues", "instructionURL": "https://github.com/MajkiIT/polish-ads-filter#polish-filters-for-adblock-ublock-origin--adguard"}, "POL-2": {"content": "filters", "group": "regions", "parent": "🇵🇱pl: Oficjalne Polskie Filtry", "off": true, "title": "🇵🇱pl: Oficjalne polskie filtry przeciwko alertom o Adblocku", "tags": "ads polish polski", "lang": "szl pl", "contentURL": "https://raw.githubusercontent.com/olegwukr/polish-privacy-filters/master/anti-adblock.txt", "supportURL": "https://github.com/olegwukr/polish-privacy-filters/issues"}, "ROU-1": {"content": "filters", "group": "regions", "off": true, "title": "🇷🇴ro 🇲🇩md: Romanian Ad (ROad) Block List Light", "tags": "ads romanian română moldavian moldovenească молдовеняскэ", "lang": "ro", "contentURL": ["https://raw.githubusercontent.com/tcptomato/ROad-Block/master/road-block-filters-light.txt"], "supportURL": "https://github.com/tcptomato/ROad-Block"}, "RUS-0": {"content": "filters", "group": "regions", "off": true, "title": "🇷🇺ru 🇺🇦ua 🇺🇿uz 🇰🇿kz: RU AdList", "tags": "ads belarusian беларуская kazakh tatar russian русский ukrainian українська uzbek", "lang": "be kk tt ru uk uz", "contentURL": "https://raw.githubusercontent.com/easylist/ruadlist/master/RuAdList-uBO.txt", "cdnURLs": ["https://cdn.jsdelivr.net/gh/dimisa-RUAdList/RUAdListCDN@main/lists/ruadlist.ubo.min.txt", "https://cdn.statically.io/gh/dimisa-RUAdList/RUAdListCDN/main/lists/ruadlist.ubo.min.txt", "https://raw.githubusercontent.com/dimisa-RUAdList/RUAdListCDN/main/lists/ruadlist.ubo.min.txt"], "supportURL": "https://forums.lanik.us/viewforum.php?f=102", "instructionURL": "https://forums.lanik.us/viewtopic.php?f=102&t=22512"}, "spa-0": {"content": "filters", "group": "regions", "off": true, "title": "🇪🇸es 🇦🇷ar 🇲🇽mx 🇨🇴co: EasyList Spanish", "tags": "ads aragonese basque catalan spanish español galician guarani", "lang": "an ast ca cak es eu gl gn trs quz", "contentURL": "https://easylist-downloads.adblockplus.org/easylistspanish.txt", "supportURL": "https://forums.lanik.us/viewforum.php?f=103"}, "spa-1": {"content": "filters", "group": "regions", "off": true, "title": "🇪🇸es 🇦🇷ar 🇧🇷br 🇵🇹pt: AdGuard Spanish/Portuguese", "tags": "ads aragonese basque catalan spanish español galician guarani portuguese português", "lang": "an ast ca cak es eu gl gn trs pt quz", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/9.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "SVN-0": {"content": "filters", "group": "regions", "off": true, "title": "🇸🇮si: Slovenian List", "tags": "ads slovenian slovenski", "lang": "sl", "contentURL": "https://raw.githubusercontent.com/betterwebleon/slovenian-list/master/filters.txt", "supportURL": "https://github.com/betterwebleon/slovenian-list"}, "SWE-1": {"content": "filters", "group": "regions", "off": true, "title": "🇸🇪se: <PERSON><PERSON><PERSON><PERSON>'s <PERSON> Filter", "tags": "ads swedish svenska", "lang": "sv", "contentURL": "https://raw.githubusercontent.com/lassekongo83/Frellwits-filter-lists/master/Frellwits-Swedish-Filter.txt", "cdnURLs": ["https://raw.githubusercontent.com/lassekongo83/Frellwits-filter-lists/swefilter/swefilter.min.txt", "https://cdn.jsdelivr.net/gh/lassekongo83/Frellwits-filter-lists@swefilter/swefilter.min.txt"], "supportURL": "https://github.com/lassekongo83/Frellwits-filter-lists"}, "THA-0": {"content": "filters", "group": "regions", "off": true, "title": "🇹🇭th: EasyList Thailand", "tags": "ads thai ไทย", "lang": "th", "contentURL": "https://raw.githubusercontent.com/easylist-thailand/easylist-thailand/master/subscription/easylist-thailand.txt", "supportURL": "https://github.com/easylist-thailand/easylist-thailand"}, "TUR-0": {"content": "filters", "group": "regions", "off": true, "title": "🇹🇷tr: <PERSON><PERSON><PERSON>", "tags": "ads turkish türkçe", "lang": "tr", "contentURL": "https://filters.adtidy.org/extension/ublock/filters/13.txt", "supportURL": "https://github.com/AdguardTeam/AdguardFilters#adguard-filters", "instructionURL": "https://adguard.com/kb/general/ad-filtering/adguard-filters/"}, "VIE-1": {"content": "filters", "group": "regions", "off": true, "title": "🇻🇳vn: ABPVN List", "tags": "ads vietnamese việt", "lang": "vi", "contentURL": "https://raw.githubusercontent.com/abpvn/abpvn/master/filter/abpvn_ublock.txt", "supportURL": "https://abpvn.com/"}, "LegitimateURLShortener": {"content": "filters", "group": "privacy", "title": "➗ Actually Legitimate URL Shortener Tool", "contentURL": "https://raw.githubusercontent.com/DandelionSprout/adfilt/master/LegitimateURLShortener.txt", "supportURL": "https://github.com/DandelionSprout/adfilt/discussions/163"}}